import React from "react";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import DiscountLogDrawer, { DiscountLogDrawerProps } from ".";
import { OrgDiscountResponse } from "../../interfaces/responseModels";

describe("<DiscountLogDrawer />", () => {
  const mockCloseModal = jest.fn();

  const getMockDrawerState = (isOpen = true) => ({
    open: isOpen,
    title: "Test Discount Log Drawer",
    data: {
      discount: {} as OrgDiscountResponse,
      discountLogs: [
        {
          username: "<PERSON><PERSON>",
          updates: ["Test Action Log Description."],
          createdAt: "2024-10-19",
          limitedData: false,
        },
      ],
    },
  });

  const renderDiscountLogDrawer = (
    props: Pick<DiscountLogDrawerProps, "drawerState" | "testId">
  ) => {
    render(
      <TreezThemeProvider>
        <DiscountLogDrawer {...props} closeDrawer={mockCloseModal} />
      </TreezThemeProvider>
    );

    const { queryAllByRole, queryByTestId } = screen;

    const discountLogDrawer = () => queryAllByRole("presentation")[0];
    const closeButton = () => queryByTestId("discount-log-drawer-close-button");

    return {
      discountLogDrawer,
      closeButton,
    };
  };

  it("renders the <DiscountLogDrawer /> component correctly", () => {
    const { discountLogDrawer } = renderDiscountLogDrawer({
      drawerState: getMockDrawerState(),
    });
    expect(discountLogDrawer()).toBeInTheDocument();
    expect(discountLogDrawer()).toHaveTextContent("Test Discount Log Drawer");
    expect(discountLogDrawer()).toHaveTextContent(
      "Test Action Log Description"
    );
  });

  it("should not render the drawer when the open prop is false", () => {
    const { discountLogDrawer } = renderDiscountLogDrawer({
      drawerState: getMockDrawerState(false),
    });
    expect(discountLogDrawer()).not.toBeDefined();
  });

  it("should call the closeDrawer function when the secondary button is clicked", async () => {
    const { closeButton } = renderDiscountLogDrawer({
      drawerState: getMockDrawerState(),
    });

    fireEvent.click(closeButton() as HTMLElement);

    await waitFor(() => {
      expect(mockCloseModal).toHaveBeenCalled();
    });
  });
});
