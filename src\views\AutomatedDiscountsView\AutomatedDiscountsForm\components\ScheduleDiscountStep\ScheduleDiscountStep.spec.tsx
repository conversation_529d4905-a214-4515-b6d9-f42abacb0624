import React from "react";
import {
  fireEvent,
  render,
  renderHook,
  screen,
  waitFor,
} from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { FormProvider, useForm } from "react-hook-form";
import ScheduleDiscountStep, { defaultSchedule } from ".";
import { RepeatType } from "../../../../../interfaces/discounts";

describe("ScheduleDiscountStep.spec.tsx", () => {
  const renderScheduleDiscountStep = () => {
    const { result } = renderHook(() =>
      useForm({
        defaultValues: defaultSchedule(),
      })
    );

    const FormProviderWrapper: React.FC<{ children: React.ReactNode }> = ({
      children,
    }) => <FormProvider {...result.current}>{children}</FormProvider>;

    const { getByTestId } = screen;

    render(
      <TreezThemeProvider>
        <FormProviderWrapper>
          <ScheduleDiscountStep />
        </FormProviderWrapper>
      </TreezThemeProvider>
    );

    return {
      scheduleDiscountStep: getByTestId("automated-schedule-discount-step"),
      scheduleEnabledSwitch: getByTestId("automated-schedule-discount-switch"),
    };
  };

  it("the step starts with a switch, other elements are hidden", () => {
    const { scheduleDiscountStep, scheduleEnabledSwitch } =
      renderScheduleDiscountStep();

    const { queryByTestId } = screen;

    expect(scheduleDiscountStep).toBeInTheDocument();
    expect(scheduleEnabledSwitch).toBeInTheDocument();

    expect(queryByTestId("start-date-picker")).toBe(null);
    expect(queryByTestId("spans-multiple-days-checkbox")).toBe(null);
  });

  it("enabling scheduling on a discount, shows start/end date pickers and all day checkbox", async () => {
    const { queryByTestId } = screen;

    const { scheduleEnabledSwitch } = renderScheduleDiscountStep();

    await waitFor(() => {
      scheduleEnabledSwitch.click();

      expect(queryByTestId("start-date-picker")).toBeInTheDocument();
      expect(queryByTestId("spans-multiple-days-checkbox")).toBeInTheDocument();
      expect(queryByTestId("all-day-checkbox")).toBeInTheDocument();
    });
  });

  it("checking spansMultipleDays showsend date picker", async () => {
    const { queryByTestId } = screen;

    const { scheduleEnabledSwitch } = renderScheduleDiscountStep();

    await waitFor(() => {
      fireEvent.click(scheduleEnabledSwitch);

      expect(queryByTestId("start-date-picker")).toBeInTheDocument();
      expect(queryByTestId("spans-multiple-days-checkbox")).toBeInTheDocument();

      fireEvent.click(queryByTestId("spans-multiple-days-checkbox")!);

      expect(queryByTestId("end-date-picker")).toBeInTheDocument();
    });
  });

  it("checking all day shows start time picker", async () => {
    const { queryByTestId } = screen;

    const { scheduleEnabledSwitch } = renderScheduleDiscountStep();

    await waitFor(() => {
      fireEvent.click(scheduleEnabledSwitch);

      expect(queryByTestId("start-date-picker")).toBeInTheDocument();
      expect(queryByTestId("all-day-checkbox")).toBeInTheDocument();

      fireEvent.click(queryByTestId("all-day-checkbox")!);

      expect(queryByTestId("start-time-picker")).toBeInTheDocument();
    });
  });

  it("unchecking all day shows end time picker", async () => {
    const { queryByTestId } = screen;

    const { scheduleEnabledSwitch } = renderScheduleDiscountStep();

    await waitFor(() => {
      fireEvent.click(scheduleEnabledSwitch);

      expect(queryByTestId("start-date-picker")).toBeInTheDocument();
      expect(queryByTestId("all-day-checkbox")).toBeInTheDocument();

      fireEvent.click(queryByTestId("all-day-checkbox")!);

      expect(queryByTestId("start-time-picker")).toBeInTheDocument();
      expect(queryByTestId("end-time-picker")).toBeInTheDocument();
    });
  });

  it("checking and unchecking spans-multiple-days hides the end date picker again", async () => {
    const { queryByTestId } = screen;

    const { scheduleEnabledSwitch } = renderScheduleDiscountStep();

    await waitFor(() => {
      fireEvent.click(scheduleEnabledSwitch);

      expect(queryByTestId("start-date-picker")).toBeInTheDocument();

      expect(queryByTestId("spans-multiple-days-checkbox")).toBeInTheDocument();

      fireEvent.click(queryByTestId("spans-multiple-days-checkbox")!);

      expect(queryByTestId("end-date-picker")).toBeInTheDocument();

      fireEvent.click(queryByTestId("spans-multiple-days-checkbox")!);

      expect(queryByTestId("end-date-picker")).toBe(null);
    });
  });

  it("checking and unchecking all day hides the start time picker again", async () => {
    const { queryByTestId } = screen;

    const { scheduleEnabledSwitch } = renderScheduleDiscountStep();

    await waitFor(() => {
      fireEvent.click(scheduleEnabledSwitch);

      expect(queryByTestId("start-date-picker")).toBeInTheDocument();
      expect(queryByTestId("all-day-checkbox")).toBeInTheDocument();

      fireEvent.click(queryByTestId("all-day-checkbox")!);

      expect(queryByTestId("start-time-picker")).toBeInTheDocument();

      fireEvent.click(queryByTestId("all-day-checkbox")!);

      expect(queryByTestId("start-time-picker")).toBe(null);
    });
  });

  it("should show custom recurrence form when recurrence custom is selected", async () => {
    const { scheduleEnabledSwitch } = renderScheduleDiscountStep();
    const { queryByTestId, getByTestId } = screen;

    await waitFor(() => {
      fireEvent.click(scheduleEnabledSwitch);
      expect(queryByTestId("start-date-picker")).toBeInTheDocument();
    });

    const recurrenceSelect = getByTestId("schedule-recurrence-type-select");

    fireEvent.change(recurrenceSelect.querySelector("input")!, {
      target: { value: RepeatType.CUSTOM },
    });

    expect(getByTestId("custom-recurrence-container")).toBeInTheDocument();
  });

  it("should hide custom recurrence form when recurrence custom is selected then unselected", async () => {
    const { scheduleEnabledSwitch } = renderScheduleDiscountStep();
    const { queryByTestId, getByTestId } = screen;

    await waitFor(() => {
      fireEvent.click(scheduleEnabledSwitch);
      expect(queryByTestId("start-date-picker")).toBeInTheDocument();
    });

    const recurrenceSelect = getByTestId("schedule-recurrence-type-select");

    fireEvent.change(recurrenceSelect.querySelector("input")!, {
      target: { value: RepeatType.CUSTOM },
    });

    expect(getByTestId("custom-recurrence-container")).toBeInTheDocument();

    fireEvent.change(recurrenceSelect.querySelector("input")!, {
      target: { value: RepeatType.DAY },
    });

    expect(queryByTestId("custom-recurrence-container")).toBeNull();
  });
});
