import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { FormProvider, useForm } from "react-hook-form";
import { ManualDiscountMethods } from "../../../../../constants/discounts";
import DiscountInfoStep, { DiscountInfoStepProps } from ".";
import { ManualDiscountFormData } from "../../../../../interfaces/discounts";

describe("<DiscountInfoStep />", () => {
  const renderDiscountInfoStep = (
    discount: Partial<ManualDiscountFormData>,
    props?: Pick<
      DiscountInfoStepProps,
      | "isEditMode"
      | "isEditConfirmModal"
      | "closeEditConfirmModal"
      | "handleEditConfirmModal"
      | "couponFeatureFlag"
      | "priceAtFeatureFlag"
    >
  ) => {
    const { getByTestId, queryAllByTestId } = screen;

    const closeEditConfirmModal = props?.closeEditConfirmModal || jest.fn();
    const handleEditConfirmModal = props?.handleEditConfirmModal || jest.fn();
    const couponFeatureFlag = props?.couponFeatureFlag || true;
    const priceAtFeatureFlag = props?.priceAtFeatureFlag || true;
    const isEditMode = props?.isEditMode || false;
    const isEditConfirmModal = props?.isEditConfirmModal || false;

    const Wrapper: React.FC = () => {
      const defaultValues = {
        title: discount.title || "",
        displayTitle: discount.displayTitle || "",
        method: discount.method || "",
        amount: discount.amount || "",
        isItem: discount.isItem || false,
        isAdjustment: discount.isAdjustment || false,
        isActive: discount.isActive || true,
        requirePin: discount.requirePin || false,
        requireReason: discount.requireReason || false,
        requireCoupon: discount.requireCoupon || false,
        storeCustomizations: [],
        couponFormModel: discount.couponFormModel || {
          coupons: [],
        },
      };
      const methods = useForm<ManualDiscountFormData>({
        defaultValues,
        mode: "all",
      });

      const {
        control,
        formState: { errors },
        setValue,
        getValues,
        trigger,
        watch,
      } = methods;

      return (
        <FormProvider {...methods}>
          <DiscountInfoStep
            trigger={trigger}
            control={control}
            errors={errors}
            setValue={setValue}
            getValues={getValues}
            watch={watch}
            closeEditConfirmModal={closeEditConfirmModal}
            handleEditConfirmModal={handleEditConfirmModal}
            couponFeatureFlag={couponFeatureFlag}
            priceAtFeatureFlag={priceAtFeatureFlag}
            isEditMode={isEditMode}
            isEditConfirmModal={isEditConfirmModal}
          />
        </FormProvider>
      );
    };

    render(
      <TreezThemeProvider>
        <Wrapper />
      </TreezThemeProvider>
    );

    const discountTitleInput = getByTestId("discount-title-input");
    const couponsFormContainer = () =>
      queryAllByTestId("add-coupons-form-container");

    const discountRequirementBox = getByTestId("discount-requirement-box");
    const discountTypesRadios = screen
      .getByTestId("discount-types-radio-group")
      .querySelectorAll(".MuiRadio-root input");

    return {
      discountTitleInput,
      couponsFormContainer,
      discountRequirementBox,
      discountTypesRadios,
    };
  };

  describe("coupons", () => {
    it("should render the accordion coupon form with code", () => {
      const discountName = "DISCOUNT1";
      const { discountTitleInput } = renderDiscountInfoStep({
        title: discountName,
      });

      const input = discountTitleInput.querySelector(
        "input"
      ) as HTMLInputElement;
      expect(input.value).toBe(discountName);
    });

    it("should hide coupon section when line item option is selected", async () => {
      const { couponsFormContainer } = renderDiscountInfoStep({ isItem: true });

      await waitFor(() => {
        expect(couponsFormContainer()).toHaveLength(0);
      });
    });

    it("should show coupon section when cart option is selected", async () => {
      const { couponsFormContainer } = renderDiscountInfoStep({
        isItem: false,
      });

      await waitFor(() => {
        expect(couponsFormContainer()).toBeDefined();
      });
    });
  });

  it("should display discount type requirements box", async () => {
    const { discountRequirementBox } = renderDiscountInfoStep({});

    await waitFor(() => {
      expect(discountRequirementBox).toBeInTheDocument();
    });
  });

  it("should disable cart discount type when method is price adjustment", async () => {
    const { discountTypesRadios } = renderDiscountInfoStep({
      method: ManualDiscountMethods.PRICE_AT,
    });

    await waitFor(() => {
      expect(discountTypesRadios[0]).toBeDisabled();
    });
  });
});
