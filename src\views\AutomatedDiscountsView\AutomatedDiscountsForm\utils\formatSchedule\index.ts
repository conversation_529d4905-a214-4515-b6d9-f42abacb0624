import { Schedule } from "../../../../../interfaces/discounts";
import { ScheduleReqBody } from "../../../../../interfaces/requestModels";
import { getDateLocaleString, formatDateToTime } from "../../../../../utils";

export const formatSchedule = (schedule: Schedule): ScheduleReqBody => ({
  ...schedule,
  startDate: schedule.startDate && getDateLocaleString(schedule.startDate),
  endDate: schedule.endDate && getDateLocaleString(schedule.endDate),
  startTime: schedule.startTime && formatDateToTime(schedule.startTime),
  endTime: schedule.endTime && formatDateToTime(schedule.endTime),
  customEndDate:
    schedule.customEndDate && getDateLocaleString(schedule.customEndDate),
});
