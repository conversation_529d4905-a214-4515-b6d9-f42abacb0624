import React, {
  ReactNode,
  createContext,
  useContext,
  useMemo,
  useState,
} from "react";
import { Snackbar } from "@treez-inc/component-library";
import {
  SnackbarContextProps,
  TSnackbarState,
} from "../../interfaces/snackbar";

const SnackbarContext = createContext<SnackbarContextProps>({
  snackbarState: { message: "", severity: undefined },
  openSnackbar: () => {},
});

export const useSnackbar = () => useContext(SnackbarContext);

const SnackbarProvider = ({ children }: { children: ReactNode }) => {
  const [open, setOpen] = useState(false);

  const defaultSnackbarState: TSnackbarState = {
    message: "",
    severity: undefined,
  };

  const [snackbarState, setSnackbarState] = useState(defaultSnackbarState);

  const openSnackbar = (propsToUpdate: TSnackbarState) => {
    setSnackbarState((prevSnackbarState) => ({
      ...prevSnackbarState,
      ...propsToUpdate,
    }));
    setOpen(true);
  };

  const providerValue = useMemo<SnackbarContextProps>(
    () => ({ snackbarState, openSnackbar }),
    []
  );

  return (
    <SnackbarContext.Provider value={providerValue}>
      {children}
      <Snackbar
        open={open}
        onClose={() => setOpen(false)}
        testId="snackbar"
        {...snackbarState}
      />
    </SnackbarContext.Provider>
  );
};

export default SnackbarProvider;
