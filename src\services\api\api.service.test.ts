import ApiService from "./apiService";

const api = require("./apiInterceptor").default;

jest.mock("./apiInterceptor");

api.mockImplementation(() => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
}));

const validTokens = {
  accessToken: "accessToken",
  expiresIn: 300,
  refreshToken: "refreshToken",
  idToken: "idToken",
};
const clearTokens = () => {};

describe("api service", () => {
  it("should perform get request with given parameters ", async () => {
    const getTokens = () => validTokens;
    const headers = {
      headers: {
        Authorization: "Bearer accessToken",
      },
    };
    const apiService = new ApiService(getTokens, clearTokens);
    await apiService.get("mockurl");
    expect(apiService.api.get).toBeCalledWith("mockurl", headers);
    expect(apiService.api.get).toBeCalledTimes(1);
  });

  it("should perform post request with given parameters ", async () => {
    const getTokens = () => validTokens;
    const headers = {
      headers: {
        Authorization: "Bearer accessToken",
      },
    };
    const apiService = new ApiService(getTokens, clearTokens);
    await apiService.post("mockurl", {});
    expect(apiService.api.post).toBeCalledWith("mockurl", {}, headers);
    expect(apiService.api.post).toBeCalledTimes(1);
  });

  it("should perform put request with given parameters ", async () => {
    const getTokens = () => validTokens;
    const headers = {
      headers: {
        Authorization: "Bearer accessToken",
      },
    };
    const apiService = new ApiService(getTokens, clearTokens);
    await apiService.put("mockurl", {});
    expect(apiService.api.put).toBeCalledWith("mockurl", {}, headers);
    expect(apiService.api.put).toBeCalledTimes(1);
  });

  it("should perform delete request with given parameters ", async () => {
    const getTokens = () => validTokens;
    const headers = {
      headers: {
        Authorization: "Bearer accessToken",
      },
    };
    const apiService = new ApiService(getTokens, clearTokens);
    await apiService.delete("mockurl", {});
    expect(apiService.api.delete).toBeCalledWith("mockurl", {
      data: {},
      ...headers,
    });
    expect(apiService.api.delete).toBeCalledTimes(1);
  });
});
