import React from "react";
import { Box, Skeleton, styled } from "@mui/material";
import { Panel, convertPxToRem } from "@treez-inc/component-library";

export const StyledCollectionItem = styled("div")({
  // panel
  "> div": {
    padding: `${convertPxToRem(16)} ${convertPxToRem(24)}`,
    marginTop: convertPxToRem(6),
    marginBottom: convertPxToRem(6),
    marginRight: convertPxToRem(6),
  },
});

export const ProductCollectionItemSkeleton = React.memo(
  ({ length = 12 }: { length?: number }) => (
    <>
      {Array(length)
        .fill(0)
        .map((value, index) => (
          // Disabled as each skeleton element is identical
          // eslint-disable-next-line react/no-array-index-key
          <StyledCollectionItem key={index}>
            <Panel>
              <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
                <Skeleton variant="rounded" width={20} height={20} />
                <Skeleton
                  variant="text"
                  width={150 + Math.floor(Math.random() * 20)}
                  sx={{ typography: "largeText" }}
                />
              </Box>
            </Panel>
          </StyledCollectionItem>
        ))}
    </>
  ),
  (prevProps, newProps) => prevProps.length === newProps.length
);
