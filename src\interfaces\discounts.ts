import {
  BogoDiscountMethods,
  BundleDiscountMethods,
  BundlePurchaseRequirement,
  CustomerEvents,
  FulfillmentTypes,
  LicenseTypes,
} from "../constants/discounts";

import { CouponFormModel } from "./couponFormModel";

export interface OrgDiscount {
  id?: string;
  title: string;
  displayTitle?: string | null;
  description?: string | null;
  amount: string;
  method: string;
  isActive: boolean;
  isManual: boolean;
  storeCustomizations: StoreCustomization[];
  imageUrl?: string;
  organizationId: string;
}

export interface AutomatedDiscountFormData extends OrgDiscount {
  isStackable: boolean;
  conditions: DiscountConditionFormData;
  customerGroups: CustomerGroup[];
  collections: ProductCollection[];
  collectionsRequired: ProductCollection[];
  schedule: Schedule | null;
  displayChannels: DisplayChannels;
}

export interface ManualDiscountFormData extends OrgDiscount {
  isItem: boolean;
  isAdjustment: boolean;
  requirePin: boolean;
  requireReason: boolean;
  requireCoupon: boolean;
  couponFormModel: CouponFormModel;
  manualConditions: ManualDiscountConditionFormData;
}

export interface DisplayChannels {
  sellTreez: boolean;
  ecommerce: boolean;
  customerFacing: boolean;
}

export interface DiscountConditionFormData {
  customerCapEnabled: boolean;
  customerCapValue: number | null;
  customerLimitEnabled: boolean;
  customerLimitValue: number | null;
  purchaseMinimumEnabled: boolean;
  purchaseMinimumValue: number | null;
  purchaseMinimumType: string | null;
  customerEventEnabled: boolean;
  customerEvents: CustomerEvent | null;
  customerGroupsEnabled: boolean;
  itemLimitEnabled: boolean;
  itemLimitValue: number | null;
  fulfillmentTypesEnabled: boolean;
  fulfillmentTypes: FulfillmentTypesMap | null;
  customerLicenseTypeEnabled: boolean;
  customerLicenseType: LicenseTypes | null;
  bogoConditions: BogoConditions | null;
  bundleConditions: BundleConditions | null;
  packageAgeEnabled: boolean;
}

export interface ManualDiscountConditionFormData {
  customerCapValue: number | null;
  purchaseMinimumValue: number | null;
  purchaseMinimumType: string | null;
  itemLimitValue: number | null;
  customerCapEnabled: boolean;
  purchaseMinimumEnabled: boolean;
  itemLimitEnabled: boolean;
}

export enum RepeatType {
  DO_NOT = "DO_NOT",
  DAY = "DAY",
  WEEK = "WEEK",
  /** example: first friday of each month */
  MONTH = "MONTH",
  /** example: 5th day of Each Month */
  MONTH_DAY = "MONTH_DAY",
  ANNUAL = "ANNUAL",
  /** Every weekday */
  WEEK_DAY = "WEEK_DAY",
  CUSTOM = "CUSTOM",
}

export interface Schedule {
  id?: string;
  startDate: Date;
  startTime: Date | null;
  endDate: Date | null;
  endTime: Date | null;
  allDay: boolean;
  spansMultipleDays: boolean;
  repeatType: RepeatType;
  customRepeatIntervalCount: number | null;
  customRepeatEvery: CustomRepeatEvery | null;
  customRepeatDaysOfWeek: DaysOfWeek | null;
  customEndType: CustomEndType | null;
  customEndDate: Date | null;
  customEndRepeatCount: number | null;
}

export enum CustomRepeatEvery {
  DAY = "DAY",
  WEEK = "WEEK",
  MONTH = "MONTH",
  YEAR = "YEAR",
}

export enum CustomEndType {
  NEVER = "NEVER",
  DATE = "DATE",
  REPEAT_COUNT = "REPEAT_COUNT",
}

export enum DaysOfWeekValues {
  MON = "MON",
  TUE = "TUE",
  WED = "WED",
  THU = "THU",
  FRI = "FRI",
  SAT = "SAT",
  SUN = "SUN",
}

export interface DaysOfWeek {
  MON: boolean;
  TUE: boolean;
  WED: boolean;
  THU: boolean;
  FRI: boolean;
  SAT: boolean;
  SUN: boolean;
}

export interface ProductCollection {
  id: string;
  orgDiscountId: string | null;
  productCollectionId: string;
  productCollectionName: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface CustomerEvent {
  eventName: CustomerEvents | null;
  eventValue?: number | null;
}

export type FulfillmentTypesMap = {
  [key in FulfillmentTypes]: boolean;
};

export interface StoreCustomization {
  id?: string;
  createdAt?: Date;
  entityId: string;
  entityName: string;
  amount?: string | null;
  requireReason: boolean | null;
  requirePin: boolean | null;
  isActive: boolean | null;
}

export interface BogoConditions {
  buyCount: number;
  getCount: number;
  discountUnit: BogoDiscountMethods;
}

export interface BundleConditions {
  buyCount: number;
  discountUnit: BundleDiscountMethods;
  purchaseRequirement: BundlePurchaseRequirement;
  threshold: boolean;
}

export interface CustomerGroup {
  id: string;
  tagId: string;
  tagName: string;
  label?: string;
  value?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}
