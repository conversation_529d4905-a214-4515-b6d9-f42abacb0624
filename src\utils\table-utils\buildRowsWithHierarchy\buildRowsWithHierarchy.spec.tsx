import { buildRowsWithHierarchy } from ".";
import { testAutomatedDiscountsResponse } from "../../../test/fixtures";

describe("buildRowsWithHierarchy", () => {
  it("should correctly build a hierarchy of discount rows with parent and child relationships", () => {
    const result = buildRowsWithHierarchy(testAutomatedDiscountsResponse);

    const expectedHierarchy = [
      ["disc-discount1"],
      ["disc-discount1", "store-disc1-store1"],
      ["disc-discount1", "store-disc1-store2"],
      ["disc-discount2"],
      ["disc-discount2", "store-disc2-store1"],
    ];

    const resultHierarchy = result.map((row) => row.hierarchy);
    expect(resultHierarchy).toEqual(expectedHierarchy);
  });

  it("should correctly set entityName as the first store when there is only one store customization and null when there's multiple", () => {
    const result = buildRowsWithHierarchy(testAutomatedDiscountsResponse);

    const expectedEntityNames = [
      undefined,
      "Store 1",
      "Store 2",
      "Store 1",
      "Store 1",
    ];

    const resultEntityNames = result.map((row) => row.entityName);
    expect(resultEntityNames).toEqual(expectedEntityNames);
  });
});
