export interface Tokens {
  accessToken: string;
  expiresIn: number;
  refreshToken: string;
  idToken: string;
}

export type ClearTokens = () => void;

type PermissionsResponse = {
  id: string;
  entityContexts: { id: string; permissions: string[] }[] | [];
  permissions: string[] | [];
};
export interface FrameworkProps {
  // TODO: return value from getTokens could be null, should update to handle null gracefully in future
  getTokens: () => Tokens;
  clearTokens: ClearTokens;
  refreshTokens: () => Promise<Tokens | null>;
  redirectToLogin: () => void;
  getPermissions: () => Promise<PermissionsResponse>;
}
