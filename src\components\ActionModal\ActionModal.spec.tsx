import React from "react";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import ActionModal, { ActionModalProps } from ".";

describe("<ActionModal />", () => {
  const mockCloseModal = jest.fn();
  const mockSubmit = jest.fn();

  const getMockModalState = (isOpen = true) => ({
    open: isOpen,
    title: "Test Action Modal",
    content: "This is a test action modal",
    submit: mockSubmit,
  });

  const renderActionModal = (
    props: Pick<ActionModalProps, "modalState" | "testId">
  ) => {
    render(
      <TreezThemeProvider>
        <ActionModal
          {...props}
          closeModal={mockCloseModal}
          primaryLabel="Confirm"
          secondaryLabel="Cancel"
        />
      </TreezThemeProvider>
    );

    const { queryByTestId, queryAllByRole } = screen;

    const actionModal = () => queryAllByRole("presentation")[0];
    const primaryButton = () => queryByTestId("action-modal-primary-button");
    const secondaryButton = () =>
      queryByTestId("action-modal-secondary-button");

    return {
      actionModal,
      primaryButton,
      secondaryButton,
    };
  };

  it("renders the <ActionModal /> component correctly", () => {
    const { actionModal, primaryButton, secondaryButton } = renderActionModal({
      modalState: getMockModalState(),
    });
    expect(actionModal()).toBeInTheDocument();
    expect(actionModal()).toHaveTextContent("Test Action Modal");
    expect(actionModal()).toHaveTextContent("This is a test action modal");
    expect(primaryButton()).toHaveTextContent("Confirm");
    expect(secondaryButton()).toHaveTextContent("Cancel");
  });

  it("should not render the modal when the open prop is false", () => {
    const { actionModal } = renderActionModal({
      modalState: getMockModalState(false),
    });
    expect(actionModal()).not.toBeDefined();
  });

  describe("primary button", () => {
    it("should call the submit function when clicked", async () => {
      const { primaryButton } = renderActionModal({
        modalState: getMockModalState(),
      });

      fireEvent.click(primaryButton() as HTMLElement);

      await waitFor(() => {
        expect(mockSubmit).toHaveBeenCalled();
      });
    });

    it("should call the closeModal function when the primary button is clicked", async () => {
      const { primaryButton } = renderActionModal({
        modalState: getMockModalState(),
      });

      fireEvent.click(primaryButton() as HTMLElement);

      await waitFor(() => {
        expect(mockCloseModal).toHaveBeenCalled();
      });
    });
  });

  it("should call the closeModal function when the secondary button is clicked", async () => {
    const { secondaryButton } = renderActionModal({
      modalState: getMockModalState(),
    });

    fireEvent.click(secondaryButton() as HTMLElement);

    await waitFor(() => {
      expect(mockCloseModal).toHaveBeenCalled();
    });
  });
});
