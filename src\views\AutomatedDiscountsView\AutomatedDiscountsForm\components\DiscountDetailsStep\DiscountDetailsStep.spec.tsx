import React, { ReactNode } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { act, render, screen, waitFor } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import {
  AUTOMATED_DISCOUNT_METHODS,
  defaultAutomatedDiscountFormValues as defaultValues,
} from "../../../../../constants/discountForm";
import DiscountDetailsStep from ".";
import ApiService from "../../../../../services/api/apiService";

const testData = {
  tokens: {
    accessToken: "accessToken",
    expiresIn: 300,
    refreshToken: "refreshToken",
    idToken: "idToken",
  },
};
const clearTokens = () => {};
const apiService = new ApiService(() => testData.tokens, clearTokens);

describe("DiscountDetailsStep", () => {
  const renderDiscountDetailsStep = () => {
    const FormProviderWrapper: React.FC<{ children: ReactNode }> = ({
      children,
    }) => {
      const methods = useForm({ defaultValues });
      return <FormProvider {...methods}>{children}</FormProvider>;
    };

    render(
      <TreezThemeProvider>
        <FormProviderWrapper>
          <DiscountDetailsStep api={apiService} />
        </FormProviderWrapper>
      </TreezThemeProvider>
    );

    const { getByTestId } = screen;

    const discountDetailsPanel = getByTestId(
      "automated-discount-details-panel"
    );
    const discountMethodPanel = getByTestId("discount-method-panel");
    const discountTitleInput = getByTestId("discount-title-input");
    const discountDisplayTitleInput = getByTestId("discount-display-title-input");
    const discountDescriptionInput = getByTestId("discount-description-input");
    const imageUploader = getByTestId("image-file-upload-component");
    const stackableDiscountCheckbox = getByTestId(
      "stackable-discount-checkbox"
    );
    const discountMethodSelect = getByTestId("discount-method-select");
    const discountAmountInput = getByTestId("discount-amount-input");

    return {
      discountDetailsPanel,
      discountMethodPanel,
      discountTitleInput,
      stackableDiscountCheckbox,
      discountMethodSelect,
      discountAmountInput,
      discountDisplayTitleInput,
      discountDescriptionInput,
      imageUploader,
    };
  };

  describe("automated discount details panel", () => {
    it("renders", () => {
      const { discountDetailsPanel, discountTitleInput, discountDisplayTitleInput, discountDescriptionInput, imageUploader } =
        renderDiscountDetailsStep();

      expect(discountDetailsPanel).toBeInTheDocument();
      expect(discountDetailsPanel).toHaveTextContent(
        /Automated Discount Details/
      );
      expect(discountTitleInput).toBeInTheDocument();
      expect(discountTitleInput).toHaveTextContent(/Discount Title/);
      expect(discountDisplayTitleInput).toBeInTheDocument();
      expect(discountDisplayTitleInput).toHaveTextContent(/Display Title/);
      expect(discountDescriptionInput).toBeInTheDocument();
      expect(discountDescriptionInput).toHaveTextContent(/Description/);
      expect(imageUploader).toBeInTheDocument();
    });
  });

  it("renders the discount method panel", () => {
    const {
      discountMethodPanel,
      discountMethodSelect,
      discountAmountInput,
      stackableDiscountCheckbox,
    } = renderDiscountDetailsStep();

    expect(discountMethodPanel).toBeInTheDocument();
    expect(discountMethodPanel).toHaveTextContent(/Discount Method/);
    expect(
      screen.queryByText(
        "Select how the discount will work, such as a percentage off, a fixed amount, or a buy-one-get-one (BOGO) deal. This defines how the discount affects product prices."
      )
    ).toBeInTheDocument();

    expect(stackableDiscountCheckbox).toBeInTheDocument();
    expect(stackableDiscountCheckbox).toHaveTextContent(/Stackable discount/);
    expect(discountMethodSelect).toBeInTheDocument();
    expect(discountAmountInput).toBeInTheDocument();
    expect(discountAmountInput).toHaveTextContent(/Amount/);
  });

  it("renders all the discount method options", () => {
    const { discountMethodSelect } = renderDiscountDetailsStep();

    expect(discountMethodSelect).toBeInTheDocument();

    act(() => {
      discountMethodSelect.click();
    });

    waitFor(() => {
      expect(
        screen.getByText(AUTOMATED_DISCOUNT_METHODS.DOLLAR)
      ).toBeInTheDocument();
      expect(
        screen.getByText(AUTOMATED_DISCOUNT_METHODS.PERCENT)
      ).toBeInTheDocument();
      expect(
        screen.getByText(AUTOMATED_DISCOUNT_METHODS.COST_PLUS)
      ).toBeInTheDocument();
    });
  });
});
