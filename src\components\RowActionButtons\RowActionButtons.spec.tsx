import React from "react";
import { render, screen } from "@testing-library/react";
import { GridTreeNodeWithRender } from "@mui/x-data-grid-pro";
import { MemoryRouter } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { TreezThemeProvider } from "@treez-inc/component-library";
import iconAriaLabels from "@treez-inc/component-library/dist/components/Icon/icon-library/icon-aria-labels";
import RowActionButtons, { RowActionButtonsProps } from ".";
import {
  testActiveAutomatedDiscountRow,
  testAutomatedDiscountsResponse,
} from "../../test/fixtures";
import { buildRowsWithHierarchy } from "../../utils";

jest.mock("../../hooks/useTreeDataGroupingCell", () => ({
  __esModule: true,
  default: () => ({
    hasChildren: true,
    isExpanded: false,
    handleClick: jest.fn(),
  }),
}));

describe("<RowActionButtons />", () => {
  const mockOpenDiscountModal = jest.fn();
  const mockOpenDiscountLog = jest.fn();

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  const mockRowNode: GridTreeNodeWithRender = {
    id: "1",
    type: "group",
    depth: 0,
    children: [],
    groupingKey: "",
    groupingField: "",
    isAutoGenerated: false,
    childrenFromPath: {},
    parent: "",
  };

  const renderRowActionButtons = (
    props: Partial<RowActionButtonsProps> = {}
  ) => {
    render(
      <MemoryRouter>
        <QueryClientProvider client={queryClient}>
          <TreezThemeProvider>
            <RowActionButtons
              id="1"
              field="testField"
              row={testActiveAutomatedDiscountRow}
              rowNode={mockRowNode}
              openDiscountModal={mockOpenDiscountModal}
              openDiscountLog={mockOpenDiscountLog}
              {...props}
            />
          </TreezThemeProvider>
        </QueryClientProvider>
      </MemoryRouter>
    );

    const { getByTestId, queryByTestId } = screen;

    const rowActionButtons = getByTestId("row-action-buttons-1");
    const actionMenuButton = rowActionButtons.querySelector("button");
    const expandIconButton = () => queryByTestId("expand-row-button-1");

    return {
      rowActionButtons,
      actionMenuButton,
      expandIconButton,
    };
  };

  it("should render <RowActionButtons />", () => {
    const rowsWithHierarchy = buildRowsWithHierarchy(
      testAutomatedDiscountsResponse
    );
    const parentRow = rowsWithHierarchy.find((row) => !row.parentId);
    const { rowActionButtons } = renderRowActionButtons({ row: parentRow });

    expect(rowActionButtons).toBeInTheDocument();
  });

  it("should render the icon button for the action menu", () => {
    const { actionMenuButton } = renderRowActionButtons();
    const actionMenuButtonIcon = actionMenuButton!
      .querySelector("span")!
      .querySelector("span");

    expect(actionMenuButton).toBeInTheDocument();
    expect(actionMenuButtonIcon).toBeInTheDocument();
    expect(actionMenuButtonIcon).toHaveAccessibleName(iconAriaLabels.More);
  });

  it("should render the expand button for the action menu", () => {
    const { expandIconButton } = renderRowActionButtons();
    expect(expandIconButton()).toBeInTheDocument();
    const expandIconButtonIcon = expandIconButton()!
      .querySelector("span")!
      .querySelector("span");

    expect(expandIconButtonIcon).toBeInTheDocument();
    expect(expandIconButtonIcon).toHaveAccessibleName(
      iconAriaLabels.ChevronRight
    );
  });

  it("should not render any buttons in the action menu of a child row ", () => {
    const childNode: GridTreeNodeWithRender = {
      id: "2",
      type: "leaf",
      depth: 1,
      groupingKey: "",
      parent: "1",
    };

    const rowsWithHierarchy = buildRowsWithHierarchy(
      testAutomatedDiscountsResponse
    );
    const childRow = rowsWithHierarchy.find((row) => row.parentId);
    const { rowActionButtons, actionMenuButton, expandIconButton } =
      renderRowActionButtons({
        row: childRow,
        rowNode: childNode,
      });

    expect(rowActionButtons).toBeInTheDocument();
    expect(actionMenuButton).toBeNull();
    expect(expandIconButton()).toBeNull();
  });
});
