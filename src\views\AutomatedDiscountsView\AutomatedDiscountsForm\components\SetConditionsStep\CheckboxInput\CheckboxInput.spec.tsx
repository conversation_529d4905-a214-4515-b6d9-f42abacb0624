import React from "react";
import { fireEvent, render, renderHook, screen, waitFor } from "@testing-library/react";
import { FieldValues, FormProvider, useForm } from "react-hook-form";
import { TreezThemeProvider } from "@treez-inc/component-library";
import CheckboxInput, { CheckboxOption } from ".";

describe("CheckboxInput", () => {
  const options: CheckboxOption[] = [
    { label: "Option 1", value: "option1" },
    { label: "Option 2", value: "option2" },
    { label: "Option 3", value: "option3" },
  ];

  const renderCheckboxInput = (testId = "test-options") => {
    const { result } = renderHook(() =>
      useForm({ defaultValues: { testCheckboxInput: {} } as FieldValues })
    );

    const FormProviderWrapper: React.FC<{ children: React.ReactNode }> = ({
      children,
    }) => <FormProvider {...result.current}>{children}</FormProvider>;

    render(
      <TreezThemeProvider>
        <FormProviderWrapper>
          <CheckboxInput
            name="testCheckboxInput"
            control={result.current.control}
            options={options}
            testId={testId}
          />
        </FormProviderWrapper>
      </TreezThemeProvider>
    );

    const { getByTestId, getAllByTestId } = screen;

    const checkboxInputGroup = getByTestId(`${testId}-checkbox-input`);
    const checkboxOptions = getAllByTestId(`${testId}-checkbox-option`);

    return { checkboxInputGroup, checkboxOptions, result };
  };

  it("should render the checkbox group with the correct number of options", () => {
    const { checkboxInputGroup, checkboxOptions } = renderCheckboxInput();

    expect(checkboxInputGroup).toBeInTheDocument();
    expect(checkboxOptions).toHaveLength(options.length);
  });

  it("should check the checkbox when clicked", () => {
    const { checkboxOptions, result } = renderCheckboxInput();

    const checkboxOption = checkboxOptions[0];
    const checkboxInput = checkboxOption.querySelector("input");

    expect(checkboxInput).not.toBeChecked();

    fireEvent.click(checkboxOption);

    expect(checkboxInput).toBeChecked();

    const inputValue = result.current.getValues("testCheckboxInput");
    expect(inputValue.option1).toBeTruthy();
  });

  it("should uncheck the checkbox when clicked after being initially checked", () => {
    const { checkboxOptions, result } = renderCheckboxInput();

    const checkboxOption = checkboxOptions[0];
    const checkboxInput = checkboxOption.querySelector("input");

    fireEvent.click(checkboxOption);

    expect(checkboxInput).toBeChecked();

    fireEvent.click(checkboxOption);

    expect(checkboxInput).not.toBeChecked();

    const inputValue = result.current.getValues("testCheckboxInput");
    expect(inputValue.option1).toBeFalsy();
  });

  it("should render the error message when no fulfillment type is selected", async () => {
    const { checkboxOptions, result } = renderCheckboxInput();
  
    const checkboxOption = checkboxOptions[0];
    const checkboxInput = checkboxOption.querySelector("input");
  
    fireEvent.click(checkboxOption);
    expect(checkboxInput).toBeChecked();
  
    fireEvent.click(checkboxOption);
    expect(checkboxInput).not.toBeChecked();
  
    await waitFor(async () => {
      const isValid = await result.current.trigger();
      expect(isValid).toBe(false);
    });
  
    const errorMessage = await screen.findByTestId("select-one-fulfillment-type-error");
  
    expect(errorMessage).toBeInTheDocument();
  });
});
