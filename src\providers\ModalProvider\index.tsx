import React, {
  ReactNode,
  createContext,
  useContext,
  useState,
  useMemo,
} from "react";

interface ModalStateProps {
  open: boolean;
  title: string;
  content: string;
  submit?: () => void;
}

interface ModalContextProps {
  modalState: ModalStateProps;
  openModal: (state: ModalStateProps) => void;
  closeModal: () => void;
}

interface ModalProviderProps {
  children: ReactNode;
}

const defaultState: ModalStateProps = { open: false, title: "", content: "" };
const ModalContext = createContext<ModalContextProps>({
  modalState: defaultState,
  openModal: () => {},
  closeModal: () => {},
});

export const useModal = () => useContext(ModalContext);

const ModalProvider = ({ children }: ModalProviderProps) => {
  const [modalState, setModalState] = useState<ModalStateProps>(defaultState);

  const openModal = (state: ModalStateProps) => {
    setModalState({ ...defaultState, ...state, open: true });
  };

  const closeModal = () => {
    setModalState((prevModalState) => ({ ...prevModalState, open: false }));
  };

  const value = useMemo(
    () => ({
      modalState,
      openModal,
      closeModal,
    }),
    [modalState]
  );

  return (
    <ModalContext.Provider value={value}>{children}</ModalContext.Provider>
  );
};
export default ModalProvider;
