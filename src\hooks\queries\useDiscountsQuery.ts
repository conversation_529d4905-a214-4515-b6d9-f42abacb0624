import { useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { listDiscountUrl } from "../../services/apiEndPoints";
import { OrgDiscountResponse } from "../../interfaces/responseModels";
import { getSortedDiscounts } from "../../utils";
import ApiService from "../../services/api/apiService";
import { DISCOUNT_FILTER_FIELDS } from "../../constants/discountTable";
import { KeyValueFilter } from "../../interfaces/table";

export interface UseOrgDiscountsQueryOptions {
  filters?: KeyValueFilter;
  isManual: boolean;
}

const useOrgDiscountsQuery = (
  api: ApiService,
  options: UseOrgDiscountsQueryOptions
) =>
  useQuery({
    queryKey: ["discounts"],
    queryFn: async () => {
      const result = await api.get(listDiscountUrl);
      return result.data;
    },
    select: useCallback(
      (data: OrgDiscountResponse[]) => {
        const { filters, isManual } = options;

        let filteredDiscounts = getSortedDiscounts(data, isManual);

        const storesToFilter =
          filters && filters[DISCOUNT_FILTER_FIELDS.STORES];

        // Filter workflow will work only if user chooses at least one of the values, or else it will show all
        if (Array.isArray(storesToFilter) && storesToFilter.length) {
          filteredDiscounts = filteredDiscounts.filter(
            (discount) =>
              discount.storeCustomizations.findIndex((store) =>
                (storesToFilter as string[]).includes(store.entityId)
              ) >= 0
          );
        }

        return filteredDiscounts;
      },
      [options.filters]
    ),
  });

export default useOrgDiscountsQuery;
