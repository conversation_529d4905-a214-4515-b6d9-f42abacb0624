import React, { useState } from "react";
import Box from "@mui/material/Box";
import {
  CircularProgress,
  ImageCropUploadButton,
} from "@treez-inc/component-library";
import {
  Stage,
  UploadFileResult,
  ValidateFileResult,
} from "@treez-inc/component-library/dist/components/FileUpload/types";
import { ContentType, ObjectType } from "@treez-inc/file-management";
import { STAGE } from "../../constants";
import { useSnackbar } from "../../providers/SnackbarProvider";
import ApiService from "../../services/api/apiService";

interface ImageUploaderProps {
  api: ApiService;
  objectId: string;
  objectType: ObjectType;
  label: string;
  onNewImageCreated: (imageUrl: string) => void;
}

const getStage = (): Stage => {
  switch (STAGE) {
    case "sandbox":
      return Stage.SANDBOX;
    case "dev":
      return Stage.DEV;
    case "prod":
      return Stage.PROD;
    default:
      return Stage.LOCAL;
  }
};

const ImageUploader = ({
  api,
  objectId,
  objectType,
  label,
  onNewImageCreated,
}: ImageUploaderProps) => {
  const { openSnackbar } = useSnackbar();
  const [isImgUploading, setIsImgUploading] = useState(false);

  const accessToken = api.getTokens()?.accessToken;

  const onFileValidated = (result: ValidateFileResult) => {
    if (!result.isValid) {
      openSnackbar({
        message: result.validationErrors[0],
        severity: "warning",
        iconName: "Warning",
      });
    }
  };

  const onFilesChanged = () => {
    setIsImgUploading(true);
  };

  const onUploadFinished = (result: UploadFileResult) => {
    if (!result.isSuccess) {
      openSnackbar({
        message: `${result.error}`,
        severity: "warning",
        iconName: "Warning",
      });
    } else if (result.isSuccess) {
      openSnackbar({
        message: `Image has been uploaded successfully`,
        severity: "info",
        iconName: "Success",
      });

      // calculate order

      const imgId = result?.fileId as string;

      onNewImageCreated(imgId);
    }
    setIsImgUploading(false);
  };

  return (
    <>
      {isImgUploading && (
        <Box>
          <CircularProgress />
        </Box>
      )}
      {!isImgUploading && (
        <ImageCropUploadButton
          authToken={accessToken}
          contentTypes={[ContentType.PNG, ContentType.JPEG, ContentType.JPG]}
          label={label}
          maxFileSizeInMegaBytes={10}
          objectId={objectId}
          objectType={objectType}
          onFileValidated={onFileValidated}
          onUploadFinished={onUploadFinished}
          onFilesChanged={onFilesChanged}
          stage={getStage()}
          showMaxFileSize
          aspect={3 / 2}
          testId="image-file-upload-component"
        />
      )}
    </>
  );
};

export default ImageUploader;
