## [1.71.7] - 2025-07-08
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!192

### Description
Description goes here

### Added

* Add Customer Limit condition to Automatic Discounts and update Item Limit condition to Redemption Limit.

### Close Issues

Closes #292

### Personal
Co-Authors
Co-authored-by: <PERSON><PERSON><PERSON> <<EMAIL>>

Merged By
<PERSON><PERSON>h<PERSON>hur<PERSON> <<EMAIL>>

Approvers
Approved-by: <PERSON> <<EMAIL>>
Approved-by: <PERSON><PERSON> <<EMAIL>>


## [1.71.6] - 2025-06-27
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!199

### Description
Description goes here

### Added

* Added Store and User Filter to Automated Discounts
* Added User Filter to Manual Discounts

### Close Issues

Closes #297 https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/301

### Personal
Co-Authors

Merged By
<PERSON><PERSON>h<PERSON> Athuru <<EMAIL>>

Approvers
Approved-by: <PERSON> <<EMAIL>>
Approved-by: <PERSON> Le <<EMAIL>>


## [1.71.5] - 2025-06-20
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!206

### Description
Description goes here

### Added

* Push latest to prod

### Close Issues

### Personal
Co-Authors

Merged By
Prudhvi Athuru <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Dan Hill <<EMAIL>>


## [1.71.4] - 2025-06-13
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!203

### Description
Description goes here

### Added

* Push latest to prod

### Close Issues

### Personal
Co-Authors

Merged By
Prudhvi Athuru <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Lucas Giraldelli <<EMAIL>>


## [1.71.3] - 2025-06-12
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!197

### Description
Description goes here

### Changed

* Rendering of Select Store step based on the number of stores.

### Close Issues

Closes #303

### Personal
Co-Authors

Merged By
Prudhvi Athuru <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Kenji Mukai <<EMAIL>>


## [1.71.2] - 2025-06-06
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!200

### Description
fix single store og recreating discounts


### Fixed
* Store customizations being recreated.


### Close Issues

### Personal
Co-Authors
Co-authored-by: PrudhviAthuru <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Prudhvi Athuru <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.71.1] - 2025-06-03
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!195

### Description
Description goes here

### Added

* Show custom error message when Product Collections return 403 in Discount Management

### Close Issues

Closes #299

### Personal
Co-Authors

Merged By
Prudhvi Athuru <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.71.0] - 2025-05-29
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!190

### Description
MFE - Discount Description, Display Title, and Display Channel

### Added
* Display title field
* Description field
* Display Channels options

### Close Issues


Closes #285

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Dan Hill <<EMAIL>>


## [1.70.1] - 2025-05-02
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!196

### Description
Add validation on fulfillment type condition

### Added
* New validation when no selecting any fulfillment type

### Close Issues


Closes #302

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Dan Hill <<EMAIL>>


## [1.70.0] - 2025-04-10
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!191

### Description
Description goes here

### Added

* New Stepper to add conditions in manual discounts form

### Close Issues

Closes #289

### Personal
Co-Authors
Co-authored-by: “Kenji <<EMAIL>>
Co-authored-by: Manu George <<EMAIL>>

Merged By
Prudhvi Athuru <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Aleksej Ziukov <<EMAIL>>


## [1.69.1] - 2025-03-11
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!193

### Description
rename require coupon


### Changed
* Require coupon to Hide Discount Button in POS

### Close Issues


Closes #294

### Personal
Co-Authors
Co-authored-by: PrudhviAthuru <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Prudhvi Athuru <<EMAIL>>
Approved-by: Wade Hastings <<EMAIL>>


## [1.69.0] - 2025-01-16
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!188

### Description
Discount Image Uploader

### Added
* Discount Image Uploader.

### Close Issues


Closes #286

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Wade Hastings <<EMAIL>>
Approved-by: Prudhvi Athuru <<EMAIL>>


## [1.68.1] - 2024-11-25
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!189

### Description
show warning message for limited history data

### Added
* Warning icon next to the logs when there is limited data.

### Close Issues


Closes #288

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Dan Hill <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.68.0] - 2024-11-19
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!185

### Description
History Log Panel - Automated Discounts

### Added
* Discount Log Drawer component.


### Close Issues


Closes #273

### Personal
Co-Authors
Co-authored-by: Raissa Bergamini <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Dan Hill <<EMAIL>>


## [1.67.4] - 2024-11-12
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!187

### Description
Fix deactivating discount is removing entities

### Added
* Included collectionsRequired and schedule object in the build request discount method.


### Close Issues


Closes #140

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.67.3] - 2024-11-05
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!186

### Description
Fix open handles in unit tests

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Raghul R Nair <<EMAIL>>


## [1.67.2] - 2024-08-01
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!182

### Description
fix format amount in discount table

### Fixed
* Amount format in discount table

### Close Issues


Closes #279

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Prudhvi Athuru <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.67.1] - 2024-07-18
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!180

### Description
MSO price adjustment

### Added
* PRICE_AT discount method in manual discount form.

### Close Issues


Closes #266

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>


## [1.67.0] - 2024-06-05
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!177

### Description
### Closed Issue

#240 

### Added

Add package date type input, add packaged at option, update some text, add tests

## MR Requirements

- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Wade Hastings <<EMAIL>>

Approvers
Approved-by: Dan Hill <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.66.0] - 2024-05-31
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!176

### Description

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/226

### Added
Ability to navigate using stepper


## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Dan Hill <<EMAIL>>


## [1.65.0] - 2024-05-31
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!174

### Description
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/251
### Closed Issues

### Changed

Put primary sort on selected vs non selected collections, then a secondary alphabetical sort


## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Wade Hastings <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.64.0] - 2024-05-29
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!175

### Description
<!-- Add high-level description here -->
### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/263


### Added
Link straight to specific collection via icon

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Prudhvi Athuru <<EMAIL>>


## [1.63.0] - 2024-05-16
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!173

### Description
Add Last Updated to Automated Discounts List View and Manual Discounts List View

### Closed Issues

https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/249

### Changed

column order / columns present


## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Dan Hill <<EMAIL>>
Approved-by: Joey Sterling <<EMAIL>>


## [1.62.5] - 2024-05-16
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!172

### Description

### Closed Issues

https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/261
### Fixed
No longer reactivating when editing a manual discounts and hitting done editing

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Prudhvi Athuru <<EMAIL>>


## [1.62.4] - 2024-05-15
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!171

### Description
### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/259

### Fixed
dynamic link instead of hardcoded to collections

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [ ] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Wade Hastings <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.62.3] - 2024-05-09
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!168

### Description
Added new validation for current saved collections. This was causing a bug when clicking quickly through steps would show the modal incorrectly.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/256

### Added
* validation for not triggering error state on fetch/refetch status
* custom test for not displaying the modal

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Kelvin Chinchilla <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kenji Mukai <<EMAIL>>


## [1.62.2] - 2024-05-06
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!170

### Description
Description goes here


### Fixed
* Test failing on main but succeeding on MR and vice versa

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Wade Hastings <<EMAIL>>


## [1.62.1] - 2024-04-29
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!166

### Description

### Closed Issues

https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/248
### Added

### Changed
validation to take into account time range when spans multiple days is not toggled

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.62.0] - 2024-04-24
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!167

### Description
Correctly set the existing discount's type of cart or line item in the form when editing a discount

### Closed Issues
Closes #245 

### Changed
- Update to use controlled RadioButton to correctly set value when editing manual discount
- Get Tags API call updated to use new structure, and mocked API response added

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.61.2] - 2024-04-23
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!164

### Description
Disabling customer groups should send null

### Changed
* Disabling customer groups should send null


### Close Issues


Closes #247

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.61.1] - 2024-04-17
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!163

### Description

### Closed Issues
#253 


### Fixed
Mapping from backend to TCL day of week format fixed in both directions. 


## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.61.0] - 2024-04-16
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!158

### Description
Displays the discount start date and time to Automated Discounts Table

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/223

### Added
* Custom format for displaying startDate + startTime


## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: “Kenji <<EMAIL>>

Merged By
Kelvin Chinchilla <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Kenji Mukai <<EMAIL>>


## [1.60.0] - 2024-04-12
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!160

### Description
Adds a path in the create/edit automated discount flow to create a new product collection, and a button to refresh the list manually. Skeleton elements now indicate if the list is in the process of refreshing.

### Closed Issues
Closes #234 

### Added
- Button to refresh product collection list
- Link to Add a new Product Collection, which opens a new tab on the product collections page
- API instance, can be imported to any component to remove necessity of prop drilling

### Changed
- When loading, product collections list now shows skeleton elements to indicate loading state

### Fixed

### Deleted
- Unused API hooks

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Wade Hastings <<EMAIL>>


## [1.59.0] - 2024-04-12
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!161

### Description
Fix issue around parsing for schedule that was triggering re-renders on automated discounts flow.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/242

### Changed
* memoization for parsing values on `methods`


## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [ ] ~~You added a demo video or a screenshot for any visual changes to a comment on the MR~~
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [ ] ~~You reviewed your own MR and left comments where needed to provide context for reviewers~~
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Kelvin Chinchilla <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.58.0] - 2024-04-10
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!159

### Description


### Added
* UI for MVP package age condition


### Close Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/231

Closes #231

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.57.0] - 2024-04-10
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!149

### Description
Adds the necessary bindings for displaying and saving the customer groups elements in the `AutoComplete` component for the conditions section.

### Close Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/213

### Added
* API query for org tag group tags displayed as `customerGroups`

### Changed
* Validations for autocomplete

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [ ] ~~You added a demo video or a screenshot for any visual changes to a comment on the MR~~
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Wade Hastings <<EMAIL>>

Merged By
Kelvin Chinchilla <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.56.0] - 2024-04-09
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!157

### Description
Automated Discounts - BE Integration for Schedule

### Added
* Schedule in request body for create/update APIs.


### Close Issues


Closes #237

### Personal
Co-Authors
Co-authored-by: Kelvin <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.55.0] - 2024-04-08
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!156

### Description
### Closed Issues

https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/241

### Added

new field in automated discount POST request, to support following otherwise breaking api change 

## MR Requirements

- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [~] Your changes meet all of the Acceptance Criteria listed in the user story
- [ ] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Hope Le <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.54.1] - 2024-04-05
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!153

### Description

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/236


### Added
* Tooltip describing spans multiple days

### Fixed
* End time now shows whenever all day is unchecked, instead of based on a combination of allDay and spans multiple days
* Placeholder/spacer is added when end date is not shown, such that end time does not automatically expand to full width and stays on the right.
* Tests Adjusted
## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.54.0] - 2024-04-05
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!154

### Description

Closes #235 

### Changed
* Text for the scheduling toggle and following description


## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [~] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Hope Le <<EMAIL>>


## [1.53.0] - 2024-04-04
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!155

### Description
### Changed
* install TCL 6.0.0
* all numerical inputs are now using type="number", but handling their own validation, since MFE will treat this has type="text" with additional attributes
* adjust tests as needed
* small description change on purchase minimum

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Wade Hastings <<EMAIL>>


## [1.52.1] - 2024-04-03
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!152

### Description
Cleaned up test files and components to reduce test times, number of warnings/error logs appearing during test runs

### Fixed
- Warnings with controlled component values
- Removed test error messages from testing output
- Updated tests to use userEvents instead of fireEvents
- Use latest version of react-testing-library react-hooks

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [~] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kenji Mukai <<EMAIL>>


## [1.52.0] - 2024-04-02
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!150

### Description
<!-- Add high-level description here -->
### Closed Issues

https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/217


## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.51.2] - 2024-04-01
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!151

### Description

### Closed Issues

https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/233

### Fixed

ability to spam the create button and result in multiple requests to backend


## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.51.1] - 2024-04-01
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!142

### Description
### Closed Issues
#229 

### Fixed
- BOGO related properties are now removed from the request when editing BOGO discounts into other methods
- Updated typescript types for request body to proper types

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.51.0] - 2024-03-29
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!148

### Description

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/216

### Added
Custom Recurrence UI

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Dan Hill <<EMAIL>>
Approved-by: Wade Hastings <<EMAIL>>


## [1.50.0] - 2024-03-28
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!147

### Description
Updates the product collections retrieval to use React Query, which should reduce any errors involving product collections retrieval race conditions that was causing the edit flow to sometimes load without collections, and reduce load times within the same session when editing multiple automated discounts.

### Closed Issues
Closes #227 

### Changed
- Product collections retrieval refactored to use react-query, for performance improvements

### Fixed
- Resolve issue when Product Collection assigned to an automated discount has no name

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [~] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.49.0] - 2024-03-27
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!145

### Description
Additional commit to trigger the tag job and fix a lint error. The following information is regarding the previous commit that was missing the tag prefix.


### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/220


### Added
Dropdown to select recurrence type
all static or dynamic options for that dropdown other than CUSTOM

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Kenji Mukai <<EMAIL>>


## [1.48.0] - 2024-03-26
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!141

### Description
Adds Product Collections to the automated discounts table, along with additional small improvements

### Closed Issues
Closes #224 

### Added
- Product Collections column, using chip to show one or more assigned collections for a discount
- Tooltips to Store and Collections chips, listing all stores or collections assigned to a given discount on hover
- Tooltip to truncated discount Titles in the table, to show full name when column is too small

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [ ] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [ ] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Wade Hastings <<EMAIL>>


## [1.47.1] - 2024-03-26
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!143

### Description
### Removed
* reverts changes unintentionally pushed to main

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Jess D <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.47.0] - 2024-03-15
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!138

### Description
Adds the BOGO method to the automated discounts creation and edit. Users can select the number of items required to trigger the BOGO discount, the limit of how many items are discounted, and the method of discount for the discounted items. For this current iteration of the feature, the same product collection(s) is used for triggering the BOGO discount, and the items that are discounted. A future feature enhancement will allow separate product collections to be selected for each.

### Closed Issues

Closes #109 

### Added
- BOGO method in the discount method selection
- BOGO related selections in the Product Collection step

### Changed
- Updated formatting for the Product Collections step layout

## MR Requirements

- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Stacey Sugiono <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Wade Hastings <<EMAIL>>


## [1.46.1] - 2024-03-13
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!139

### Description
Fix issue where deactivating or activating would clear any data that is not returned in the list view.


### Changed
* added a GET by id call before using PUT on activate and deactivate

### Fixed
* collections, conditions, etc will now not be cleared when activating or deactivating


### Close Issues

Closes #222

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [ ] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Stacey Sugiono <<EMAIL>>


## [1.46.0] - 2024-03-13
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!137

### Description
Add Customer Group condition to UI. This is not connected to API data yet and does not save

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/169

### Added
- Customer Group to the dropdown select list
- unit tests for Customer Group
- Customer Group panel 

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.45.0] - 2024-03-04
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!132

### Description
Setup Cypress to the discounts repo

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/187

### Added
- Cypress
- sample cypress test
- post_deploy_dev pipeline stage for cypress tests

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Raissa Bergamini <<EMAIL>>

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.44.1] - 2024-03-01
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!135

### Description
### Added
- onClose function for confirmation modal on automated discounts

### Fixed
- Quick edit Confirmation modal not closing when click X on Automated discounts

### Closes
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/212

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Stacey Sugiono <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.44.0] - 2024-03-01
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!134

### Description
Resolve issue where expanded rows in either the Manual or Automated discounts tables would return to their retracted state when scrolling away then back to the expanded parent row.

### Closed Issues

Closes #205 

### Changed
- Updated expanded/collapsed logic to use internal DataGrid state instead of useState

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Stacey Sugiono <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.43.0] - 2024-03-01
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!133

### Description


### Changed
* Revalidate amount when editing method, instead of clearing amount.
* Fixes amount getting cleared when reloading DiscountDetail step 
* Associated Tests


### Close Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/210

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.42.0] - 2024-03-01
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!131

### Description
### Closed Issues
Closes #192 

### Added
- Add Discount button is disabled if the user does not have the write discount permission
- Save changes buttons in the edit discount flows are disabled if the user does not have the write discount permission

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Stacey Sugiono <<EMAIL>>


## [1.41.0] - 2024-02-27
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!129

### Description
The user should see error messages that are more descriptive, and less generic. The automated discounts error message on form submission and permissions error should show from axios error response

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/209

### Added
- Created the `truncateSnackbarMessage` util function since we don't know how long an error from the BE could be and Snackbars have a max character limit of 60.

### Changed
- Show axios error response in `useGet` hook
- Network error default error messages have been changed to fit the snackbar character limit

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.40.0] - 2024-02-26
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!128

### Description
Fixes issue where, when no results are returned when retrieving the list of discounts, the loader on the table would stay active, never showing a state with "No rows". Loading has been shifted to individual component loading instead of a full page load. While loading:
- Table is in a loading state until a response is returned from the API
- Count of discounts is a Skeleton component (flashing loading state) until results are retrieved

### Closed Issues

Closes #204 

### Changed
- Loading page shows a loading state on the table
- Total discounts count has a loading state
- Loading spinner changed to only be active during edit/create discount loading

### Fixed
- Issue where editing the first item in the table would not properly update after changes

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.39.0] - 2024-02-23
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!127

### Description
When editing an existing discounts, users are able to save changes while in the middle of the edit flow so they don't need to click through all of the steps to save.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/189

### Added
- 'Done Editing' button added to the Footer of Automated and Manual discounts
- "Confirm your edit" modal for automated discounts
- unit tests

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.38.5] - 2024-02-23
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!125

### Description
set isActive to null on storeCustomizations, so that discounts can be deactivated successfully

### Fixed
* Deactivating an auto discount does not reflect in SellTreez - because storeCustomizations isActive is set to true

### Close Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/206

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Jeremy Culler <<EMAIL>>
Co-authored-by: Kelvin Chinchilla <<EMAIL>>

Merged By
Rick Branning <<EMAIL>>

Approvers
Approved-by: Rick Branning <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.38.4] - 2024-02-23
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!126

### Description
Adds a loading spinner to the Edit Discounts page while retrieving the discounts details.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/199

### Added
* Validation for showing up the loading spinner when retrieving discount details in edit mode

### Changed
* Changed the zIndex value from the Linear Stepper component since it's with value `1400`. This caused that one of the icons would be rendered on top of the loading overlay for automated discounts. Should we update this on TCL?

### Fixed
* Bug when loading spinner disappeared when fetching wasn't completely done.


## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Kelvin Chinchilla <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.38.3] - 2024-02-21
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!120

### Description
When editing a manual discount a second time without refreshing the page, the form fields would be filled in with the previously cached discount details for the discount. This change now checks if there is new data being fetch, and waits for the fetch to complete before filling the form fields with the data.

### Closed Issues
Closes #196 

### Fixed
- Editing a manual discount now waits for any updates to the discount data to be retrieved before filling the form fields

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [~] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Stacey Sugiono <<EMAIL>>


## [1.38.2] - 2024-02-20
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!124

### Description
Removing an unused info icon on automated discount title

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/193

### Deleted
- info icon on discount title

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.38.1] - 2024-02-20
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!121

### Description
Reset page number whenever searching so you are not stuck on a page without the results you are looking for. 

### Fixed

Selecting a page and then searching would yield unnexpected/incomplete results

### Close Issues

https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/198




## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [ ] You added unit tests to cover your changes Note: Tests were not added as search is managed by TCL component/MUI-X component
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Stacey Sugiono <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.38.0] - 2024-02-20
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!115

### Description
Refactor Coupons to use TCL Panel and Accordion components, fix key prop error

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/147

### Changed
- Update TCL to v5.15.0
- Use TCL Panel component instead of custom styles in CouponForm
- Renamed AccordionCouponForm to CouponPanel
- Updated tests to use coupon panel name
- Use TCL Accordion component instead of custom styles in CouponPanel

### Fixed
- key prop error on the CouponPanel (previously the AccordionCouponForm)
- Delete button to be size small to match Figma designs

### Deleted
- AccordionCouponHeader and styles file

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.37.0] - 2024-02-20
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!118

### Description
Adds the product collections view step for the automated discounts creation/edit

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/102

### Added
- Main API binding for listing the product collections on useAutomatedDiscountForm with custom hook
- Custom Product Collection View Components
- Type interfaces for response and request on discount
- Modal prompt when product collections are not selected at editing/adding an org discount


## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Kelvin <<EMAIL>>
Co-authored-by: Jess Desrochers <<EMAIL>>

Merged By
Wade Hastings <<EMAIL>>

Approvers
Approved-by: Wade Hastings <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.36.0] - 2024-02-16
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!116

### Description
When creating or updating Manual or Automated discounts, we were fetching the entire list of discounts when navigated back to the Discounts page. This could result in the newly created or updated discount not yet being present in the response, and requiring a refresh of the page to see the discount you just created.

This new branch utilizes the Tanstack Query package to make our API calls. This provides a few benefits:
- The data is cached: when navigating between Manual and Automated discounts (which both make the request to get all discounts, then filter the response to show only Manual or Automated), the request is made in the background, and compared against the data in the cache. If there is no difference between the retrieved data and the cache, the data is not updated and there are no re-renders, and therefore to the end user the experience is seamless, only updating if it determines the data is different.
- When creating or editing discounts, the updated or created discount object that is returned in the response from the POST and PUT requests are optimistically added to the existing list of discounts, so the entire Discounts page does not need to be reloaded after creation/edit. 

Other updates in this MR include:
- The Create/Edit windows that open are overlaid on-top of the Discounts table, preventing additional renders of the table and actually maintaining filters or search properties on the table after you've gone through or exited the Create and Edit flows (Searched for all "Wade" discounts and edited "Wade 3"? When you're done your "Wade" search will still be present with the updated data)

### Closed Issues

Closes #188 

### Changed
- When loading the list of discounts, the data is now cached and will only update if there has been a change in the data retrieved on subsequent requests
- When creating a discount, the discount is now shown within the list of discounts after a successful creation
- When modifying a discount, the discount's updated details are now show within the list of discounts after successful update
- After completing or cancelling a discount create/edit flow, the user is returned to the list of discounts with any filters or searches preserved

### Fixed
- Updated incorrect types and interfaces values to match response from API
- Automated discounts Stackable checkbox now properly reflects current value when editing a discount
- Purchase Minimum condition value fixed to remove unnecessary decimal values from DB data when editing a discount 
- Modal updated so that when closing, the contents do not disappear before the modal has fully disappeared
- Fixed various strange behaviors while changing the assigned stores when editing an automated discount

### Deleted
- Unified Automated and Manual DiscountActionMenu to one shared component and deleted both files and tests after combining

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Stacey Sugiono <<EMAIL>>


## [1.35.0] - 2024-02-15
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!117

### Description
Add ContentContainer from Navigation UI MFE for mobile breakpoint styles to properly place discounts MFE within bounds of the topbar and sidebar

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/186

### Added
- ContentContainer
- Cross MFE import configuration from nav sidebar

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [~] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.34.0] - 2024-02-08
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!103

### Description
<!-- Add high-level description here -->
### Closed Issues
#183 

<!-- Be sure to delete unused headers and comments -->
### Added
Added Validate function for the amount in Automated discount creation

### Deleted

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Ankita Jain <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>


## [1.33.0] - 2024-02-08
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!108

### Description
<!-- Add high-level description here -->
### Closed Issues
#176 

<!-- Be sure to delete unused headers and comments -->
### Added
Added Validation for the maxcount in the `Customer Cap` condition in Automated Discount creation


## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [ ] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Ankita Jain <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.32.0] - 2024-02-06
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!111

### Description
Added validation for the purchase minimum input field to accept only valid dollar amounts. Negative numbers, numbers over 2 decimal places, letters, and characters are not allowed.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/177

### Added
- Validation for purchase minimum input to accept only a valid dollar amount
- Unit tests

### Changed
- Refactored unit tests

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.31.0] - 2024-02-06
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!113

### Description
Updates and improves design consistency within the Discount MFE for Automated Discounts and Manual Discounts View.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/184

### Added
* `filterOptions` property for `PageHeader` component in order to render filter options in Manual Discount
* Sync styles for height properties on respective wrappers

### Deleted
* Unused components after `PageHeader` addition

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Kelvin Chinchilla <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Stacey Sugiono <<EMAIL>>


## [1.30.0] - 2024-02-02
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!109

### Description
Adds the Delete functionality for automated discounts via the Action menu.

### Closed Issues

Closes #152 

### Added
- Ability to delete discounts from the automated discounts table
- Tests for the AutomatedDiscountsActionMenu to verify delete modal opens when clicking the menu item
- Snackbar message when a discount is successfully deleted
- Snackbar message when an error occurs when attempting to delete a discount

### Changed

### Fixed

### Deleted

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Stacey Sugiono <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.29.0] - 2024-02-02
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!106

### Description
Support for allowing default condition to be as expanded in the `AccordionPanel` component

### Closed Issues

### Added
* Callback for toggle expand in `AccordionPanel` component
* `expanded` property to be added in `AccordionPanel` component

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Kelvin Chinchilla <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Stacey Sugiono <<EMAIL>>


## [1.28.0] - 2024-02-01
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!107

### Description
Repository restructured to enhance clarity and simplify maintenance. Components have been realigned with associated views, and a central index file has been introduced for hooks, utilities, and interfaces to streamline imports and facilitate future additions.


### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/185

### Added
- exports from a a central `index.ts` file for each of the following: hooks, utils, interfaces

### Changed
- Automated-specific components moved to a components folder within `AutomatedDiscountsView`
- Manual-specific components moved to a components folder within `ManualDiscountsView`
- All component within the `src/components/Shared` folder have been moved up directly under `components`
- Renamed `orgFeatureFlag.ts`, `orgDiscountReqBody.ts`, and `orgDiscountResponse.ts` files to `featureFlag.ts`, `requestModels.ts`, and `responseModels.ts`
- Changed default exports for interfaces to named exports for consistency and clarity
- Renamed interfaces to not have an `I` prefix

### Deleted
- Now empty `Shared` components folder


## MR Requirements
- [X] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [~] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.27.0] - 2024-02-01
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!105

### Description
For Single Store orgs, we will skip the "Select Stores" step.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/181

### Added
- useEffect to remove SelectStore step for single store orgs
- unit tests for useAutomatedDiscountsForm

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.26.0] - 2024-01-25
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!104

### Description
Created a ModalProvider and reusable ActionModal component so that it can be shared between Automated and Manual discounts. Automated discounts can toggle between active and inactive, and includes success and error Snackbars.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/132

### Added
- ModalProvider
- ActionModal component
- Automated discounts activate/deactivate functionality with success/error snackbars
- useDiscounts custom hook

### Changed
- Extracted out from ManualDiscountsView: `openDiscountModal`, `handleUnassignStore`, `handleDeactivateDiscount`, `handleActivateDiscount`, `handleActivateDiscount`, `handleDeleteDiscount`, `buildOrDiscountReqBody`
- Replaced Modal in ManualDiscountsView w/ new ActionModal
- Moved automated discount mock test data into a fixture file, and refactored where it was being duplicated

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.25.0] - 2024-01-25
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!101

### Description
Changing the customer events component to a `SelectInput` component combined with the ability to support a Nth Purchase Field for VISIT_NUMBER event type.

### Added
*  `Nth Purchase Field` to be populated as `eventValue` inside `customerEvents`
* Validations as controlled components for customer event scenario

### Changed
* `SelectInput` to support custom `onChange` for controlled components
* `formatConditions` to support nested value for `customerEvents`
* Required validations on Customer Event condition

### Removed
* `AutoCompleteInput` from Customer Event option


### Close Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/175

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Kelvin Chinchilla <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Kenji Mukai <<EMAIL>>


## [1.24.1] - 2024-01-19
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!99

### Description
When editing a manual discount, the success modal is replaced by a snackbar message and an error snackbar appears if not succesful.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/161

### Added
* Create and update messages
* Trigger create and update snackbars

### Changed
* Error messages to "Your request could not be completed due to a network error. Please try again or contact support."

### Deleted
* Success modal at `ReviewDiscountStep`
* Stack trace error from the error snackbar
* Unneeded parameters and state for `ReviewDiscountStep`

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Kelvin Chinchilla <<EMAIL>>

Approvers
Approved-by: Stacey Sugiono <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.24.0] - 2024-01-18
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!102

### Description
Remove the Review step from the Automated Discounts flow. On success, user is navigated back to Automated Discounts page

### Closed Issues

Closes #180 

### Added

### Changed

* Tests updated to reflect removal of Review step from Automated Discount creation flow

### Fixed

### Deleted

* Review step removed from Automated Discount creation flow

## MR Requirements

- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.23.0] - 2024-01-17
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!95

### Description
Add store picker step for automatic discount creation.

### Closed Issues

#100 

<!-- Be sure to delete unused headers and comments -->
### Added
- Added store picker step for automatic discount creation flow.

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Kelvin <<EMAIL>>
Co-authored-by: Stacey Sugiono <<EMAIL>>

Merged By
Wade Hastings <<EMAIL>>

Approvers
Approved-by: Stacey Sugiono <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.22.0] - 2024-01-15
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!97

### Description
### Closed Issues

Closes #166, #178 


### Added

* Sorting for stores column (Sorting is done by number of stores the discount is assigned to. If discounts only have one store assigned, they are sorted alphabetically by store name)
* Tooltips for manual discount names when truncated by column width

### Changed

* Styling for manual discount page and table
* Default sizes of manual discount table columns

## MR Requirements

- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Kenji Mukai <<EMAIL>>


## [1.21.0] - 2024-01-13
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!98

### Description
When creating a new automated discount, a success or error snackbar will appear

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/141

### Added
- Info Snackbar for successfully created automated discounts
- Error Snackbar when an error occurs during creation

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.20.0] - 2024-01-13
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!100

### Description
Updated the Customer Type to only allow selection of one customer licence type, by using a Select component. All tests have been updated to reflect the new data structure being sent to the backend.

### Closed Issues
#174 

### Changed
- Customer Type condition now uses a Select, as multiple options should not be selected


## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Jess Desrochers <<EMAIL>>

Merged By
Wade Hastings <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Wade Hastings <<EMAIL>>


## [1.19.1] - 2024-01-05
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!92

### Description
When there is a coupon present or "Require Coupon" is selected, error message appears when "Require Reason" is selected

This avoids that Require reason doesn't work as expected

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/156

### Added
* Inline validation error next to "Require Reason": Require Reasons cannot be used with coupons.
* Custom rule `validate` for requireReason checkbox
* Missing test for discount requirement box

### Changed
* Executed `format` script for consistency 

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Kelvin Chinchilla <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Stacey Sugiono <<EMAIL>>


## [1.19.0] - 2024-01-04
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!96

### Description
Reenable the deploy prod stage

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/172

### Changed
- Comment back in the `deploy_prod` stage


## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [~] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.18.1] - 2023-12-22
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!93

### Description
Fix add button and error messages so they are consistent with other MFEs

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/143

### Changed
- Add button to not have an icon
- Error messages on helper texts are specific to their inputs

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Wade Hastings <<EMAIL>>


## [1.18.0] - 2023-12-19
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!89

### Description
Refactor confirm modal to use TCL version and remove custom modals

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/125

### Changed
- Replaced custom modal with TCL version

### Deleted
- custom ConfirmModal

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Wade Hastings <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.17.0] - 2023-12-18
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!81

### Description
Automated discounts can be seen on the list view and appropriately sorts between automated vs manual discounts on both lists

### Closed Issues

https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/99

### Added
- `getSortedDiscounts()` util function + unit tests
- AutomatedDiscountsView displays automated discounts
- ManualDiscountsView displays manual discounts

### Changed
- Refactored manual discounts to use `getSortedDiscounts()`
- Reorganized and renamed `getStores()` file to match function name
- 
### Fixed
- Functions with named and default export to only have one
- ManualDiscountsView tests for `isManual` = true
- Proper kebab-case for some test id


## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.16.5] - 2023-12-13
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!91

### Description
Change UI to use v1 discount management url instead of v0

### Changed
* Discounts API base url

### Close Issues


Closes #168

### Personal
Co-Authors
Co-authored-by: “Kenji <<EMAIL>>

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>
Approved-by: Stacey Sugiono <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.16.4] - 2023-12-12
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!83

### Description
Coupons ui is hidden for line item discounts, only supported for cart

### Changed
* Hiding coupon form when selecting item line


### Close Issues


Closes #158

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Stacey Sugiono <<EMAIL>>


## [1.16.3] - 2023-12-12
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!90

### Description
Fixes manual discounts not updating due to missed automated fields removal from manual discounts

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/165

### Deleted
- leftover automated discounts fields in manual discounts

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kenji Mukai <<EMAIL>>


## [1.16.2] - 2023-12-12
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!86

### Description
Coupon codes are able to be in lowercase and uppercase

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/155

### Fixed
- coupon code input was being changed to uppercase

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Jeremy Culler <<EMAIL>>


## [1.16.1] - 2023-12-11
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!82

### Description
Adjust FE validation to prevent saving coupons with spaces

### Added
* Coupon name validation

### Close Issues


Closes #145

### Personal
Co-Authors

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Stacey Sugiono <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.16.0] - 2023-12-05
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!80

### Description
The basic details of creating an automated discount can be saved.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/138

### Added
- Automated Discount Form + unit tests
- Discount Details Step + unit tests
- Updated IOrgDiscountReqBody and IOrgDiscountResponses with isStackable and conditions properties
- PageHeader component + unit tests
- Footer component + unit tests
- HeaderBar unit tests
- DiscountConditionObject interface
- Add Discount button

### Changed
- Refactored and reorganized discount interfaces
- Renamed previous footer to `LegacyFooter` (to be removed later)
- Renamed `Title` component to `HeaderBar`
- Link updated to Treez Component Library vrsion
- Updated discount methods interface

### Fixed
- Typos

### Deleted
- `ActionMenuComponent` prop from RowActionButtons
- Unused `MainWrapper` styled-component
- `StepsWrapper` styled-component file
- `automatedDiscount.ts` after consolidating into `discount.ts`

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>


## [1.15.0] - 2023-11-23
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!71

### Description
<!-- Add high-level description here -->
### Closed Issues
#22
<!-- Be sure to delete unused headers and comments -->
### Added
- Coupons Form in Discount Form

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Kelvin <<EMAIL>>
Co-authored-by: Kenji Mukai <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers


## [1.14.0] - 2023-11-15
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!77

### Description
Added automated discount action menu to the table

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/131

### Added
- automated discount action menu added to the table
- unit tests for 


## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.13.0] - 2023-11-14
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!76

### Description
Create table list for Automated Discounts using DataGridPro from Treez Component Library

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/124

### Added
- Automated discounts path to root component
- AutomatedDiscountsTable to view page
- Created table-utils folder with `formatAmount`, `storeCellRenderer`, and `buildRowsWithHierarchy` functions taken from ManualDiscountsTable and added unit tests
- `isManual` to `orgDiscountResponse`
- Automated discounts interfaces (`IAutomatedDiscount` and `IAutomatedDiscountRow`)
- `useTreeDataGroupingCell` hook
- AUTOMATED_DISCOUNT_METHODS constant
- Added Automated Discount Table
- Unit tests

### Changed
- Updated Treez Component Library to v5.9.0
- Moved components to a Shared folder and consolidated Manual Discounts components to organize
- Renamed the DISCOUNTS_METHODS constant to MANUAL_DISCOUNTS_METHODS since we now have automated discounts
- Moved `DiscountStatusBox` out of Manual Discounts Table to make it a reusable component
- Refactored ManualDiscountsTable to use the new `DiscountStatusBox` component
- Renamed view pages to include "View" for clarification


## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>


## [1.12.3] - 2023-11-09
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!78

### Description
Manual discounts has been incorrectly saving with `isManual` set to false. This fixes it to true and adds unit test

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/137

### Added
- unit test for saving manual discounts as `isManual = true`

### Fixed
- manual discounts saving with `isManual` set to true


## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.12.2] - 2023-11-02
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!73

### Description
Updating Name of Project on Readme to test auto changelog


### Changed
* for changes in existing functionality.

### Personal
Co-Authors

Merged By
Jeremy Culler <<EMAIL>>

Approvers


## [1.12.1] - 2023-11-02
## [1.12.0] - 2023-11-02
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!70

### Description
Refactor the menu so it can be reused for both manual and auto discounts and added unit tests

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/134

### Added
- unit tests

### Changed
- refactored FlyOutMenu to be a reusable component
- renamed FlyOutMenu to DiscountActionMenu
- ManualDiscountActionMenu to use the new DiscountActionMenu reusable component


## MR Requirements
- [ ] ~~If you made visual changes, you have completed the design review process and your changes have been approved~~
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Stacey Sugiono <<EMAIL>>

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>

## [1.11.2] - 2023-10-26
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!69

### Description
Manually adding changelog for v1.11.1 since changelog still not automatic. Setting was reverted so hopefully it works this time.

### Closed Issues
- https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/126

Which adds changelog for
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/merge_requests/67

### Added
Changelog for v1.11.1


## MR Requirements 
- [ ] ~~If you made visual changes, you have completed the design review process and your changes have been approved~~
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [ ] ~~You added a demo video or a screenshot for any visual changes to a comment on the MR~~
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [ ] ~~You added unit tests to cover your changes~~
- [ ] ~~You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down~~

### Personal
Co-Authors
Co-authored-by: Stacey Sugiono <<EMAIL>>

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>

## [1.11.1] - 2023-10-25
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!67

### Description
Changelogs for v1.10.0 and v1.11.0 were missing so this is to manually add it

### Closed Issues
- https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/126

Which adds change logs for the following
- https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/79
- https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/42

### Added
Changelog information that was missing from the last couple merges


## MR Requirements
- [ ] ~~If you made visual changes, you have completed the design review process and your changes have been approved~~
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [ ] ~~You added a demo video or a screenshot for any visual changes to a comment on the MR~~
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [ ] ~~You added unit tests to cover your changes~~
- [ ] ~~You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down~~

### Personal
Co-Authors
Co-authored-by: Stacey Sugiono <<EMAIL>>

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.11.0] - 2023-10-24
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!65

### Description
Replace Menu components with the Treez Component Library version and refactor the FlyOutMenu by breaking it out into it's own component.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/42

### Added
- Additional unit tests for FlyOutMenu

### Changed
- Replace MUI Menu components with TCL Menu components
- Move FlyOutMenu component into its own file

## MR Requirements
- [ ] ~~If you made visual changes, you have completed the design review process and your changes have been approved~~
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Stacey Sugiono <<EMAIL>>

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.10.0] - 2023-10-23
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!64

### Description
Changed store search to use the SearchInput component from the UI library.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/79

### Changed
Store search

## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: John Hamel <<EMAIL>>
Co-authored-by: Stacey Sugiono <<EMAIL>>

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>

## [1.9.0] - 2023-10-16
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!57

### Description
Based on latest issue at https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/94 some tests are added for require pin/reason scenarios on discount tables

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/94

### Added
- Pending tests for verification of required / not required conditions on manual discount form views.

## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- ~~[ ] You added a demo video or a screenshot for any visual changes to a comment on the MR~~
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Kelvin <<EMAIL>>
Co-authored-by: santhoshkumar <<EMAIL>>

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>


## [1.8.1] - 2023-10-13
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!58

### Description
Removed the old info alert for edit discounts page and added a new warning alert (using TCL) with correct, updated copy.

### Closed Issues

https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/89

### Added

TCL Warning Alert with correct, updated copy for edit discount page

### Changed

Updated unit tests for the alert

### Deleted

Non-TCL Info Alert component with incorrect copy

## MR Requirements
- [X] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>


## [1.8.0] - 2023-10-11
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!59

### Description
<!-- Add high-level description here -->
### Closed Issues
#94 

### Fixed
- On discount edit workflow & data grid view, requireReason and requirePin existing store customization value not loaded properly, So that system shows wrong details on respective checkbox.
- Modified validation condition of store customization value. So that systtem shows correct details on respective checkbox.

## MR Requirements
~~- [ ] If you made visual changes, you have completed the design review process and your changes have been approved~~
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [ ] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: santhoshkumar <<EMAIL>>

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kelvin Chinchilla <<EMAIL>>


## [1.7.1] - 2023-10-10
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!55

### Description
Radio button shows that it is still a line item discount

### Fixed
* fix discount type default value

### Close Issues
Closes #93

### Personal
Co-Authors
Co-authored-by: Kenji Mukai <<EMAIL>>
Co-authored-by: Kelvin Chinchilla <<EMAIL>>

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>


## [1.7.0] - 2023-10-09
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!43

### Description
In Edit Discounts, the tooltips and text descriptions have been updated with new copy, color, and size variants to match our Figma designs.

### Closed Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/67

### Added
- Tooltip for reset global button
- Unit tests for tooltips

### Changed
- Tooltip copy, placement, and variant for reset amount
- Tooltip copy, placement,  and variant for require pin
- Tooltip copy, placement, and variant for require reason
- Updated Select Store Step description for clarity
- Reorganized unit tests for improved readability

### Fixed
- Typo on a header, typography size and colors
- Border of the edit section to wrap all of the content
- Test IDs to be hyphenated

## MR Requirements
- [X] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Jess Desrochers <<EMAIL>>

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>


## [1.6.4] - 2023-10-04
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!50

### Description
MSO DISCOUNT - Unable to edit discount >> 'Select Stores' after discount has been created.

### Fixed
* Fix ddit discount and store customization

### Close Issues
Closes #81

### Personal
Co-Authors
Co-authored-by: Kenji Mukai <<EMAIL>>

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: John Hamel <<EMAIL>>


## [1.6.3] - 2023-10-03
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!47

### Description
Update Line and Cart Options in Discount Form

### Added
* FFL for the coupons feature

### Changed
* Discount Info Step to show line and cart options as radio buttons


### Close Issues
Closes #23

### Personal
Co-Authors
Co-authored-by: Kenji Mukai <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: John Hamel <<EMAIL>>


## [1.6.2] - 2023-09-25
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!45

### Description
For dollar discounts, remove the trailing two decimals before rendering into the form.

### Changed
* `buildManualDiscountDefaultValues` to format the amount when method is dollar
* Let customers use decimals in both cases (% and $)
* Limit decimals to 2 (.XX)
* Hide double zero decimals

### Close Issues
Closes #72

### Personal
Co-Authors
Co-authored-by: Kenji Mukai <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>


## [1.6.1] - 2023-09-20
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!44

### Description
Description goes here
Single Store Overrides - Require manager pin cannot be edited + UI tweaks

### Fixed
* UI alignments.
* Require Manager PIN cannot be edited

### Close Issues

Closes #65

### Personal
Co-Authors
Co-authored-by: Kenji Mukai <<EMAIL>>

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>


## [1.6.0] - 2023-09-19
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!35

### Description
Makes a custom validation on discount amount input, to validate that the number is:
- greater than 0.
- discount type $ amount is up to 2 decimals.
- discount type % is is up to 6 decimals.

Error messages:

* $ Discounts - Please enter 2 decimal places. For example $5.50
* % Discounts - Please enter a maximum of 6 decimal places. For example 66.666666%
* % Discounts over 100 - Please enter a number smaller or equal to 100%

Discount List
* Displays $ amount with two decimals always. For example $4.2 should be displayed as $4.20.
* % Discount should display with all decimals as stored in the database truncating any trailing zeros. For example 4.22% should show like 4.22%
* Ensure data grid can fit all decimals without overflowing


### Closed Issues
#61 

### Added
* Custom `validate` function within controller input for discount amount.

### Changed
* Updated validation messages for corresponding discount types.

### Deleted
* Removed pattern object for controller input, since validate function is used within react-hook-form


## MR Requirements
- [ ] If you made visual changes, you have completed the design review process and your changes have been approved
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [ ] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Kelvin Chinchilla <<EMAIL>>

Approvers
Approved-by: John Hamel <<EMAIL>>


## [1.5.1] - 2023-09-18
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!42

### Description
Introduce sortable column for discount creation date/time in discount

### Added
* Created at column in discounts table

### Close Issues
Closes #64

### Personal
Co-Authors
Co-authored-by: Kenji Mukai <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: John Hamel <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.5.0] - 2023-09-18
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!38

### Description
Updates modals to use single state, removing a number of props passed down to the table rows. Also updates the modals to use the Treez Component Library Modal instead of the custom modal created in the MFE, as well as establishing the start of error handling for failed calls to delete discounts.

### Closed Issues
#44 

### Changed
- Modal for actions are now using shared state
- Updated modal for actions on discounts to use Treez Component Library Modal


## MR Requirements
* [x] If you made visual changes, you have completed the design review process and your changes have been approved
* [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
* [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
* [x] Your changes meet all of the Acceptance Criteria listed in the user story
* [x] You reviewed your own MR and left comments where needed to provide context for reviewers
* [x] You added unit tests to cover your changes
* [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: John Hamel <<EMAIL>>
Approved-by: Stacey Sugiono <<EMAIL>>


## [1.4.0] - 2023-09-15
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!41

### Description
Sorting has been updated to restrict some columns and fix row comparisons. Rows can no longer be sorted by stores, as no standard of sorting has been decided. The amount column is now comparing based on their numerical value, instead of alphabetically. On first load, discounts are now ordered alphabetically by name, ascending.

### Closed Issues
#62 

### Changed
- Table can no longer be sorted by store
- Sorting by amount now sorts numerically, ignoring whether it is percent or amount
- On page load, discounts are presented in alphabetical order, ascending, without applying any sorts

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Stacey Sugiono <<EMAIL>>


## [1.3.1] - 2023-09-11
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!34

### Description
Update the validation for the "Amount" field to not allow negative numbers and the number must be greater than 0.

### Closed Issues
[58](https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/58)

### Changed
- Update validation message to say "Please enter a number greater than 0"

### Fixed
- Fixed validation so that it does not accept negative numbers and the amount must be greater than 0


## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>


## [1.3.0] - 2023-09-06
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!31

### Description

Cashier discounts is renamed to manual discounts.

### Closed Issues

[38](https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/38)

### Changed
Renamed "cashier" to "manual" everywhere in the repo
- components: CashierDiscountFilters, CashierDiscountForm, CashierDiscountHeader, CashierDiscountTable
- constants: `CASHIER_DISCOUNTS`, `cashierDiscountsPath`, `addCashierDiscountsPath`, `editCashierDiscountsPath`
- hooks: `useCreateCashierDiscount`, `useUpdateCashierDiscount`, `useUpdateCashierDiscount`
- interfaces: `ICashierDiscount`
- views: CashierDiscountForm, CashierDiscounts

### Fixed
- typos in variable names

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [X] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [~] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Stacey Sugiono <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>


## [1.2.1] - 2023-09-05
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!29

### Description
### Fixed
When an region is null, group those entities under "Unknown Region" header instead of throwing an error

Tested on app.dev.treez.io

Before Fix
![Screenshot_2023-09-01_at_1.24.17_PM](/uploads/a8c298fb92083283dc759e3938d10d04/Screenshot_2023-09-01_at_1.24.17_PM.png)

After Fix
![Screenshot_2023-09-01_at_3.52.42_PM](/uploads/6c941a8a74f5d0aac8cd6fe679504a28/Screenshot_2023-09-01_at_3.52.42_PM.png)

Implemented test first.

### Personal
Co-Authors
Co-authored-by: Kenji Mukai <<EMAIL>>

Merged By
Jeremy Culler <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>


## [1.2.0] - 2023-08-28
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!28

### Description
Updated the routing to use the createBrowserRouter structure from React Router, enabling future enhancements using the Data APIs introduced in React Router v6.4

### Closed Issues
#45 

### Changed
- Routing updated to use createBrowserRouter instead of Router components
- Default error page routing added on non-existent discount paths
- Restructured files to move more components into Views section of repo 

### Deleted
- Removed CustomSnackbar and replaced with Treez Component Library Snackbar

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved (do this BEFORE MR code review) 
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [X] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Kenji Mukai <<EMAIL>>


## [1.1.0] - 2023-08-24
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!27

### Description
In order for production environment to work, the environment variables needed to be updated, as well as changing the gitlab-ci configuration to remove the release-prod step from pipeline.

### Closed Issues
#34 

### Added
- Production environment variables for service URL

### Changed
- Removed release step from pipeline

## MR Requirements
- [~] If you made visual changes, you have completed the design review process and your changes have been approved (do this BEFORE MR code review) 
- [X] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [~] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [X] Your changes meet all of the Acceptance Criteria listed in the user story
- [X] You reviewed your own MR and left comments where needed to provide context for reviewers
- [~] You added unit tests to cover your changes
- [X] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors

Merged By
Jess D <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>


## [1.0.6] - 2023-08-23
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!25

### Description
Replace checkbox with a toggle for 'Global Conditions'

### Close Issues
https://gitlab.com/treez-inc/engineering/front-of-house/discount-management-micro-front-end/-/issues/28

Closes #28

### Added
* Add top bar when editing Discount or Store Condition

### Changed
* Replace checkbox with a toggle for global conditions
* Add confirm modal when enabling/disabling toggle in Edit Mode


## MR Requirements
- [x] If you made visual changes, you have completed the design review process and your changes have been approved (do this BEFORE MR code review) 
- [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
- [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
- [x] Your changes meet all of the Acceptance Criteria listed in the user story
- [x] You reviewed your own MR and left comments where needed to provide context for reviewers
- [x] You added unit tests to cover your changes
- [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type can be narrowed down

### Personal
Co-Authors
Co-authored-by: Kenji Mukai <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>
Approved-by: Nicolas Lundin <<EMAIL>>


## [1.0.5] - 2023-08-15
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!24

### Description
Fixing wrong behavior on Select Stores

### Fixed
* Fixed Select Store step when selecting/unselecting multiple stores


### Close Issues
https://gitlab.com/treez-inc/engineering/team-discounts/discount-management-micro-front-end/-/issues/31

Closes #31

### Personal
Co-Authors
Co-authored-by: Kenji Mukai <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>


## [1.0.4] - 2023-08-14
See merge request treez-inc/engineering/front-of-house/discount-management-micro-front-end!23

### Description
Add a column for Discount Status to the far right of the Overview table

### Added
* Column for Discount Status in Discount Overview

### Close Issues
https://gitlab.com/treez-inc/engineering/team-discounts/discount-management-micro-front-end/-/issues/25

### Personal
Co-Authors
Co-authored-by: Kenji Mukai <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Jess D <<EMAIL>>


## [1.0.3] - 2023-08-11
See merge request treez-inc/engineering/team-foh-mso/discount-management-micro-front-end!22

### Description
Filter discount list by store

### Added
* Dropdown Chip with Stores to filter Discounts.

### Close Issues
https://gitlab.com/treez-inc/engineering/team-discounts/discount-management-micro-front-end/-/issues/26

### Personal
Co-Authors
Co-authored-by: Kenji Mukai <<EMAIL>>

Merged By
Kenji Mukai <<EMAIL>>

Approvers
Approved-by: Jeremy Culler <<EMAIL>>
Approved-by: Jess D <<EMAIL>>


## [1.0.2] - 2023-08-08
## [1.0.1] - 2023-08-07
## [1.0.0] - 03.28.2023
### Added
- Created Discount Management MFE from template
- Discount Overview Page
- Create Discount
- Update Discount
- Delete Discount
- Unassign Store Condition from Discount





















































































































































































































