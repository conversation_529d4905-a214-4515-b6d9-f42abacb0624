import { http, HttpResponse } from "msw";
import {
  getOrgTagGroupUrl,
  getProductCollectionsUrl,
  listDiscountUrl,
  upsertDiscountUrl,
} from "../../services/apiEndPoints";
import { OrgDiscountReqBody } from "../../interfaces/requestModels";
import {
  testAutomatedDiscountsResponse,
  testProductCollections,
} from "../fixtures";
import { testDiscountsResponse } from "../constants";

const getDiscountsHandler = http.get(listDiscountUrl, () =>
  HttpResponse.json([
    ...testAutomatedDiscountsResponse,
    ...testDiscountsResponse,
  ])
);

const getDiscountByIdHandler = http.get(
  `${upsertDiscountUrl}/:id`,
  ({ params }) => {
    if (params.id === "discount1") {
      return HttpResponse.json(testAutomatedDiscountsResponse[0]);
    }
    if (params.id === "disc1") {
      return HttpResponse.json(testDiscountsResponse[0]);
    }
    if (params.id === "disc2") {
      return HttpResponse.json(testDiscountsResponse[1]);
    }
    return new HttpResponse(null, { status: 404 });
  }
);

const getProductCollectionsHandler = http.get(getProductCollectionsUrl, () =>
  HttpResponse.json({
    data: [...testProductCollections],
    totalCount: testProductCollections.length,
  })
);

const getOrgTagGroupByNameHandler = http.get(getOrgTagGroupUrl, () =>
  HttpResponse.json({
    name: "Customer Group",
    data: { tags: [] },
    status: 200,
  })
);

const createDiscountHandler = http.post(
  upsertDiscountUrl,
  async ({ request }) => {
    const discount = (await request.json()) as OrgDiscountReqBody;
    return HttpResponse.json(discount);
  }
);

const updateDiscountHandler = http.put(
  upsertDiscountUrl,
  async ({ request }) => {
    const discount = (await request.json()) as OrgDiscountReqBody;
    return HttpResponse.json(discount);
  }
);

const deleteDiscountHandler = http.delete(`${upsertDiscountUrl}/:id`, () =>
  HttpResponse.json()
);

export const handlers = [
  getDiscountsHandler,
  getDiscountByIdHandler,
  getProductCollectionsHandler,
  createDiscountHandler,
  updateDiscountHandler,
  deleteDiscountHandler,
  getOrgTagGroupByNameHandler,
];
