import { EntityResponse } from "../../../interfaces/entity";
import { OrgDiscountResponse } from "../../../interfaces/responseModels";

export const getBannerTitle = ({
  orgDiscount,
  entities,
  storeCustomizationId,
}: {
  orgDiscount: OrgDiscountResponse | undefined;
  entities?: EntityResponse[] | undefined;
  storeCustomizationId?: string | undefined;
}) => {
  if (!orgDiscount) {
    return "";
  }

  if (storeCustomizationId && entities) {
    const currentStoreCustomization = orgDiscount.storeCustomizations.find(
      (storeCustom) => storeCustom.id === storeCustomizationId
    );
    const currentStore = entities.find(
      (entity) => entity.id === currentStoreCustomization?.entityId
    );

    if (!currentStore) {
      return "";
    }

    return `Edit Discount - "${currentStore?.name}"`;
  }

  return "Edit Discount";
};
