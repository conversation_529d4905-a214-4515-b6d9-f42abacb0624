import { useReducer } from "react";
import ApiService from "../../services/api/apiService";
import { ErrorObject } from "../../interfaces/error";

const LOADING = "LOADING" as const;
const SUCCESS = "SUCCESS" as const;
const FAILURE = "FAILURE" as const;

export type TStateUseDelete<Data> =
  | { loading: false; error: undefined; data: undefined }
  | { loading: true; error: undefined; data: undefined }
  | {
      loading: false;
      error: ErrorObject;
      data: undefined;
    }
  | { loading: false; error: undefined; data: Data };

export type THandlerUseDelete = () => void;

type TActionUseDelete<Data> =
  | { type: typeof LOADING }
  | { type: typeof SUCCESS; payload: Data }
  | { type: typeof FAILURE; payload: ErrorObject };

function reducer<Data>(
  _: TStateUseDelete<Data>,
  action: TActionUseDelete<Data>
): TStateUseDelete<Data> {
  if (action.type === LOADING) {
    return {
      error: undefined,
      loading: true,
      data: undefined,
    };
  }
  if (action.type === SUCCESS) {
    return {
      error: undefined,
      loading: false,
      data: action.payload,
    };
  }
  return {
    error: action.payload,
    loading: false,
    data: undefined,
  };
}

const initialState: TStateUseDelete<never> = {
  loading: false,
  error: undefined,
  data: undefined,
};

export type TRequestMethod = "delete";

export default function useDelete<ResponseData>(
  apiInstance: ApiService,
  url: string,
  method: TRequestMethod
): [THandlerUseDelete, TStateUseDelete<ResponseData>] {
  const [state, dispatch] = useReducer(reducer<ResponseData>, initialState);

  const handleRequest: THandlerUseDelete = async () => {
    try {
      dispatch({ type: LOADING });
      const { data } = await apiInstance[method](url);
      dispatch({ type: SUCCESS, payload: data });
    } catch (e: any) {
      dispatch({ type: FAILURE, payload: e.response });
      throw e;
    }
  };

  return [handleRequest, state];
}
