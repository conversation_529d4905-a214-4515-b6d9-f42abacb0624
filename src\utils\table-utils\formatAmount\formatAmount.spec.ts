import { formatAmount } from ".";
import {
  AutomatedDiscountMethods,
  BogoDiscountMethods,
  BundleDiscountMethods,
  BundlePurchaseRequirement,
} from "../../../constants/discounts";

describe("formatAmount", () => {
  it("should return a dollar amount for DOLLAR method", () => {
    const result = formatAmount("10.55", AutomatedDiscountMethods.DOLLAR);
    expect(result).toBe("$10.55");
  });

  it("should return a percentage for PERCENT method", () => {
    const result = formatAmount("15", AutomatedDiscountMethods.PERCENT);
    expect(result).toBe("15.00%");
  });

  it("should return a dollar amount for COST_PLUS method", () => {
    const result = formatAmount("7.25", AutomatedDiscountMethods.COST_PLUS);
    expect(result).toBe("$7.25");
  });

  it("should return a plain amount for for BOGO method", () => {
    const result = formatAmount("7.25", AutomatedDiscountMethods.BOGO);
    expect(result).toBe("7.25");
  });

  it("should return a plain amount if method is not recognized", () => {
    const result = formatAmount("20.99", "UNKNOWN_METHOD");
    expect(result).toBe("20.99");
  });

  it("should return a dollar amount if method is BOGO and discount unit DOLLAR", () => {
    const result = formatAmount("20.99", "BOGO", {
      bogoConditions: {
        discountUnit: BogoDiscountMethods.DOLLAR,
        buyCount: 1,
        getCount: 1,
      },
    });
    expect(result).toBe("$20.99");
  });

  it("should return a dollar amount if method is BOGO and discount unit TARGET_PRICE", () => {
    const result = formatAmount("20.99", "BOGO", {
      bogoConditions: {
        discountUnit: BogoDiscountMethods.TARGET_PRICE,
        buyCount: 1,
        getCount: 1,
      },
    });
    expect(result).toBe("$20.99");
  });

  it("should return a percent amount if method is BOGO and discount unit PERCENT", () => {
    const result = formatAmount("20.99", "BOGO", {
      bogoConditions: {
        discountUnit: BogoDiscountMethods.PERCENT,
        buyCount: 1,
        getCount: 1,
      },
    });
    expect(result).toBe("20.99%");
  });

  it("should return a dollar amount if method is BUNDLE and discount unit DOLLAR", () => {
    const result = formatAmount("20.99", "BUNDLE", {
      bundleConditions: {
        discountUnit: BundleDiscountMethods.DOLLAR,
        purchaseRequirement: BundlePurchaseRequirement.RETAIL_VALUE,
        buyCount: 1,
        threshold: false,
      },
    });
    expect(result).toBe("$20.99");
  });

  it("should return a dollar amount if method is BUNDLE and discount unit TARGET_PRICE", () => {
    const result = formatAmount("20.99", "BUNDLE", {
      bundleConditions: {
        discountUnit: BundleDiscountMethods.DOLLAR,
        purchaseRequirement: BundlePurchaseRequirement.RETAIL_VALUE,
        buyCount: 1,
        threshold: false,
      },
    });
    expect(result).toBe("$20.99");
  });

  it("should return a percent amount if method is BUNDLE and discount unit PERCENT", () => {
    const result = formatAmount("20.99", "BUNDLE", {
      bundleConditions: {
        discountUnit: BundleDiscountMethods.PERCENT,
        purchaseRequirement: BundlePurchaseRequirement.RETAIL_VALUE,
        buyCount: 1,
        threshold: false,
      },
    });
    expect(result).toBe("20.99%");
  });

  it("should return a percent amount if method is BUNDLE and discount unit PERCENT threshold enabled", () => {
    const result = formatAmount("20.99", "BUNDLE", {
      bundleConditions: {
        discountUnit: BundleDiscountMethods.PERCENT,
        purchaseRequirement: BundlePurchaseRequirement.RETAIL_VALUE,
        buyCount: 1,
        threshold: true,
      },
    });
    expect(result).toBe("20.99%");
  });
});
