import React, { useState, SyntheticEvent } from "react";
import { styled } from "@mui/material";
import { Tooltip } from "@treez-inc/component-library";

interface TruncatedTooltipProps {
  children: string;
}

const StyledTextWrapper = styled("span")(() => ({
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  display: "block",
}));

const TruncatedTooltip = ({ children }: TruncatedTooltipProps) => {
  const [open, setOpen] = useState(false);
  const handleClose = () => {
    if (open) setOpen(false);
  };

  const handleOpen = (event: SyntheticEvent<Element, Event>) => {
    const element = event.target as HTMLElement;
    if (element.offsetWidth < element.scrollWidth) {
      setOpen(true);
    }
  };

  return (
    <Tooltip
      open={open}
      onClose={handleClose}
      onOpen={handleOpen}
      title={children}
      enterDelay={400}
      leaveDelay={0}
      variant="context"
    >
      <StyledTextWrapper data-testid="truncated-tooltip-content">
        {children}
      </StyledTextWrapper>
    </Tooltip>
  );
};

export default TruncatedTooltip;
