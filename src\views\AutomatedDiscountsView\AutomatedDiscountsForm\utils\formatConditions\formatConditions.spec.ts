import { CUSTOMER_EVENTS } from "../../../../../constants/discountForm";
import { formatConditions } from "."; // Replace with your file path
import { DiscountConditionFormData } from "../../../../../interfaces/discounts";

describe("formatConditions function", () => {
  it("should return an empty object if conditions object is empty", () => {
    const conditions: DiscountConditionFormData =
      {} as DiscountConditionFormData;
    const formattedConditions = formatConditions(conditions);
    expect(formattedConditions).toEqual({});
  });

  it("should correctly format customerCapEnabled", () => {
    const conditions: DiscountConditionFormData = {
      customerCapEnabled: true,
      customerCapValue: 100,
    } as DiscountConditionFormData;
    const formattedConditions = formatConditions(conditions);
    expect(formattedConditions.customerCapEnabled).toBe(true);
  });

  it("should correctly format customerLimitEnabled", () => {
    const conditions: DiscountConditionFormData = {
      customerLimitEnabled: true,
      customerLimitValue: 100,
    } as DiscountConditionFormData;
    const formattedConditions = formatConditions(conditions);
    expect(formattedConditions.customerLimitEnabled).toBe(true);
  });

  it("should correctly format purchaseMinimumEnabled", () => {
    const conditions: DiscountConditionFormData = {
      purchaseMinimumEnabled: true,
      purchaseMinimumValue: 50,
      purchaseMinimumType: "amount",
    } as DiscountConditionFormData;
    const formattedConditions = formatConditions(conditions);
    expect(formattedConditions.purchaseMinimumEnabled).toBe(true);
  });

  it("should correctly format customerEventEnabled", () => {
    const conditions: DiscountConditionFormData = {
      customerEventEnabled: true,
      customerEvents: {
        eventName: CUSTOMER_EVENTS.VISIT_NUMBER.displayValue,
        eventValue: 1,
      },
    } as DiscountConditionFormData;
    const formattedConditions = formatConditions(conditions);
    expect(formattedConditions.customerEventEnabled).toBe(true);
  });

  it("should correctly format redemptionLimitEnabled", () => {
    const conditions: DiscountConditionFormData = {
      redemptionLimitEnabled: true,
      redemptionLimitValue: 5,
    } as DiscountConditionFormData;
    const formattedConditions = formatConditions(conditions);
    expect(formattedConditions.redemptionLimitEnabled).toBe(true);
  });

  it("should correctly format fulfillmentTypesEnabled", () => {
    const conditions: DiscountConditionFormData = {
      fulfillmentTypesEnabled: true,
      fulfillmentTypes: {
        IN_STORE: true,
        DELIVERY: false,
        PICKUP: true,
        EXPRESS: false,
      },
    } as DiscountConditionFormData;
    const formattedConditions = formatConditions(conditions);
    expect(formattedConditions.fulfillmentTypesEnabled).toBe(true);
  });

  it("should correctly format customerLicenseTypeEnabled", () => {
    const conditions: DiscountConditionFormData = {
      customerLicenseTypeEnabled: true,
      customerLicenseType: "ADULT",
    } as DiscountConditionFormData;
    const formattedConditions = formatConditions(conditions);
    expect(formattedConditions.customerLicenseTypeEnabled).toBe(true);
  });

  it("should correctly format customerEvents with eventValue present", () => {
    const conditions: DiscountConditionFormData = {
      customerEventEnabled: true,
      customerEvents: {
        eventName: CUSTOMER_EVENTS.VISIT_NUMBER.displayValue,
        eventValue: 1,
      },
    } as DiscountConditionFormData;

    const formattedConditions = formatConditions(conditions);

    expect(formattedConditions.customerEventEnabled).toBe(true);
    expect(formattedConditions.customerEvents).toHaveLength(1);
    expect(formattedConditions.customerEvents).toEqual([
      {
        eventName: CUSTOMER_EVENTS.VISIT_NUMBER.displayValue,
        eventValue: 1,
      },
    ]);
  });

  it("should correctly format customerEvents without eventValue", () => {
    const conditions: DiscountConditionFormData = {
      customerEventEnabled: true,
      customerEvents: {
        eventName: CUSTOMER_EVENTS.BIRTHDAY.displayValue,
      },
    } as DiscountConditionFormData;

    const formattedConditions = formatConditions(conditions);

    expect(formattedConditions.customerEventEnabled).toBe(true);
    expect(formattedConditions.customerEvents).toHaveLength(1);
    expect(formattedConditions.customerEvents).toEqual([
      {
        eventName: CUSTOMER_EVENTS.BIRTHDAY.displayValue,
        eventValue: null,
      },
    ]);
  });
});
