import { AutomatedDiscountFormData } from "../interfaces/discounts";
import {
  BogoDiscountMethods,
  CustomerEvents,
  ManualDiscountMethods,
  AutomatedDiscountMethods,
  FulfillmentTypes,
  LicenseTypes,
  PackageAgeTypes,
  PurchaseAmountTypes,
  BundleDiscountMethods,
  BundlePurchaseRequirement,
} from "./discounts";

export const conditionsDropdownSelect = [
  { key: "customer-cap-condition", label: "Customer Cap" },
  { key: "customer-limit-condition", label: "Per-Customer Limit" },
  { key: "customer-event-condition", label: "Customer Event" },
  { key: "customer-group-condition", label: "Customer Group" },
  { key: "customer-type-condition", label: "Customer Type" },
  { key: "fulfillment-type-condition", label: "Fulfillment Type" },
  { key: "redemption-item-limit-condition", label: "Redemption Item Limit" },
  { key: "purchase-minimum-condition", label: "Purchase Minimum" },
  { key: "package-age-condition", label: "Package Age" },
];

export const manualDiscountConditionsDropdownSelect = [
  { key: "per-customer-condition", label: "Per-Customer Limit" },
  { key: "purchase-minimum-condition", label: "Purchase Minimum Requirement" },
  { key: "redemption-item-limit-condition", label: "Redemption Item Limit" },
];

export const MANUAL_DISCOUNT_METHODS = {
  [ManualDiscountMethods.DOLLAR]: ManualDiscountMethods.DOLLAR,
  [ManualDiscountMethods.PERCENT]: ManualDiscountMethods.PERCENT,
  [ManualDiscountMethods.PRICE_AT]: ManualDiscountMethods.PRICE_AT,
};

export const AUTOMATED_DISCOUNT_METHODS = {
  [AutomatedDiscountMethods.DOLLAR]: "Dollar Amount",
  [AutomatedDiscountMethods.PERCENT]: "Percent Discount",
  [AutomatedDiscountMethods.COST_PLUS]: "Cost Plus %",
  [AutomatedDiscountMethods.BOGO]: "Buy One Get One",
  [AutomatedDiscountMethods.BUNDLE]: "Bundle",
};

export const DISCOUNT_METHOD_OPTIONS = [
  {
    displayName: "$",
    displayValue: ManualDiscountMethods.DOLLAR,
  },
  {
    displayName: "%",
    displayValue: ManualDiscountMethods.PERCENT,
  },
  {
    displayName: "Price At",
    displayValue: ManualDiscountMethods.PRICE_AT,
  },
];

export const CUSTOMER_EVENTS = {
  [CustomerEvents.BIRTHDAY]: {
    displayName: "Birthday",
    displayValue: CustomerEvents.BIRTHDAY,
  },
  [CustomerEvents.SIGN_UP_DATE]: {
    displayName: "Sign-up Date",
    displayValue: CustomerEvents.SIGN_UP_DATE,
  },
  [CustomerEvents.VISIT_NUMBER]: {
    displayName: "Nth Purchase",
    displayValue: CustomerEvents.VISIT_NUMBER,
  },
};

export const customerEvents = [
  CUSTOMER_EVENTS.VISIT_NUMBER,
  CUSTOMER_EVENTS.BIRTHDAY,
  CUSTOMER_EVENTS.SIGN_UP_DATE,
];

export const PACKAGE_AGE_TYPES = {
  [PackageAgeTypes.RECEIVED]: {
    displayName: "Received Date",
    displayValue: PackageAgeTypes.RECEIVED,
  },
  [PackageAgeTypes.PACKAGED]: {
    displayName: "Packaged Date",
    displayValue: PackageAgeTypes.PACKAGED,
  },
};

export const packageAgeTypes = [
  PACKAGE_AGE_TYPES.RECEIVED,
  PACKAGE_AGE_TYPES.PACKAGED,
];

export const FULFILLMENT_TYPES = {
  [FulfillmentTypes.IN_STORE]: {
    label: "In Store",
    value: FulfillmentTypes.IN_STORE,
  },
  [FulfillmentTypes.DELIVERY]: {
    label: "Delivery",
    value: FulfillmentTypes.DELIVERY,
  },
  [FulfillmentTypes.PICKUP]: {
    label: "Pickup",
    value: FulfillmentTypes.PICKUP,
  },
  [FulfillmentTypes.EXPRESS]: {
    label: "Express",
    value: FulfillmentTypes.EXPRESS,
  },
};

export const fulfillmentTypes = [
  FULFILLMENT_TYPES.IN_STORE,
  FULFILLMENT_TYPES.DELIVERY,
  FULFILLMENT_TYPES.PICKUP,
  FULFILLMENT_TYPES.EXPRESS,
];

export const CUSTOMER_LICENSE_TYPES = {
  [LicenseTypes.ADULT]: {
    displayName: "Adult",
    displayValue: LicenseTypes.ADULT,
  },
  [LicenseTypes.MEDICAL]: {
    displayName: "Medical",
    displayValue: LicenseTypes.MEDICAL,
  },
};

export const customerLicenseTypes = [
  CUSTOMER_LICENSE_TYPES.ADULT,
  CUSTOMER_LICENSE_TYPES.MEDICAL,
];

export const PURCHASE_AMOUNT_TYPES = {
  [PurchaseAmountTypes.GRANDTOTAL]: {
    displayName: "Grand Total",
    displayValue: PurchaseAmountTypes.GRANDTOTAL,
  },
  [PurchaseAmountTypes.SUBTOTAL]: {
    displayName: "Subtotal",
    displayValue: PurchaseAmountTypes.SUBTOTAL,
  },
};

export const purchaseAmountTypes = [
  PURCHASE_AMOUNT_TYPES.GRANDTOTAL,
  PURCHASE_AMOUNT_TYPES.SUBTOTAL,
];

export const BOGO_DISCOUNT_METHODS = {
  [BogoDiscountMethods.PERCENT]: {
    displayName: "% Off",
    displayValue: BogoDiscountMethods.PERCENT,
  },
  [BogoDiscountMethods.DOLLAR]: {
    displayName: "$ Off",
    displayValue: BogoDiscountMethods.DOLLAR,
  },
  [BogoDiscountMethods.TARGET_PRICE]: {
    displayName: "Price At Value",
    displayValue: BogoDiscountMethods.TARGET_PRICE,
  },
};

export const BUNDLE_DISCOUNT_METHODS = {
  [BundleDiscountMethods.PERCENT]: {
    displayName: "% Off",
    displayValue: BundleDiscountMethods.PERCENT,
  },
  [BundleDiscountMethods.DOLLAR]: {
    displayName: "$ Off",
    displayValue: BundleDiscountMethods.DOLLAR,
  },
  [BundleDiscountMethods.TARGET_PRICE]: {
    displayName: "Price At Value",
    displayValue: BundleDiscountMethods.TARGET_PRICE,
  },
};

export const BUNDLE_PURCHASE_REQUIREMENTS = {
  [BundlePurchaseRequirement.UNIT_COUNT]: {
    displayName: "Unit Count",
    displayValue: BundlePurchaseRequirement.UNIT_COUNT,
  },
  [BundlePurchaseRequirement.RETAIL_VALUE]: {
    displayName: "Retail Value",
    displayValue: BundlePurchaseRequirement.RETAIL_VALUE,
  },
};

export const defaultAutomatedDiscountFormValues: AutomatedDiscountFormData = {
  title: "",
  displayTitle: "",
  method: AUTOMATED_DISCOUNT_METHODS[AutomatedDiscountMethods.DOLLAR],
  amount: "",
  isActive: true,
  isManual: false,
  isStackable: false,
  customerGroups: [],
  conditions: {
    customerCapEnabled: false,
    customerCapValue: null,
    customerLimitEnabled: false,
    customerLimitValue: null,
    customerEventEnabled: false,
    customerEvents: { eventName: null, eventValue: null },
    customerGroupsEnabled: false,
    customerLicenseTypeEnabled: false,
    customerLicenseType: null,
    fulfillmentTypesEnabled: false,
    fulfillmentTypes: null,
    itemLimitEnabled: false,
    itemLimitValue: null,
    purchaseMinimumEnabled: false,
    purchaseMinimumType: null,
    purchaseMinimumValue: null,
    bogoConditions: null,
    bundleConditions: null,
    packageAgeEnabled: false,
  },
  collections: [],
  storeCustomizations: [],
  schedule: null,
  collectionsRequired: [],
  displayChannels: {
    ecommerce: true,
    customerFacing: true,
    sellTreez: true,
  },
  organizationId: '',
};

export const AUTOMATED_DISCOUNT_FORM_STEPS = {
  DISCOUNT_DETAILS: 'Discount Details',
  PRODUCT_COLLECTIONS: 'Select Product Collections',
  STORES: 'Select Stores',
  SCHEDULE: 'Schedule Discount',
  CONDITIONS: 'Set Conditions',
}