import React from "react";
import { Box, IconButton, styled } from "@mui/material/";
import { convertPxToRem, Icon } from "@treez-inc/component-library";
import ImageViewer from "./ImageViewer";

const ImgThumbnailWrapper = styled(Box)(() => ({
  display: "inline-flex",
  flexDirection: "column",
  margin: `${convertPxToRem(14)} 0`,
  cursor: "grab",
}));

const ImgThumbnail = styled(Box)(({ theme }) => ({
  border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
  borderRadius: convertPxToRem(16),
  cursor: "pointer",
  height: convertPxToRem(52),
  position: "relative",
  width: convertPxToRem(84),
  "&:hover": {
    ".imgIconBtn": {
      display: "inline-flex",
    },
  },
}));

const DeleteImgButton = styled(IconButton)(({ theme }) => ({
  border: `1px solid ${theme.palette.grey04.main}`,
  background: `${theme.palette.primaryWhite.main}`,
  display: "none",
  height: "24px",
  overflow: "hidden",
  position: "absolute",
  right: "3px",
  top: "-8px",
  width: "24px",

  "&:hover": {
    background: `${theme.palette.green03.main}`,
  },
}));

interface ImageSliderProps {
  imageUrl: string;
  onDeleteImage?: () => any;
  onSelectImage?: () => any;
}

const ImagePreview = ({
  imageUrl,
  onDeleteImage,
  onSelectImage,
}: ImageSliderProps) => {
  const handleImageDelete = () => {
    onDeleteImage?.();
  };

  const handleImageSelect = () => {
    onSelectImage?.();
  };

  return (
    <ImgThumbnailWrapper data-testId="image-preview-wrapper">
      <ImgThumbnail>
        <DeleteImgButton
          data-testId="image-preview-delete-button"
          className="imgIconBtn"
          onClick={() => handleImageDelete()}
        >
          <Icon iconName="Delete" fontSize="small" />
        </DeleteImgButton>
        <ImageViewer imageUrl={imageUrl} onClick={() => handleImageSelect()} />
      </ImgThumbnail>
    </ImgThumbnailWrapper>
  );
};

export default ImagePreview;
