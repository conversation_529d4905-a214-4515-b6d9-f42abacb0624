import { useReducer } from "react";
import ApiService from "../../services/api/apiService";
import { ErrorObject } from "../../interfaces/error";

const LOADING = "LOADING" as const;
const SUCCESS = "SUCCESS" as const;
const FAILURE = "FAILURE" as const;

// const ERROR_MSG = "There was a problem while saving data.";

export type TStateUseSave<Data> =
  | { loading: false; error: undefined; data: undefined }
  | { loading: true; error: undefined; data: undefined }
  | {
      loading: false;
      error: ErrorObject;
      data: undefined;
    }
  | { loading: false; error: undefined; data: Data };

export type THandlerUseSave<RequestData> = (payload: RequestData) => void;

type TActionUseSave<Data> =
  | { type: typeof LOADING }
  | { type: typeof SUCCESS; payload: Data }
  | { type: typeof FAILURE; payload: ErrorObject };

function reducer<Data>(
  _: TStateUseSave<Data>,
  action: TActionUseSave<Data>
): TStateUseSave<Data> {
  if (action.type === LOADING) {
    return {
      error: undefined,
      loading: true,
      data: undefined,
    };
  }
  if (action.type === SUCCESS) {
    return {
      error: undefined,
      loading: false,
      data: action.payload,
    };
  }
  return {
    error: action.payload,
    loading: false,
    data: undefined,
  };
}

const initialState: TStateUseSave<never> = {
  loading: false,
  error: undefined,
  data: undefined,
};

export type TRequestMethod = "post" | "put";

export default function useSave<RequestData, ResponseData>(
  apiInstance: ApiService,
  url: string,
  method: TRequestMethod
): [THandlerUseSave<RequestData>, TStateUseSave<ResponseData>] {
  const [state, dispatch] = useReducer(reducer<ResponseData>, initialState);

  const handleRequest: THandlerUseSave<RequestData> = async (requestData) => {
    try {
      dispatch({ type: LOADING });
      const { data } = await apiInstance[method](url, requestData);
      dispatch({ type: SUCCESS, payload: data });
    } catch (e: any) {
      dispatch({ type: FAILURE, payload: e.response });
      throw e;
    }
  };

  return [handleRequest, state];
}
