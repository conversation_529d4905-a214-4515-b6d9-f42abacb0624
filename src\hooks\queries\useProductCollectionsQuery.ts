import { useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { getProductCollectionsUrl } from "../../services/apiEndPoints";
import { api } from "../../services/api";
import { ProductCollectionResponse } from "../../interfaces/responseModels";

const useProductCollectionsQuery = (options = { includeDeleted: false }) =>
  useQuery({
    queryKey: ["productCollections"],
    queryFn: async () => {
      const result = await api.get(`${getProductCollectionsUrl}`);
      // response is an object that contains data key with array result and totalCount with number of items in data array
      return result.data.data as ProductCollectionResponse[];
    },
    select: useCallback(
      (data: ProductCollectionResponse[]) =>
        options.includeDeleted
          ? data
          : data.filter(({ deletedAt }) => !deletedAt),
      [options.includeDeleted]
    ),
  });

export default useProductCollectionsQuery;
