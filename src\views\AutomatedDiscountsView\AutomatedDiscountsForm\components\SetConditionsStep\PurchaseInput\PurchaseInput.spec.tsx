import React from "react";
import { fireEvent, render, renderHook, screen } from "@testing-library/react";
import { FieldValues, FormProvider, useForm } from "react-hook-form";
import { TreezThemeProvider } from "@treez-inc/component-library";
import PurchaseInput, { PurchaseInputProps } from ".";

const renderPurchaseInput = (
  { inputLabel, selectLabel }: Partial<PurchaseInputProps>,
  invalid = false
) => {
  const { result } = renderHook(() =>
    useForm({
      defaultValues: {
        testAmountInput: "",
        testSelectInput: "",
      } as FieldValues,
      mode: "onChange",
    })
  );

  const FormProviderWrapper: React.FC<{ children: React.ReactNode }> = ({
    children,
  }) => <FormProvider {...result.current}>{children}</FormProvider>;

  if (invalid) {
    result.current.setError("testAmountInput", {
      type: "required",
      message: "Min amount is required",
    });

    result.current.setError("testSelectInput", {
      type: "required",
      message: "An option is required",
    });
  }

  render(
    <TreezThemeProvider>
      <FormProviderWrapper>
        <PurchaseInput
          inputName="testAmountInput"
          inputLabel={inputLabel}
          selectName="testSelectInput"
          selectLabel={selectLabel}
          control={result.current.control}
          options={[
            { displayName: "Option 1", displayValue: "option1" },
            { displayName: "Option 2", displayValue: "option2" },
          ]}
        />
      </FormProviderWrapper>
    </TreezThemeProvider>
  );

  const { getByTestId, queryByText } = screen;

  const purchaseInputGroup = getByTestId("purchase-input");
  const amountInput = getByTestId("amount-input");
  const inputField = amountInput.getElementsByTagName("input")[0];
  const selectField = getByTestId("purchase-amount-select");
  const validationText = () => queryByText("Must be a valid dollar amount");

  return {
    purchaseInputGroup,
    amountInput,
    inputField,
    selectField,
    validationText,
  };
};

describe("PurchaseInput", () => {
  it("should render", () => {
    const { purchaseInputGroup } = renderPurchaseInput({});
    expect(purchaseInputGroup).toBeInTheDocument();
  });

  it("should render input as a required field", async () => {
    const { inputField } = renderPurchaseInput({});
    expect(inputField).toBeRequired();
  });

  it("should display default labels for input and select elements", () => {
    renderPurchaseInput({});
    expect(screen.getByText("Min Amount")).toBeInTheDocument();
    expect(screen.getByText("Purchase Amount Type")).toBeInTheDocument();
  });

  it("should display custom labels for input and select elements", () => {
    const customLabels = {
      inputLabel: "Custom Input Label",
      selectLabel: "Custom Select Label",
    };
    renderPurchaseInput(customLabels);
    expect(screen.getByText(customLabels.inputLabel)).toBeInTheDocument();
    expect(screen.getByText(customLabels.selectLabel)).toBeInTheDocument();
  });

  it("should display an error message when input is invalid", () => {
    renderPurchaseInput({}, true);
    expect(screen.getByText("Min amount is required")).toBeInTheDocument();
  });

  it("should display an error message when select is invalid", () => {
    renderPurchaseInput({}, true);
    expect(screen.getByText("An option is required")).toBeInTheDocument();
  });

  it("should not display an error message when the input is empty", () => {
    const { inputField } = renderPurchaseInput({}, true);
    fireEvent.change(inputField, {
      target: { value: "" },
    });
    expect(screen.queryByText("Amount is required")).not.toBeInTheDocument();
  });
});
