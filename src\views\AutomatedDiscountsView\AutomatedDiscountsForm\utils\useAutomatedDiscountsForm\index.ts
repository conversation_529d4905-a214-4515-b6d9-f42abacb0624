import { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import axios from "axios";
import { UseQueryResult, useQueryClient } from "@tanstack/react-query";
import { AutomatedDiscountMethods } from "constants/discounts";
import ApiService from "../../../../../services/api/apiService";
import {
  AutomatedDiscountFormData,
  DiscountConditionFormData,
} from "../../../../../interfaces/discounts";
import { EntityResponse } from "../../../../../interfaces/entity";
import { OrgDiscountReqBody } from "../../../../../interfaces/requestModels";
import {
  OrgDiscountResponse,
  OrgTags,
  ProductCollectionResponse,
  StoreCustomizationResponse,
} from "../../../../../interfaces/responseModels";
import {
  useCreateDiscountMutation,
  useDiscountByIdQuery,
  useUpdateDiscountMutation,
  useOrgTagGroupByName,
  useProductCollectionsQuery,
} from "../../../../../hooks";
import {
  getDateFromTime,
  parseDateString,
  parseJwt,
  truncateSnackbarMessage,
} from "../../../../../utils";
import { formatConditions } from "../formatConditions";
import { formatMethod } from "../formatMethod";
import { useSnackbar } from "../../../../../providers/SnackbarProvider";
import {
  AUTOMATED_DISCOUNT_METHODS,
  MANUAL_DISCOUNT_METHODS,
  defaultAutomatedDiscountFormValues as defaultValues,
  AUTOMATED_DISCOUNT_FORM_STEPS,
} from "../../../../../constants/discountForm";
import { automatedDiscountsPath } from "../../../../../constants/routes";
import { formatSchedule } from "../formatSchedule";

const getAmountFormatted = (method: string, amount: string) => {
  const value = parseFloat(amount).toFixed(2);

  if (method === MANUAL_DISCOUNT_METHODS.DOLLAR) {
    return Number(value).toString();
  }

  return value;
};

export const convertDiscountResponseToAutomatedDiscountForm = (
  orgDiscount: OrgDiscountResponse
): AutomatedDiscountFormData => ({
  id: orgDiscount.id,
  title: orgDiscount.title,
  displayTitle: orgDiscount.displayTitle,
  description: orgDiscount.description,
  method:
    AUTOMATED_DISCOUNT_METHODS[orgDiscount.method as AutomatedDiscountMethods],
  amount: getAmountFormatted(orgDiscount.method, orgDiscount.amount),
  isActive: orgDiscount.isActive,
  isManual: orgDiscount.isManual,
  imageUrl: orgDiscount.imageUrl,
  // handle cases where isStackable is somehow set to null on an automated discount
  isStackable: !!orgDiscount.isStackable,
  conditions: orgDiscount.conditions
    ? {
        ...orgDiscount.conditions,
        purchaseMinimumValue: orgDiscount.conditions.purchaseMinimumValue
          ? Number(orgDiscount.conditions.purchaseMinimumValue)
          : null,
        customerEvents: orgDiscount.conditions.customerEvents
          ? orgDiscount.conditions.customerEvents[0]
          : null,
      }
    : defaultValues.conditions,
  customerGroups: orgDiscount.customerGroups.map((tag) => ({
    ...tag,
    label: tag.tagName,
    value: tag.id,
  })),
  schedule: orgDiscount.schedule
    ? {
        ...orgDiscount.schedule,
        startDate: parseDateString(orgDiscount.schedule.startDate),
        endDate: orgDiscount.schedule.endDate
          ? parseDateString(orgDiscount.schedule.endDate)
          : null,
        startTime: orgDiscount.schedule.startTime
          ? getDateFromTime(orgDiscount.schedule.startTime)
          : null,
        endTime: orgDiscount.schedule.endTime
          ? getDateFromTime(orgDiscount.schedule.endTime)
          : null,
        customEndDate: orgDiscount.schedule.customEndDate
          ? parseDateString(orgDiscount.schedule.customEndDate)
          : null,
      }
    : null,
  collections: orgDiscount.collections,
  collectionsRequired: orgDiscount.collectionsRequired,
  storeCustomizations:
    orgDiscount.storeCustomizations &&
    orgDiscount.storeCustomizations.map(
      (store: StoreCustomizationResponse) => ({
        ...store,
        createdAt: new Date(store.createdAt),
        updatedAt: new Date(store.updatedAt),
        amount:
          store.amount !== null
            ? getAmountFormatted(orgDiscount.method, store.amount)
            : null,
      })
    ),
  displayChannels: {
    ecommerce: orgDiscount.showEcommerce,
    customerFacing: orgDiscount.showCustomerFacing,
    sellTreez: orgDiscount.showSellTreez,
  },
  organizationId: orgDiscount.organizationId,
});

export const useAutomatedDiscountsForm = (
  api: ApiService,
  setIsPageLoading: (isLoading: boolean) => void,
  entityList: EntityResponse[],
  discountId?: string | undefined
) => {
  const isEdit = !!discountId;
  const navigate = useNavigate();
  const decodedToken = parseJwt(api.getTokens().accessToken);
  const { openSnackbar } = useSnackbar();

  const useDiscountQuery: UseQueryResult | undefined = isEdit
    ? useDiscountByIdQuery(api, discountId)
    : undefined;

  const methods = useForm<AutomatedDiscountFormData>({
    defaultValues: {
      ...defaultValues,
      organizationId: decodedToken?.orgId,
    },
    values: useMemo(() => {
      if (useDiscountQuery?.isSuccess) {
        return convertDiscountResponseToAutomatedDiscountForm(
          useDiscountQuery.data as OrgDiscountResponse
        );
      }
      return undefined;
    }, [useDiscountQuery?.data]),
    mode: "onChange",
  });

  const [activeStep, setActiveStep] = useState<number>(0);

  const tryToGoStep = async (stepIdx: number) => {
    const canNavigate = isEdit ? await methods.trigger() : true;

    if (canNavigate) {
      setActiveStep(stepIdx);
    }
  };

  const handleNext = async () => {
    const isStepValid = await methods.trigger();
    if (isStepValid) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = async () => {
    const isStepValid = await methods.trigger();

    if (isStepValid) {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    }
  };

  const formSteps = [
    {
      label: AUTOMATED_DISCOUNT_FORM_STEPS.DISCOUNT_DETAILS,
      completed: isEdit,
      disabled: !isEdit,
      onClick: () => {},
    },
    {
      label: AUTOMATED_DISCOUNT_FORM_STEPS.PRODUCT_COLLECTIONS,
      completed: isEdit,
      disabled: !isEdit,
      onClick: () => {},
    },
    {
      label: AUTOMATED_DISCOUNT_FORM_STEPS.STORES,
      completed: isEdit,
      disabled: !isEdit,
      onClick: () => {},
    },
    {
      label: AUTOMATED_DISCOUNT_FORM_STEPS.SCHEDULE,
      completed: isEdit,
      disabled: !isEdit,
      onClick: () => {},
    },
    {
      label: AUTOMATED_DISCOUNT_FORM_STEPS.CONDITIONS,
      completed: isEdit,
      disabled: !isEdit,
      onClick: () => {},
    },
  ];

  const attachOnClickToSteps = (
    steps: {
      label: string;
      completed: boolean;
      disabled: boolean;
      onClick: () => void;
    }[]
  ) =>
    steps.map((step, index) => ({
      ...step,
      onClick: () => {
        tryToGoStep(index);
      },
    }));

  const [steps, setSteps] = useState(attachOnClickToSteps(formSteps));
  const storeCount = entityList.length || 0;
  const queryClient = useQueryClient();
  const mutateCreateDiscount = useCreateDiscountMutation({ api, queryClient });
  const mutateUpdateDiscount = useUpdateDiscountMutation({ api, queryClient });

  const tagGroupResponse: UseQueryResult<OrgTags[]> =
    useOrgTagGroupByName("Customer Group");

  const productCollectionsResponse: UseQueryResult<
    ProductCollectionResponse[]
  > = useProductCollectionsQuery();

  useEffect(() => {
    /** When submitting the form, this was removing the loader immediately */
    if (activeStep !== 4) {
      setIsPageLoading(
        (useDiscountQuery && useDiscountQuery?.isLoading) ||
          productCollectionsResponse?.isLoading ||
          tagGroupResponse.isLoading
      );
    }

    if (useDiscountQuery?.error && !useDiscountQuery?.isLoading) {
      navigate(automatedDiscountsPath);
      openSnackbar({
        severity: "error",
        message: "There was an error retrieving the discount details",
      });
    }

    if (productCollectionsResponse.isError) {
      navigate(automatedDiscountsPath);

      const { error } = productCollectionsResponse;
      let errorMessage = truncateSnackbarMessage(error.message);

      if (axios.isAxiosError(error) && error?.response?.status === 403) {
        errorMessage = "Product Collections: User does not have permission";
      }

      openSnackbar({
        severity: "error",
        message: errorMessage,
      });
      setIsPageLoading(false);
    }

    if (tagGroupResponse.isError) {
      openSnackbar({
        severity: "error",
        message: "There was an error retrieving the customer groups",
      });
      setIsPageLoading(false);
    }
  }, [useDiscountQuery, productCollectionsResponse, tagGroupResponse]);

  useEffect(() => {
    if (storeCount <= 1) {
      setSteps((prevSteps) =>
        attachOnClickToSteps(
          prevSteps.filter((step) => step.label !== "Select Stores")
        )
      );
    } else {
      setSteps(attachOnClickToSteps(formSteps));
    }
  }, [storeCount]);

  useEffect(() => {
    if (!isEdit) {
      setSteps((currentSteps) =>
        currentSteps.map((currentStep, i) => ({
          ...currentStep,
          disabled: i > activeStep,
          completed: i < activeStep,
        }))
      );
    }
  }, [activeStep]);

  const buildStoreCustomizationReqBody = (data: AutomatedDiscountFormData) => {
    if (storeCount === 1 && data.storeCustomizations.length === 0) {
      return [
        {
          isActive: null,
          entityId: entityList[0].id,
          entityName: entityList[0].name,
        },
      ];
    }
    return data.storeCustomizations.map((storeCustomization) => ({
      ...storeCustomization,
      isActive: null,
    }));
  };

  const onSubmit = async (data: AutomatedDiscountFormData) => {
    setIsPageLoading(true);

    const orgDiscount: OrgDiscountReqBody = {
      id: data.id,
      title: data.title,
      displayTitle: data.displayTitle,
      description: data.description,
      method: formatMethod(data.method),
      amount: data.amount,
      isActive: data.isActive,
      isAdjustment: false,
      isCart: false,
      isManual: false,
      customerGroups: data.customerGroups.map((tag) => ({
        tagId: tag.id !== tag.tagId ? tag.tagId : tag.value,
      })),
      conditions: formatConditions(
        data.conditions as DiscountConditionFormData,
        data.method
      ),
      schedule: data.schedule && formatSchedule(data.schedule),
      collections: data.collections.map((collection) => ({
        id: collection.id,
        productCollectionId: collection.productCollectionId,
        productCollectionName: collection.productCollectionName,
        orgDiscountId: collection.orgDiscountId,
      })),
      ...(data.method === AUTOMATED_DISCOUNT_METHODS.BOGO && {
        collectionsRequired: data.collectionsRequired.map((collection) => ({
          id: collection.id,
          productCollectionId: collection.productCollectionId,
          productCollectionName: collection.productCollectionName,
          orgDiscountId: collection.orgDiscountId,
        })),
      }),
      isStackable: data.isStackable,
      requireReason: false,
      requirePin: false,
      requireCoupon: false,
      showEcommerce: data.displayChannels.ecommerce,
      showCustomerFacing: data.displayChannels.customerFacing,
      showSellTreez: data.displayChannels.sellTreez,
      imageUrl: data.imageUrl,
      storeCustomizations: buildStoreCustomizationReqBody(data),
      organizationId: decodedToken?.orgId,
      externalIds: [],
      internalIds: [],
    };

    try {
      if (isEdit) {
        await mutateUpdateDiscount.mutateAsync(orgDiscount);
      } else {
        await mutateCreateDiscount.mutateAsync(orgDiscount);
      }

      openSnackbar({
        severity: "info",
        iconName: "Success",
        message: isEdit
          ? `"${orgDiscount.title}" discount has been updated`
          : `"${orgDiscount.title}" discount has been created`,
      });

      navigate(automatedDiscountsPath);
    } catch (error: any) {
      let message = isEdit
        ? "Unable to update the discount. Please try again"
        : "Unable to create the discount. Please try again";

      if (axios.isAxiosError(error)) {
        message = (
          error?.response?.data?.errorMsgs || [
            "Your request could not be completed due to a network error",
          ]
        ).join(", ");
      }

      openSnackbar({
        severity: "error",
        message: truncateSnackbarMessage(message),
      });

      setIsPageLoading(false);
    }
  };

  return {
    methods,
    activeStep,
    steps,
    existingDiscount: useDiscountQuery
      ? (useDiscountQuery.data as OrgDiscountResponse)
      : undefined,
    productCollections: productCollectionsResponse,
    customerGroups: tagGroupResponse.data,
    handleNext,
    handleBack,
    onSubmit,
  };
};
