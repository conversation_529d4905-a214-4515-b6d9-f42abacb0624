import { CustomerEvent } from "./discounts";
import {
  ConditionsResponse,
  ProductCollectionDiscountResponse,
  OrgDiscountResponse,
} from "./responseModels";

export interface OrgDiscountRow
  extends Omit<
    OrgDiscountResponse,
    | "createdAt"
    | "updatedAt"
    | "conditions"
    | "collections"
    | "collectionsRequired"
  > {
  createdAt: Date;
  updatedAt: Date;
  isChild: boolean;
  conditions?: ConditionsRowData | null;
  collections?: ProductCollectionDiscountResponse[] | null;
  collectionsRequired?: ProductCollectionDiscountResponse[] | null;
  hierarchy: string[];
  entityName?: string | undefined;
  parentId?: string;
  entityId?: string;
}

export interface ConditionsRowData extends Omit<ConditionsResponse, "customerEvents"> {
  customerEvents: CustomerEvent | null;
}

export interface KeyValueFilter {
  [field: string]: string | boolean | string[];
}
