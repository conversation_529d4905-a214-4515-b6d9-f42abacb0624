import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { MemoryRouter } from "react-router-dom";
import { TreezThemeProvider } from "@treez-inc/component-library";
import iconAriaLabels from "@treez-inc/component-library/dist/components/Icon/icon-library/icon-aria-labels";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import DiscountActionMenu, { DiscountActionMenuProps } from ".";
import {
  activeManualDiscount,
  activeManualDiscountChild,
  inactiveManualDiscount,
  testActiveAutomatedDiscountRow,
  testAutomatedDiscountChildRow,
} from "../../test/fixtures";

const mockNavigate = jest.fn();

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => mockNavigate,
}));

describe("DiscountActionMenu", () => {
  const mockOpenDiscountModal = jest.fn();
  const mockOpenDiscountLog = jest.fn();

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  const renderDiscountActionMenu = (
    props: Pick<DiscountActionMenuProps, "row">
  ) => {
    render(
      <MemoryRouter>
        <TreezThemeProvider>
          <QueryClientProvider client={queryClient}>
            <DiscountActionMenu
              {...props}
              testId="test-automated-discount-action-menu"
              openDiscountModal={mockOpenDiscountModal}
              openDiscountLog={mockOpenDiscountLog}
            />
          </QueryClientProvider>
        </TreezThemeProvider>
      </MemoryRouter>
    );

    const { getByTestId, queryByTestId, queryAllByRole } = screen;

    const menuButton = () => getByTestId("discount-action-menu");
    const childMenuButton = () => getByTestId("discount-action-child-menu");
    const menu = () => queryByTestId("discount-action-list");
    const menuItems = () => queryAllByRole("menuitem");
    const copyDiscountMenuItem = () => queryByTestId("copy-discount-menu-item");
    const editDiscountMenuItem = () => queryByTestId("edit-discount-menu-item");
    const deleteDiscountMenuItem = () =>
      queryByTestId("delete-discount-menu-item");
    const deactivateDiscountMenuItem = () =>
      queryByTestId("deactivate-discount-menu-item");
    const activateDiscountMenuItem = () =>
      queryByTestId("activate-discount-menu-item");
    const editStoreConditionsMenuItem = () =>
      queryByTestId("edit-store-conditions-menu-item");
    const unassignStoreConditionsMenuItem = () =>
      queryByTestId("unassign-store-conditions-menu-item");

    return {
      menuButton,
      childMenuButton,
      menu,
      menuItems,
      copyDiscountMenuItem,
      editDiscountMenuItem,
      deleteDiscountMenuItem,
      deactivateDiscountMenuItem,
      activateDiscountMenuItem,
      editStoreConditionsMenuItem,
      unassignStoreConditionsMenuItem,
    };
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders the discount action menu icon button when isChild = false", () => {
    const { menuButton } = renderDiscountActionMenu({
      row: activeManualDiscount,
    });
    const icon = menuButton().firstChild?.firstChild;

    expect(menuButton()).toBeInTheDocument();
    expect(icon).toHaveAccessibleName(iconAriaLabels.More);
  });

  it("renders the discount action menu child icon button when isChild = true", () => {
    const { childMenuButton } = renderDiscountActionMenu({
      row: activeManualDiscountChild,
    });
    const icon = childMenuButton().firstChild?.firstChild;

    expect(childMenuButton()).toBeInTheDocument();
    expect(icon).toHaveAccessibleName(iconAriaLabels.More);
  });

  it("should open the discount action menu when the icon button is clicked", async () => {
    const { menuButton, menu } = renderDiscountActionMenu({
      row: activeManualDiscount,
    });

    userEvent.click(menuButton());
    await waitFor(() => {
      expect(menu()).toBeVisible();
    });
  });

  it("should close the discount action menu when a menu item is clicked", async () => {
    const { menuButton, menu, menuItems } = renderDiscountActionMenu({
      row: activeManualDiscount,
    });

    userEvent.click(menuButton());
    await waitFor(() => {
      expect(menu()).toBeVisible();
    });

    userEvent.click(menuItems()[0]);

    await waitFor(() => {
      expect(menu()).not.toBeInTheDocument();
    });
  });

  describe("Manual Discount", () => {
    it("renders the DiscountActionMenu", () => {
      renderDiscountActionMenu({
        row: activeManualDiscount,
      });
    });

    it("should render the correct menu items when isChild = false", async () => {
      const {
        menuButton,
        copyDiscountMenuItem,
        editDiscountMenuItem,
        deleteDiscountMenuItem,
        deactivateDiscountMenuItem,
        activateDiscountMenuItem,
        editStoreConditionsMenuItem,
        unassignStoreConditionsMenuItem,
      } = renderDiscountActionMenu({
        row: activeManualDiscount,
      });

      userEvent.click(menuButton());

      await waitFor(() => {
        expect(copyDiscountMenuItem()).toBeInTheDocument();
        expect(editDiscountMenuItem()).toBeInTheDocument();
        expect(deleteDiscountMenuItem()).toBeInTheDocument();
        expect(deactivateDiscountMenuItem()).toBeInTheDocument();
        expect(activateDiscountMenuItem()).not.toBeInTheDocument();

        expect(editStoreConditionsMenuItem()).not.toBeInTheDocument();
        expect(unassignStoreConditionsMenuItem()).not.toBeInTheDocument();
      });
    });

    it("should render the correct menu items when isChild = true", async () => {
      const {
        childMenuButton,
        copyDiscountMenuItem,
        editDiscountMenuItem,
        deleteDiscountMenuItem,
        deactivateDiscountMenuItem,
        activateDiscountMenuItem,
        editStoreConditionsMenuItem,
        unassignStoreConditionsMenuItem,
      } = renderDiscountActionMenu({
        row: activeManualDiscountChild,
      });

      userEvent.click(childMenuButton());

      await waitFor(() => {
        expect(editStoreConditionsMenuItem()).toBeInTheDocument();
        expect(unassignStoreConditionsMenuItem()).toBeInTheDocument();

        expect(copyDiscountMenuItem()).not.toBeInTheDocument();
        expect(editDiscountMenuItem()).not.toBeInTheDocument();
        expect(deleteDiscountMenuItem()).not.toBeInTheDocument();
        expect(deactivateDiscountMenuItem()).not.toBeInTheDocument();
        expect(activateDiscountMenuItem()).not.toBeInTheDocument();
      });
    });

    describe("isChild = false", () => {
      it("should call copyDiscountId() when the copy discount menu item is clicked", async () => {
        const mockWriteText = jest.fn();
        Object.assign(navigator, {
          clipboard: {
            writeText: mockWriteText,
          },
        });

        const { menuButton, copyDiscountMenuItem } = renderDiscountActionMenu({
          row: activeManualDiscount,
        });

        userEvent.click(menuButton());
        await waitFor(() => {
          expect(copyDiscountMenuItem()).toBeInTheDocument();
        });
        // @ts-ignore
        userEvent.click(copyDiscountMenuItem());
        await waitFor(() => {
          expect(mockWriteText).toHaveBeenCalledWith("100");
        });
      });
    });

    it("should call navigate when the edit menu item is clicked", async () => {
      const { menuButton, editDiscountMenuItem } = renderDiscountActionMenu({
        row: activeManualDiscount,
      });

      userEvent.click(menuButton());
      await waitFor(() => {
        expect(editDiscountMenuItem()).toBeInTheDocument();
      });

      userEvent.click(editDiscountMenuItem()!);
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith(
          `/manual/edit/${activeManualDiscount.id}`
        );
      });
    });

    it("should call openDiscountModal when the delete menu item is clicked", async () => {
      const { menuButton, menu, deleteDiscountMenuItem } =
        renderDiscountActionMenu({
          row: activeManualDiscount,
        });

      userEvent.click(menuButton());
      await waitFor(() => {
        expect(menu()).toBeVisible();
        expect(deleteDiscountMenuItem()).toBeInTheDocument();
      });

      userEvent.click(deleteDiscountMenuItem()!);
      await waitFor(() => {
        const { createdAt, updatedAt, ...rest } = activeManualDiscount;
        expect(mockOpenDiscountModal).toHaveBeenCalledWith("delete", {
          discount: rest,
        });
      });
    });

    describe("Active/Inactive", () => {
      it("should call openDiscountModal when the deactivate menu item is clicked", async () => {
        const { menuButton, menu, deactivateDiscountMenuItem } =
          renderDiscountActionMenu({
            row: activeManualDiscount,
          });

        userEvent.click(menuButton());
        await waitFor(() => {
          expect(menu()).toBeVisible();
          expect(deactivateDiscountMenuItem()).toBeInTheDocument();
        });

        userEvent.click(deactivateDiscountMenuItem()!);
        await waitFor(() => {
          const { createdAt, updatedAt, ...rest } = activeManualDiscount;

          expect(mockOpenDiscountModal).toHaveBeenCalledWith("deactivate", {
            discount: rest,
          });
        });
      });

      it("should call openDiscountModal when the activate menu item is clicked", async () => {
        const { menuButton, activateDiscountMenuItem } =
          renderDiscountActionMenu({
            row: inactiveManualDiscount,
          });

        userEvent.click(menuButton());
        await waitFor(() => {
          expect(activateDiscountMenuItem()).toBeInTheDocument();
        });

        userEvent.click(activateDiscountMenuItem()!);
        await waitFor(() => {
          const { createdAt, updatedAt, ...rest } = inactiveManualDiscount;

          expect(mockOpenDiscountModal).toHaveBeenCalledWith("activate", {
            discount: rest,
          });
        });
      });
    });

    describe("isChild = true", () => {
      it("should navigate to the edit page when the 'Edit Store Conditions' menu item is clicked", async () => {
        const { childMenuButton, editStoreConditionsMenuItem } =
          renderDiscountActionMenu({
            row: activeManualDiscountChild,
          });

        userEvent.click(childMenuButton());
        await waitFor(() => {
          expect(editStoreConditionsMenuItem()).toBeInTheDocument();
        });

        userEvent.click(editStoreConditionsMenuItem()!);
        await waitFor(() => {
          expect(mockNavigate).toHaveBeenCalledWith(
            `/manual/edit/${activeManualDiscountChild.parentId}/stores/${activeManualDiscountChild.id}`
          );
        });
      });

      it("should call openDiscountModal when the 'Unassign Store Conditions' menu item is clicked", async () => {
        const { childMenuButton, unassignStoreConditionsMenuItem } =
          renderDiscountActionMenu({
            row: activeManualDiscountChild,
          });

        userEvent.click(childMenuButton());
        await waitFor(() => {
          expect(unassignStoreConditionsMenuItem()).toBeInTheDocument();
        });

        userEvent.click(unassignStoreConditionsMenuItem()!);
        await waitFor(() => {
          expect(mockOpenDiscountModal).toHaveBeenCalledWith("unassign", {
            parentDiscountId: activeManualDiscountChild.parentId,
            storeToUnassignId: activeManualDiscountChild.id,
          });
        });
      });
    });
  });

  describe("Automated Discount", () => {
    it("should render the correct menu items when isChild = false", async () => {
      const {
        menuButton,
        editDiscountMenuItem,
        deleteDiscountMenuItem,
        deactivateDiscountMenuItem,
        activateDiscountMenuItem,
      } = renderDiscountActionMenu({
        row: testActiveAutomatedDiscountRow,
      });

      userEvent.click(menuButton());

      await waitFor(() => {
        expect(editDiscountMenuItem()).toBeInTheDocument();
        expect(deleteDiscountMenuItem()).toBeInTheDocument();
        expect(deactivateDiscountMenuItem()).toBeInTheDocument();
        expect(activateDiscountMenuItem()).not.toBeInTheDocument();
      });
    });

    it("should not render a menu button when isChild = true", async () => {
      renderDiscountActionMenu({
        row: testAutomatedDiscountChildRow,
      });

      expect(screen.queryByTestId("discount-action-child-menu")).toEqual(null);
    });
  });
});
