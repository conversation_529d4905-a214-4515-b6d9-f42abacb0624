import { AUTOMATED_DISCOUNT_METHODS } from "../../../../../constants/discountForm";
import { CustomerEvents } from "../../../../../constants/discounts";
import { DiscountConditionFormData } from "../../../../../interfaces/discounts";
import { DiscountConditionsReqBody } from "../../../../../interfaces/requestModels";

export const formatConditions = (
  conditions: DiscountConditionFormData,
  method?: string
): DiscountConditionsReqBody => {
  if (Object.keys(conditions).length === 0) {
    return {} as DiscountConditionsReqBody;
  }

  return {
    ...conditions,
    customerCapValue: conditions.customerCapEnabled
      ? conditions.customerCapValue
      : null,
    customerLimitValue: conditions.customerLimitEnabled
      ? conditions.customerLimitValue
      : null,
    purchaseMinimumValue: conditions.purchaseMinimumEnabled
      ? conditions.purchaseMinimumValue
      : null,
    itemLimitValue: conditions.itemLimitEnabled
      ? conditions.itemLimitValue
      : null,
    fulfillmentTypes: conditions.fulfillmentTypesEnabled
      ? conditions.fulfillmentTypes
      : null,
    customerLicenseType: conditions.customerLicenseTypeEnabled
      ? conditions.customerLicenseType
      : null,
    // Must be in array as backend is structured to future-proof for situations where multiple customer events can be selected
    customerEvents: conditions.customerEventEnabled
      ? [
        {
          eventName: conditions.customerEvents!.eventName,
          eventValue:
            conditions.customerEvents!.eventName ===
              CustomerEvents.VISIT_NUMBER
              ? conditions.customerEvents!.eventValue
              : null,
        },
      ]
      : null,
    bogoConditions:
      method === AUTOMATED_DISCOUNT_METHODS.BOGO
        ? conditions.bogoConditions
        : null,
    bundleConditions:
      method === AUTOMATED_DISCOUNT_METHODS.BUNDLE
        ? conditions.bundleConditions
        : null,
  };
};
