import React from "react";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import { TreezThemeProvider } from "@treez-inc/component-library";
import {
  defaultAutomatedDiscountFormValues as defaultValues,
  FULFILLMENT_TYPES,
} from "../../../../../constants/discountForm";
import SetConditionsStep, { ConditionValue } from ".";
import { PackageAgeTypes } from "../../../../../constants/discounts";

describe("SetConditionsStep", () => {
  const renderConditionsStep = () => {
    const FormProviderWrapper: React.FC<{ children: React.ReactNode }> = ({
      children,
    }) => {
      const methods = useForm({ defaultValues });
      return <FormProvider {...methods}>{children}</FormProvider>;
    };

    render(
      <TreezThemeProvider>
        <FormProviderWrapper>
          <SetConditionsStep />
        </FormProviderWrapper>
      </TreezThemeProvider>
    );

    const { getByTestId } = screen;

    const setConditionsStepContainer = getByTestId(
      "automated-set-conditions-step"
    );

    const conditionsList = getByTestId("conditions-list");

    return {
      setConditionsStepContainer,
      conditionsList,
    };
  };

  const selectCondition = ({
    testId,
    title,
  }: Pick<ConditionValue, "title"> & { testId: string }) => {
    const { conditionsList } = renderConditionsStep();

    fireEvent.click(conditionsList);

    const conditionCheckbox = screen.getByText(title);
    fireEvent.click(conditionCheckbox);

    const accordionPanel = screen.getByTestId(testId);

    const eventNameInput = accordionPanel.getElementsByTagName("input")[0];

    const accordionButtonBase =
      accordionPanel.querySelector('div[role="button"]');

    return {
      conditionCheckbox,
      accordionPanel,
      eventNameInput,
      accordionButtonBase,
    };
  };

  const verifyConditionIsRendered = async ({
    testId,
    title,
    subtitle,
    expectedChildText,
  }: Pick<ConditionValue, "title" | "subtitle"> & {
    testId: string;
    expectedChildText: string;
  }) => {
    const { accordionPanel } = selectCondition({ testId, title });

    await waitFor(() => {
      expect(accordionPanel).toBeInTheDocument();
      expect(accordionPanel).toHaveTextContent(title);
      expect(accordionPanel).toHaveTextContent(subtitle);
      expect(accordionPanel).toHaveTextContent(expectedChildText);
    });
  };

  const verifyConditionIsNotRendered = async ({
    testId,
    title,
  }: Pick<ConditionValue, "title"> & { testId: string }) => {
    const { conditionCheckbox, accordionPanel } = selectCondition({
      testId,
      title,
    });

    await waitFor(() => {
      expect(accordionPanel).toBeInTheDocument();
    });

    fireEvent.click(conditionCheckbox);

    await waitFor(() => {
      expect(accordionPanel).not.toBeInTheDocument();
    });
  };

  const verifyConditionIsExpanded = async ({
    testId,
    title,
  }: Pick<ConditionValue, "title"> & { testId: string }) => {
    const { accordionButtonBase } = selectCondition({
      testId,
      title,
    });

    await waitFor(() => {
      expect(accordionButtonBase).toBeInTheDocument();
    });

    expect(accordionButtonBase).toHaveAttribute("aria-expanded", "true");
  };

  it("should render the automated-set-conditions-step container", () => {
    const { setConditionsStepContainer } = renderConditionsStep();
    expect(setConditionsStepContainer).toBeInTheDocument();
  });

  it("should render the conditions list", () => {
    const { conditionsList } = renderConditionsStep();
    expect(conditionsList).toBeInTheDocument();
  });

  describe("Customer Cap", () => {
    it("should render the panel when the Customer Cap checkbox is selected", async () => {
      verifyConditionIsRendered({
        testId: "customer-cap-condition-accordion-panel",
        title: "Customer Cap",
        subtitle:
          "Number of customers who can use this discount before it's automatically disabled",
        expectedChildText: "Max Count",
      });
    });

    it("should not render the panel when the Customer Cap checkbox is not selected", async () => {
      verifyConditionIsNotRendered({
        testId: "customer-cap-condition-accordion-panel",
        title: "Customer Cap",
      });
    });

    it("should display condition as expanded by default after is selected from the list", async () => {
      await verifyConditionIsExpanded({
        testId: "customer-cap-condition-accordion-panel",
        title: "Customer Cap",
      });
    });
  });

  describe("Per-Customer Limit", () => {
    it("should render the panel when the Per-Customer Limit checkbox is selected", async () => {
      verifyConditionIsRendered({
        testId: "customer-limit-condition-accordion-panel",
        title: "Per-Customer Limit",
        subtitle:
          "Number of times this discount can be applied per customer before it's automatically disabled",
        expectedChildText: "Max Count",
      });
    });

    it("should not render the panel when the Per-Customer Limit checkbox is not selected", async () => {
      verifyConditionIsNotRendered({
        testId: "customer-limit-condition-accordion-panel",
        title: "Per-Customer Limit",
      });
    });

    it("should display condition as expanded by default after is selected from the list", async () => {
      await verifyConditionIsExpanded({
        testId: "customer-limit-condition-accordion-panel",
        title: "Per-Customer Limit",
      });
    });
  });

  describe("Customer Event", () => {
    it("should render the panel when the Customer Event checkbox is selected", async () => {
      await verifyConditionIsRendered({
        testId: "customer-event-condition-accordion-panel",
        title: "Customer Event",
        subtitle: "Automatically apply this discount at a customer milestone",
        expectedChildText: "Select Event",
      });
    });

    it("should not render the panel when the Customer Event checkbox is not selected", async () => {
      await verifyConditionIsNotRendered({
        testId: "customer-event-condition-accordion-panel",
        title: "Customer Event",
      });
    });

    it("should display condition as expanded by default after is selected from the list", async () => {
      await verifyConditionIsExpanded({
        testId: "customer-event-condition-accordion-panel",
        title: "Customer Event",
      });
    });

    describe("Which Purchase", () => {
      it("should render which purchase field input when customer event is VISIT_NUMBER", () => {
        const { eventNameInput } = selectCondition({
          testId: "customer-event-condition-accordion-panel",
          title: "Customer Event",
        });

        fireEvent.change(eventNameInput, {
          target: { value: "VISIT_NUMBER" },
        });

        const whichPurchaseField = screen.queryByText("Which Purchase");
        expect(whichPurchaseField).toBeInTheDocument();
      });

      it("should not render which purchase field input when customer event is not VISIT_NUMBER", () => {
        const { eventNameInput } = selectCondition({
          testId: "customer-event-condition-accordion-panel",
          title: "Customer Event",
        });

        fireEvent.change(eventNameInput, {
          target: { value: "BIRTHDAY" },
        });

        const whichPurchaseField = screen.queryByText("Which Purchase");
        expect(whichPurchaseField).not.toBeInTheDocument();
      });

      it("should display last value if customer event is changed and returns to VISIT_NUMBER", () => {
        const { accordionPanel, eventNameInput } = selectCondition({
          testId: "customer-event-condition-accordion-panel",
          title: "Customer Event",
        });

        fireEvent.change(eventNameInput, {
          target: { value: "VISIT_NUMBER" },
        });

        const eventValueInput = accordionPanel.getElementsByTagName("input")[1];

        fireEvent.change(eventValueInput, {
          target: { value: "100" },
        });

        fireEvent.change(eventNameInput, {
          target: { value: "BIRTHDAY" },
        });

        fireEvent.change(eventNameInput, {
          target: { value: "VISIT_NUMBER" },
        });

        expect(eventValueInput).toHaveValue("100");
      });
    });
  });

  describe("Customer Group", () => {
    it("should render the panel when the Customer Group checkbox is selected", async () => {
      await verifyConditionIsRendered({
        testId: "customer-group-condition-accordion-panel",
        title: "Customer Group",
        subtitle: "Group of customers who can use this discount",
        expectedChildText: "Customer Group",
      });
    });

    it("should not render the panel when the Customer Group checkbox is not selected", async () => {
      await verifyConditionIsNotRendered({
        testId: "customer-group-condition-accordion-panel",
        title: "Customer Group",
      });
    });

    it("should display condition as expanded by default after is selected from the list", async () => {
      await verifyConditionIsExpanded({
        testId: "customer-group-condition-accordion-panel",
        title: "Customer Group",
      });
    });
  });

  describe("Customer Type", () => {
    it("should render the panel when the Customer Type checkbox is selected", async () => {
      await verifyConditionIsRendered({
        testId: "customer-type-condition-accordion-panel",
        title: "Customer Type",
        subtitle: "Types of customers who can use this discount",
        expectedChildText: "Customer Type",
      });
    });

    it("should not render the panel when the Customer Type checkbox is not selected", async () => {
      await verifyConditionIsNotRendered({
        testId: "customer-type-condition-accordion-panel",
        title: "Customer Type",
      });
    });

    it("should display condition as expanded by default after is selected from the list", async () => {
      await verifyConditionIsExpanded({
        testId: "customer-type-condition-accordion-panel",
        title: "Customer Type",
      });
    });
  });

  describe("Fulfillment Type", () => {
    it("should render the panel when the Fulfillment Type checkbox is selected", async () => {
      await verifyConditionIsRendered({
        testId: "fulfillment-type-condition-accordion-panel",
        title: "Fulfillment Type",
        subtitle: "Types of orders this discount can be applied to",
        expectedChildText: FULFILLMENT_TYPES.IN_STORE.label,
      });
    });

    it("should not render the panel when the Fulfillment Type checkbox is not selected", async () => {
      await verifyConditionIsNotRendered({
        testId: "fulfillment-type-condition-accordion-panel",
        title: "Fulfillment Type",
      });
    });

    it("should display condition as expanded by default after is selected from the list", async () => {
      await verifyConditionIsExpanded({
        testId: "fulfillment-type-condition-accordion-panel",
        title: "Fulfillment Type",
      });
    });
  });

  describe("Redemption Limit", () => {
    it("should render the panel when the Redemption Limit checkbox is selected", async () => {
      await verifyConditionIsRendered({
        testId: "redemption-limit-condition-accordion-panel",
        title: "Redemption Limit",
        subtitle:
          "Number of times the discount can be applied to a single transaction",
        expectedChildText: "Max Count",
      });
    });

    it("should not render the panel when the Redemption Limit checkbox is not selected", async () => {
      await verifyConditionIsNotRendered({
        testId: "redemption-limit-condition-accordion-panel",
        title: "Redemption Limit",
      });
    });

    it("should display condition as expanded by default after is selected from the list", async () => {
      await verifyConditionIsExpanded({
        testId: "redemption-limit-condition-accordion-panel",
        title: "Redemption Limit",
      });
    });
  });

  describe("Package age", () => {
    it("should render the panel when the Redemption Limit checkbox is selected", async () => {
      await verifyConditionIsRendered({
        testId: "package-age-condition-accordion-panel",
        title: "Package Age",
        subtitle:
          "Packages that meet the package age criteria within the selected product collections will be discounted.",
        expectedChildText: "Select Package Date Type",
      });
    });

    it("should not render the panel when the Redemption Limit checkbox is not selected", async () => {
      await verifyConditionIsNotRendered({
        testId: "package-age-condition-accordion-panel",
        title: "Package Age",
      });
    });

    it("should display condition as expanded by default after is selected from the list", async () => {
      await verifyConditionIsExpanded({
        testId: "package-age-condition-accordion-panel",
        title: "Package Age",
      });
    });

    describe("Min # of Days Old", () => {
      it("should render Min # of Days Old input field if package age type is selected", async () => {
        const { eventNameInput } = selectCondition({
          testId: "package-age-condition-accordion-panel",
          title: "Package Age",
        });

        fireEvent.change(eventNameInput, {
          target: { value: PackageAgeTypes.RECEIVED },
        });

        const minNumberOfDaysInput = screen.queryByText("Min # of Days Old");

        await waitFor(() => {
          expect(minNumberOfDaysInput).toBeInTheDocument();
        });
      });

      it("should not display Min # of Days Old if package age type is not selected", async () => {
        await verifyConditionIsRendered({
          testId: "package-age-condition-accordion-panel",
          title: "Package Age",
          subtitle:
            "Packages that meet the package age criteria within the selected product collections will be discounted.",
          expectedChildText: "Select Package Date Type",
        });

        const minNumberOfDaysInput = screen.queryByText("Min # of Days Old");

        await waitFor(() => {
          expect(minNumberOfDaysInput).not.toBeInTheDocument();
        });
      });
    });
  });

  describe("Purchase Minimum", () => {
    it("should render the panel when the Purchase Minimum checkbox is selected", async () => {
      await verifyConditionIsRendered({
        testId: "purchase-minimum-condition-accordion-panel",
        title: "Purchase Minimum",
        subtitle: "Minimum order $ amount before this discount can be applied",
        expectedChildText: "Min Amount *Purchase Amount Type",
      });
    });

    it("should not render the panel when the Purchase Minimum checkbox is not selected", async () => {
      await verifyConditionIsNotRendered({
        testId: "purchase-minimum-condition-accordion-panel",
        title: "Purchase Minimum",
      });
    });

    it("should display condition as expanded by default after is selected from the list", async () => {
      await verifyConditionIsExpanded({
        testId: "purchase-minimum-condition-accordion-panel",
        title: "Purchase Minimum",
      });
    });
  });
});
