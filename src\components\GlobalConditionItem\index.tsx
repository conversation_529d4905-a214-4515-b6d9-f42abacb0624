import React from "react";
import { styled, Grid, Typography } from "@mui/material";
import { convertPxToRem } from "@treez-inc/component-library";

interface SummaryItemProps {
  label: string;
  value: string | undefined;
}

const StyledInputItem = styled(Grid)({
  display: "flex",
  flexDirection: "column",
});

const StyledValueTypography = styled(Typography)({
  marginTop: convertPxToRem(16),
});

const GlobalConditionItem: React.FC<SummaryItemProps> = ({ label, value }) => (
  <StyledInputItem item xs={12} lg={12} md={12} sm={12}>
    <Typography variant="smallText">{label}</Typography>

    <StyledValueTypography variant="largeText" color="grey06">
      {value || "N/A"}
    </StyledValueTypography>
  </StyledInputItem>
);

export default GlobalConditionItem;
