import React from "react";
import { Box, Grid, styled, Typography } from "@mui/material";
import {
  Checkbox,
  convertPxToRem,
  DatePicker,
  Icon,
  Panel,
  Select,
  Switch,
  Tooltip,
} from "@treez-inc/component-library";
import { Controller, useFormContext } from "react-hook-form";
import dayjs from "dayjs";
import {
  AutomatedDiscountFormData,
  CustomEndType,
  CustomRepeatEvery,
  RepeatType,
  Schedule,
} from "../../../../../interfaces/discounts";
import TimePicker from "../../../../../components/TimePicker";
import {
  combineDateAndTime,
  getDayOfYear,
  getDayOfMonth,
  getWeekDay,
  getWeekNumberStr,
} from "../../../../../utils";

import CustomRecurrenceForm, {
  toCustomRepeatDaysOfWeek,
} from "./CustomRecurrenceForm";

const StyledScheduleDiscountStep = styled(Box)(() => ({
  minWidth: convertPxToRem(280),
  maxWidth: convertPxToRem(620),
}));

const StyledTitleElement = styled(Box)({
  display: "flex",
  flexDirection: "row",
  marginLeft: convertPxToRem(-184),
  marginRight: convertPxToRem(8),
  minWidth: convertPxToRem(160),
});

const StyledFirstOccurrenceStartEndContainer = styled(Box)({
  display: "flex",
  flexDirection: "column",
  gap: convertPxToRem(10),
  marginBottom: convertPxToRem(12),
  "> div": {
    marginBottom: convertPxToRem(8),
  },
});

const StyledStartEndPickerContainer = styled(Grid)({
  display: "flex",
  flexDirection: "row",
  gap: convertPxToRem(12),
  marginBottom: convertPxToRem(12),
  ".MuiTextField-root": {
    width: "100%",
  },
});

const StyledRecurrenceContainer = styled(Grid)({
  gap: convertPxToRem(10),
  marginTop: convertPxToRem(20),
});

const StyledTooltipContainer = styled(Box)({
  display: "flex",
  alignItems: "center",
});

const StyledTooltipIcon = styled(Box)({
  margin: 4,
  marginTop: 6,
});

const MissingInputSpacer = styled(Box)({
  width: "100%",
});

export const SCHEDULE_FIELD = "schedule";

export const defaultSchedule = () =>
  ({
    startDate: new Date(),
    allDay: true,
    startTime: null,
    endDate: null,
    endTime: null,
    spansMultipleDays: false,
    repeatType: RepeatType.DO_NOT,
    customRepeatIntervalCount: null,
    customRepeatEvery: null,
    customRepeatDaysOfWeek: null,
    customEndType: null,
    customEndDate: null,
    customEndRepeatCount: null,
  } as Schedule);

const SPANS_MULTIPLE_DAYS_TOOLTIP_TEXT =
  "A discount that spans multiple days applies continuously from the start date into the next consecutive day(s) without any breaks in between. For example, this includes a deal that runs all day from Friday to Sunday. A daily happy hour with set times or Monday-only deal is NOT a multi-day discount because the first occurrence ends the same day it started.";

const getRepeatTypeMenuItems = (startDate: Date | undefined) => {
  const validDate = startDate && startDate.toString() !== "Invalid Date";

  /** start date has a default value, will only be invalid if manually deleting part of the date */
  /** these placehodler texts will rarely be seen, but handle an edge case */

  return [
    {
      displayName: "Does not repeat",
      displayValue: RepeatType.DO_NOT,
    },
    {
      displayName: "Daily",
      displayValue: RepeatType.DAY,
    },
    {
      displayName: validDate ? `Weekly on ${getWeekDay(startDate)}` : "Weekly",
      displayValue: RepeatType.WEEK,
    },
    {
      displayName: validDate
        ? `Monthly on ${getWeekNumberStr(startDate)} ${getWeekDay(startDate)}`
        : "Monthly on same day of week",
      displayValue: RepeatType.MONTH,
    },
    {
      displayName: validDate
        ? `Monthly on ${getDayOfMonth(startDate)}`
        : "Monthly on same day of the month",
      displayValue: RepeatType.MONTH_DAY,
    },
    {
      displayName: validDate
        ? `Anually on ${getDayOfYear(startDate)}`
        : "Annually",
      displayValue: RepeatType.ANNUAL,
    },
    {
      displayName: "Every Weekday",
      displayValue: RepeatType.WEEK_DAY,
    },
    {
      displayName: "Custom",
      displayValue: RepeatType.CUSTOM,
    },
  ];
};

export const validateStartDateVsEndDateOfSchedule = (schedule: Schedule) => {
  const { startDate, startTime, endDate, endTime, spansMultipleDays } =
    schedule;

  if (!spansMultipleDays && startTime && endTime) {
    if (endTime.getTime() < startTime.getTime()) {
      return "End time needs to be after start time";
    }
  }

  if (startDate && !startTime && endDate && !endTime) {
    if (endDate.getTime() < startDate.getTime()) {
      return "End date needs to be after start date";
    }
  }

  if (startDate && startTime && endDate && endTime) {
    const startDateTime = combineDateAndTime(startDate, startTime);
    const endDateTime = combineDateAndTime(endDate, endTime);

    if (endDateTime.getTime() < startDateTime.getTime()) {
      return "End date time needs to be after start date time";
    }
  }

  return true;
};

const ScheduleDiscountStep = () => {
  const { control, setValue, getValues, trigger } =
    useFormContext<AutomatedDiscountFormData>();

  const handleScheduleChange = (checked: boolean | undefined) => {
    if (checked) {
      setValue(SCHEDULE_FIELD, defaultSchedule());
    } else {
      setValue(SCHEDULE_FIELD, null);
    }
  };

  const validateValidDate = (fieldValue: Date | null) => {
    if (fieldValue && fieldValue.toString() === "Invalid Date") {
      return "Invalid Date";
    }

    return true;
  };

  const validateValidTime = (fieldValue: Date | null) => {
    if (fieldValue && fieldValue.toString() === "Invalid Date") {
      return "Invalid Time";
    }

    return true;
  };

  const validateStartDateVsEndDate = (fieldValue: any) => {
    const { schedule } = getValues();

    if (fieldValue && schedule) {
      return validateStartDateVsEndDateOfSchedule(schedule);
    }

    return true;
  };

  const handleScheduleFieldChange = (fieldName: string, value: any) => {
    const newSchedule = {
      ...getValues().schedule,
      [fieldName]: value,
    } as Schedule;

    setValue(SCHEDULE_FIELD, newSchedule);
  };

  const fillDefaultCustomRecurrenceFields = () => {
    handleScheduleFieldChange("customEndType", CustomEndType.NEVER);

    handleScheduleFieldChange("customRepeatIntervalCount", 1);

    handleScheduleFieldChange("customRepeatEvery", CustomRepeatEvery.WEEK);

    handleScheduleFieldChange(
      "customRepeatDaysOfWeek",
      toCustomRepeatDaysOfWeek([])
    );

    handleScheduleFieldChange("customEndRepeatCount", null);

    handleScheduleFieldChange("customEndDate", null);
  };

  const clearCustomRecurrenceFields = () => {
    handleScheduleFieldChange("customEndType", null);

    handleScheduleFieldChange("customRepeatIntervalCount", null);

    handleScheduleFieldChange("customRepeatEvery", null);

    handleScheduleFieldChange("customRepeatDaysOfWeek", null);

    handleScheduleFieldChange("customEndRepeatCount", null);

    handleScheduleFieldChange("customEndDate", null);
  };

  return (
    <Controller
      name="schedule"
      control={control}
      render={({ field: { value: schedule } }) => (
        <StyledScheduleDiscountStep data-testid="automated-schedule-discount-step">
          <Panel
            testId="automated-schedule-discount-panel"
            title="Schedule Discount"
            subtitle="Schedule discounts like recurring meetings. Set dates and times in your store's local timezone. Discounts without a set schedule will be in effect once saved and will continue indefinitely."
            titleElement={
              <StyledTitleElement>
                <Switch
                  value="On"
                  checked={schedule !== null}
                  label="Add Schedule"
                  onChange={handleScheduleChange}
                  testId="automated-schedule-discount-switch"
                />
              </StyledTitleElement>
            }
          >
            {schedule && (
              <div>
                <StyledFirstOccurrenceStartEndContainer data-testid="first-occurrence-start">
                  <Typography variant="largeText">
                    First Occurrence Start
                  </Typography>
                  <Checkbox
                    label="All Day"
                    value="allDay"
                    testId="all-day-checkbox"
                    checked={schedule.allDay}
                    onChange={(checked: boolean | undefined) => {
                      handleScheduleFieldChange("allDay", checked);
                      if (!checked) {
                        handleScheduleFieldChange("startTime", new Date());
                      } else {
                        handleScheduleFieldChange("startTime", null);
                        handleScheduleFieldChange("endTime", null);
                      }
                    }}
                  />
                  <StyledStartEndPickerContainer data-testid="start-pickers">
                    <Controller
                      name="schedule.startDate"
                      control={control}
                      rules={{
                        required: {
                          value: true,
                          message: "Date is Required",
                        },
                        validate: { validateValidDate },
                      }}
                      render={({ fieldState: { error } }) => (
                        <DatePicker
                          label="Start Date"
                          testId="start-date-picker"
                          value={schedule.startDate}
                          onChange={(value) => {
                            handleScheduleFieldChange("startDate", value.$d);

                            trigger(["schedule.startDate"]);
                          }}
                          error={!!error}
                          helperText={error?.message || ""}
                        />
                      )}
                    />
                    {!schedule.allDay && (
                      <Controller
                        name="schedule.startTime"
                        control={control}
                        rules={{
                          required: {
                            value: true,
                            message: "Start Time is Required",
                          },
                          validate: { validateValidTime },
                        }}
                        render={({ fieldState: { error } }) => (
                          <TimePicker
                            label="Start Time"
                            testId="start-time-picker"
                            value={
                              schedule.startTime && dayjs(schedule.startTime)
                            }
                            onChange={(value) => {
                              handleScheduleFieldChange("startTime", value.$d);
                              trigger([
                                "schedule.startTime",
                                "schedule.endTime",
                              ]);
                            }}
                            helperText={error?.message || ""}
                            error={!!error}
                          />
                        )}
                      />
                    )}
                  </StyledStartEndPickerContainer>
                </StyledFirstOccurrenceStartEndContainer>

                <StyledFirstOccurrenceStartEndContainer>
                  <Typography variant="largeText">
                    First Occurrence End
                  </Typography>
                  <StyledTooltipContainer>
                    <Checkbox
                      label="Spans multiple days"
                      value="spansMultipleDays"
                      testId="spans-multiple-days-checkbox"
                      checked={schedule.spansMultipleDays}
                      onChange={(checked: boolean | undefined) => {
                        handleScheduleFieldChange("spansMultipleDays", checked);

                        if (!checked) {
                          handleScheduleFieldChange("endDate", null);
                          handleScheduleFieldChange("endTime", null);
                        }
                      }}
                    />
                    <Tooltip
                      variant="multiRow"
                      title={SPANS_MULTIPLE_DAYS_TOOLTIP_TEXT}
                    >
                      <StyledTooltipIcon>
                        <Icon iconName="InfoOutlined" />
                      </StyledTooltipIcon>
                    </Tooltip>
                  </StyledTooltipContainer>
                  <StyledStartEndPickerContainer data-testid="end-pickers">
                    {schedule.spansMultipleDays ? (
                      <Controller
                        name="schedule.endDate"
                        control={control}
                        rules={{
                          required: {
                            value: true,
                            message: "End Date is Required",
                          },
                          validate: {
                            validateStartDateVsEndDate,
                            validateValidDate,
                          },
                        }}
                        render={({ fieldState: { error } }) => (
                          <DatePicker
                            label="End Date"
                            testId="end-date-picker"
                            value={schedule.endDate}
                            onChange={(value) => {
                              handleScheduleFieldChange("endDate", value.$d);

                              trigger(["schedule.endDate", "schedule.endTime"]);
                            }}
                            helperText={error?.message || ""}
                            error={!!error}
                          />
                        )}
                      />
                    ) : (
                      <MissingInputSpacer />
                    )}
                    {!schedule.allDay && (
                      <Controller
                        name="schedule.endTime"
                        control={control}
                        rules={{
                          required: {
                            value: true,
                            message: "End Time is Required",
                          },
                          validate: {
                            validateValidTime,
                            validateStartDateVsEndDate,
                          },
                        }}
                        render={({ fieldState: { error } }) => (
                          <TimePicker
                            label="End Time"
                            testId="end-time-picker"
                            value={schedule.endTime && dayjs(schedule.endTime)}
                            onChange={(value) => {
                              handleScheduleFieldChange("endTime", value.$d);
                              trigger(["schedule.endDate", "schedule.endTime"]);
                            }}
                            helperText={error?.message || ""}
                            error={!!error}
                          />
                        )}
                      />
                    )}
                  </StyledStartEndPickerContainer>
                </StyledFirstOccurrenceStartEndContainer>

                <StyledRecurrenceContainer>
                  <Typography variant="largeText">Recurrence</Typography>

                  <Controller
                    name="schedule.repeatType"
                    control={control}
                    render={() => (
                      <Select
                        label="Recurrence"
                        value={schedule.repeatType}
                        testId="schedule-recurrence-type-select"
                        onChange={(event) => {
                          const { value } = event.target;

                          handleScheduleFieldChange("repeatType", value);

                          if (value === RepeatType.CUSTOM) {
                            fillDefaultCustomRecurrenceFields();
                          } else {
                            clearCustomRecurrenceFields();
                          }
                        }}
                        menuItems={getRepeatTypeMenuItems(
                          getValues().schedule?.startDate
                        )}
                      />
                    )}
                  />
                  {schedule.repeatType === RepeatType.CUSTOM && (
                    <CustomRecurrenceForm />
                  )}
                </StyledRecurrenceContainer>
              </div>
            )}
          </Panel>
        </StyledScheduleDiscountStep>
      )}
    />
  );
};

export default ScheduleDiscountStep;
