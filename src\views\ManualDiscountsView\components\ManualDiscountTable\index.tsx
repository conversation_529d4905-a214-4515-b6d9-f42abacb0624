import React, { useEffect, useState } from "react";
import {
  convertPxToRem,
  DataGridPro,
  Icon,
  IconButton,
  StaticChip,
} from "@treez-inc/component-library";
import {
  GridColDef,
  DataGridProProps,
  useGridApiContext,
  GridRowId,
  GridTreeNodeWithRender,
  useGridApiRef,
  useGridSelector,
  gridFilteredDescendantCountLookupSelector,
} from "@mui/x-data-grid-pro";
import { styled, Box, Typography, ButtonProps } from "@mui/material";
import { GridApiPro } from "@mui/x-data-grid-pro/models/gridApiPro";
import { MANUAL_DISCOUNT_METHODS } from "../../../../constants/discountForm";
import DiscountStatusBox from "../../../../components/DiscountStatusBox";
import TruncatedTooltip from "../../../../components/TruncatedTooltip";
import { OrgDiscountResponse } from "../../../../interfaces/responseModels";
import { OrgDiscountRow } from "../../../../interfaces/table";
import { buildRowsWithHierarchy, storeCellRenderer } from "../../../../utils";
import DiscountActionMenu from "../../../../components/DiscountActionMenu";

interface ManualDiscountTableProps {
  discounts: OrgDiscountResponse[];
  openDiscountModal: (
    type: string,
    {
      discount,
      parentDiscountId,
      storeToUnassignId,
    }: {
      discount?: OrgDiscountResponse;
      parentDiscountId?: string;
      storeToUnassignId?: string;
    }
  ) => void;
  openDiscountLog: (discount: OrgDiscountResponse) => void;
}

const ManualDiscountTableContainer = styled(Box)(() => ({
  height: "100%",
  display: "flex",
  flexDirection: "column",
  paddingBottom: convertPxToRem(1),
}));

const CustomGroupGridTreeContainer = styled(Box)(() => ({
  display: "flex",
}));

function CustomGridTreeDataGroupingCellActionMenu(props: {
  id: GridRowId;
  field: string;
  row: OrgDiscountRow;
  rowNode: GridTreeNodeWithRender;
  openDiscountModal: (
    type: string,
    {
      discount,
      parentDiscountId,
      storeToUnassignId,
    }: {
      discount?: OrgDiscountResponse;
      parentDiscountId?: string;
      storeToUnassignId?: string;
    }
  ) => void;
  openDiscountLog: (discount: OrgDiscountResponse) => void;
}) {
  const { id, field, row, rowNode, openDiscountModal, openDiscountLog } = props;

  const apiRef = useGridApiContext();
  const [isExpanded, setExpanded] = useState(
    rowNode.type === "group" && rowNode.childrenExpanded
  );

  const filteredDescendantCountLookup = useGridSelector(
    apiRef,
    gridFilteredDescendantCountLookupSelector
  );

  const handleClick: ButtonProps["onClick"] = (event) => {
    if (rowNode.type !== "group") {
      return;
    }

    apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
    apiRef.current.setCellFocus(id, field);
    setExpanded(!rowNode.childrenExpanded);
    event.stopPropagation();
  };

  useEffect(() => {
    if (rowNode.type !== "group") {
      return;
    }

    if (isExpanded !== rowNode.childrenExpanded) {
      apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
      apiRef.current.setCellFocus(id, field);
    }
  }, [rowNode]);

  return (
    <CustomGroupGridTreeContainer key={`grid-tree-container-${id}`}>
      <DiscountActionMenu
        row={row}
        openDiscountModal={openDiscountModal}
        openDiscountLog={openDiscountLog}
        testId={`discount-action-menu-${id}	`}
      />
      {filteredDescendantCountLookup[rowNode.id] > 0 && (
        <IconButton
          iconName={
            rowNode.type === "group" && rowNode.childrenExpanded
              ? "ChevronUp"
              : "ChevronRight"
          }
          onClick={handleClick}
          testId="expanddiscountrow-button"
          variant="secondary"
        />
      )}
    </CustomGroupGridTreeContainer>
  );
}

const commonHeaderConfig: {} = {
  align: "left",
  headerAlign: "left",
  editable: false,
  renderHeader: (params: {
    colDef: {
      headerName: string;
    };
  }) => (
    <Typography variant="mediumTextStrong">
      {params.colDef.headerName}
    </Typography>
  ),
  renderCell: (params: any) => (
    <Typography variant="largeText" color="grey08" noWrap>
      {(() => params.value)()}
    </Typography>
  ),
};

const dataTableColumns: GridColDef[] = [
  {
    field: "title",
    headerName: "Name",
    ...commonHeaderConfig,
    flex: 1,
    minWidth: 200,
    renderCell: (params: any) => (
      <TruncatedTooltip>{params.row.title}</TruncatedTooltip>
    ),
  },
  {
    field: "amount",
    headerName: "Amount",
    ...commonHeaderConfig,
    flex: 0.125,
    minWidth: 80,
    valueGetter: ({ value }) => {
      if (!value) {
        return 0;
      }
      return Number(value);
    },
    renderCell: (params: any) => {
      const amount = parseFloat(params.row.amount).toFixed(2);
      if (params.row.method === MANUAL_DISCOUNT_METHODS.DOLLAR) {
        return `$${amount}`;
      }

      if (params.row.method === MANUAL_DISCOUNT_METHODS.PERCENT) {
        return `${amount}%`;
      }

      return `$${amount}`;
    },
  },
  {
    field: "storeCustomizations",
    headerName: "Stores",
    ...commonHeaderConfig,
    flex: 0.125,
    minWidth: 160,
    sortComparator: (v1, v2) => {
      if (v1.length === 1 && v2.length === 1) {
        return v1[0].entityName.localeCompare(v2[0].entityName, undefined, {
          numeric: true,
        });
      }
      return v1.length - v2.length;
    },
    renderCell: storeCellRenderer,
  },
  {
    field: "isCart",
    headerName: "Type",
    ...commonHeaderConfig,
    flex: 0.125,
    minWidth: 120,
    renderCell: (params: any) => (
      <StaticChip
        variant="filled"
        label={params.row.isCart ? "Cart" : "Line Item"}
        color={params.row.isCart ? "peach" : "yellow"}
      />
    ),
  },
  {
    field: "requireReason",
    headerName: "Reason",
    ...commonHeaderConfig,
    minWidth: 120,
    headerAlign: "center",
    align: "center",
    renderCell: (params: any) =>
      params.row.requireReason ? (
        <Icon iconName="Checkmark" />
      ) : (
        <Icon iconName="Remove" fontSize="extraSmall" color="secondaryText" />
      ),
  },
  {
    field: "requirePin",
    headerName: "Manager Pin",
    ...commonHeaderConfig,
    minWidth: 120,
    headerAlign: "center",
    align: "center",
    renderCell: (params: any) =>
      params.row.requirePin ? (
        <Icon iconName="Checkmark" />
      ) : (
        <Icon iconName="Remove" fontSize="extraSmall" color="secondaryText" />
      ),
  },
  {
    field: "isActive",
    headerName: "Discount Status",
    ...commonHeaderConfig,
    minWidth: 130,
    headerAlign: "center",
    align: "center",
    renderCell: (params: any) => (
      <DiscountStatusBox isActive={params.row.isActive} />
    ),
  },
  {
    field: "createdAt",
    headerName: "Created Date",
    ...commonHeaderConfig,
    flex: 0.125,
    minWidth: 120,
    renderCell: (params: any) =>
      new Date(params.row.createdAt).toLocaleDateString("en-US", {
        month: "2-digit",
        day: "2-digit",
        year: "numeric",
      }),
  },
  {
    field: "updatedAt",
    headerName: "Last Update",
    renderCell: (params: any) =>
      new Date(params.row.updatedAt).toLocaleDateString("en-US", {
        month: "2-digit",
        day: "2-digit",
        year: "numeric",
      }),
  },
];

const ManualDiscountTable: React.FC<ManualDiscountTableProps> = ({
  discounts,
  openDiscountModal,
  openDiscountLog,
}) => {
  const tableColumns = dataTableColumns;
  const rows = buildRowsWithHierarchy(discounts);

  const getTreeDataPath: DataGridProProps["getTreeDataPath"] = (row) =>
    row.hierarchy;
  const getRowClassName: DataGridProProps["getRowClassName"] = (params) =>
    params.row.isChild ? "DataGrid-Row-Child" : "";

  const groupingColDef: DataGridProProps["groupingColDef"] = {
    headerName: "",
    width: 100,
    resizable: false,
    renderCell: (params) => (
      <CustomGridTreeDataGroupingCellActionMenu
        id={params.id}
        row={params.row}
        rowNode={params.rowNode}
        field={params.field}
        openDiscountModal={openDiscountModal}
        openDiscountLog={openDiscountLog}
      />
    ),
    align: "left",
  };

  const apiRef: React.MutableRefObject<GridApiPro> = useGridApiRef();

  return (
    <ManualDiscountTableContainer data-testid="manual-discount-list-container">
      <Typography variant="largeTextStrong" data-testid="count-discounts">
        {discounts.length} Discounts
      </Typography>
      <DataGridPro
        apiRef={apiRef}
        onFilterModelChange={() => {
          apiRef?.current?.setPage(0);
        }}
        columns={tableColumns}
        rows={rows}
        rowCount={discounts.length}
        hideFooterRowCount
        rowSpacingType="border"
        treeData
        groupingColDef={groupingColDef}
        getTreeDataPath={getTreeDataPath}
        getRowClassName={getRowClassName}
      />
    </ManualDiscountTableContainer>
  );
};

export default React.memo(ManualDiscountTable);
