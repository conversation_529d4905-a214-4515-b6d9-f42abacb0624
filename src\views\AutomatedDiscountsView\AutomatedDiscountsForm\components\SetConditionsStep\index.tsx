import React, { useEffect, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { Box, Typography, styled } from "@mui/material";
import { Input, Select, convertPxToRem } from "@treez-inc/component-library";
import DropdownSelect, {
  DropdownSelectOptionProps,
} from "../../../../../components/DropdownSelect";
import {
  CUSTOMER_EVENTS,
  conditionsDropdownSelect,
  customerEvents as customerEventsList,
  customerLicenseTypes,
  fulfillmentTypes,
  purchaseAmountTypes,
  packageAgeTypes,
} from "../../../../../constants/discountForm";
import MaxCountInput from "./MaxCountInput";
import CheckboxInput from "./CheckboxInput";
import PurchaseInput from "./PurchaseInput";
import AccordionPanel from "./AccordionPanel";
import AutocompleteInput from "./AutoCompleteInput";
import { CustomerGroup } from "../../../../../interfaces/discounts";
import { OrgTags } from "../../../../../interfaces/responseModels";

export interface ConditionValue {
  title: string;
  subtitle: string;
  children: React.ReactNode;
}

interface SetConditionsStepProps {
  customerGroups?: Array<OrgTags & CustomerGroup>;
}

const conditionsEnabledProps: {
  [key: string]: string;
} = {
  "customer-cap-condition": "customerCapEnabled",
  "customer-limit-condition": "customerLimitEnabled",
  "customer-event-condition": "customerEventEnabled",
  "customer-group-condition": "customerGroupsEnabled",
  "customer-type-condition": "customerLicenseTypeEnabled",
  "fulfillment-type-condition": "fulfillmentTypesEnabled",
  "redemption-item-limit-condition": "itemLimitEnabled",
  "purchase-minimum-condition": "purchaseMinimumEnabled",
  "package-age-condition": "packageAgeEnabled",
};

const StyledConditionsStepWrapper = styled(Box)(({ theme }) => ({
  border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
  borderRadius: convertPxToRem(16),
  padding: convertPxToRem(16),
  minWidth: convertPxToRem(280),
  maxWidth: convertPxToRem(620),
}));

const StyledSetConditionsContainer = styled(Box)({
  display: "flex",
});

const StyledColumnContent = styled(Box)({
  margin: convertPxToRem(16),
  marginTop: convertPxToRem(8),
});

const StyledInputWrapper = styled("div")({
  maxWidth: convertPxToRem(300),
});

const StyledInputContainer = styled(Box)({
  display: "grid",
  gridTemplateColumns: "1fr 1fr",
  gap: convertPxToRem(16),
});

const SetConditionsStep = ({ customerGroups }: SetConditionsStepProps) => {
  const { control, setValue, getValues, watch } = useFormContext();
  const [selectedConditions, setSelectedConditions] = useState<string[]>([]);
  const customerEventType = watch("conditions.customerEvents.eventName");
  const packageAgeType = watch("conditions.packageAgeType");

  const [conditionsList, setConditionsList] = useState<
    DropdownSelectOptionProps[]
  >(conditionsDropdownSelect);

  const accordionData: Record<string, ConditionValue> = {
    "customer-cap-condition": {
      title: "Customer Cap",
      subtitle:
        "Number of customers who can use this discount before it's automatically disabled",
      children: (
        <MaxCountInput
          label="Max Count"
          name="conditions.customerCapValue"
          control={control}
        />
      ),
    },
    "customer-limit-condition": {
      title: "Per-Customer Limit",
      subtitle:
        "Restricts the number of times a discount can be applied per order and per customer, managed at the store level",
      children: (
        <MaxCountInput
          label="Max Count"
          name="conditions.customerLimitValue"
          control={control}
        />
      ),
    },
    "customer-event-condition": {
      title: "Customer Event",
      subtitle: "Automatically apply this discount at a customer milestone",
      children: (
        <StyledInputContainer>
          <Controller
            name="conditions.customerEvents.eventName"
            control={control}
            rules={{ required: "Event selection is required" }}
            render={({ field, fieldState: { error } }) => {
              const { ref, value, ...rest } = field;
              return (
                <Select
                  {...rest}
                  value={value || ""}
                  testId="customer-event-select"
                  label="Select Event"
                  menuItems={customerEventsList}
                  helperText={error?.message ? error.message : ""}
                  error={!!error}
                  required
                />
              );
            }}
          />
          {customerEventType === CUSTOMER_EVENTS.VISIT_NUMBER.displayValue && (
            <Controller
              name="conditions.customerEvents.eventValue"
              control={control}
              rules={{
                value: true,
                required: "Which purchase is required",
                pattern: {
                  value: /^[0-9]*$/,
                  message: "Which purchase cannot be in decimals or letters",
                },
              }}
              render={({ field, fieldState: { error } }) => {
                const { ref, value, ...rest } = field;
                return (
                  <StyledInputWrapper>
                    <Input
                      {...rest}
                      value={value || ""}
                      type="number"
                      label="Which Purchase"
                      error={!!error}
                      helperText={error?.message}
                      required
                    />
                  </StyledInputWrapper>
                );
              }}
            />
          )}
        </StyledInputContainer>
      ),
    },
    "customer-group-condition": {
      title: "Customer Group",
      subtitle: "Group of customers who can use this discount",
      children: (
        <AutocompleteInput
          name="customerGroups"
          label="Customer Group"
          control={control}
          isOptionEqualToValue={(option, value) => option.label === value.label}
          options={
            customerGroups?.map((tag) => ({
              label: tag.name,
              value: tag.id,
            })) || []
          }
        />
      ),
    },
    "customer-type-condition": {
      title: "Customer Type",
      subtitle: "Types of customers who can use this discount",
      children: (
        <StyledInputWrapper>
          <Controller
            name="conditions.customerLicenseType"
            control={control}
            rules={{ required: "Customer type is required" }}
            render={({ field, fieldState: { error } }) => {
              const { ref, value, ...rest } = field;
              return (
                <Select
                  {...rest}
                  value={value || ""}
                  testId="customer-event-select"
                  label="Select Type"
                  menuItems={customerLicenseTypes}
                  helperText={error?.message ? error.message : ""}
                  error={!!error}
                  required
                />
              );
            }}
          />
        </StyledInputWrapper>
      ),
    },
    "fulfillment-type-condition": {
      title: "Fulfillment Type",
      subtitle: "Types of orders this discount can be applied to",
      children: (
        <CheckboxInput
          name="conditions.fulfillmentTypes"
          control={control}
          options={fulfillmentTypes}
        />
      ),
    },
    "redemption-item-limit-condition": {
      title: "Redemption Item Limit",
      subtitle:
        "Number of times the discount can be applied to a single transaction",
      children: (
        <MaxCountInput
          label="Max Count"
          name="conditions.itemLimitValue"
          control={control}
        />
      ),
    },
    "purchase-minimum-condition": {
      title: "Purchase Minimum",
      subtitle: "Minimum order $ amount before this discount can be applied",
      children: (
        <PurchaseInput
          inputName="conditions.purchaseMinimumValue"
          selectName="conditions.purchaseMinimumType"
          control={control}
          options={purchaseAmountTypes}
        />
      ),
    },
    "package-age-condition": {
      title: "Package Age",
      subtitle:
        "Packages that meet the package age criteria within the selected product collections will be discounted.",
      children: (
        <StyledInputContainer>
          <Controller
            name="conditions.packageAgeType"
            control={control}
            rules={{ required: "Package date type selection is required" }}
            render={({ field, fieldState: { error } }) => {
              const { ref, value, ...rest } = field;
              return (
                <Select
                  {...rest}
                  value={value || ""}
                  testId="package-date-type-select"
                  label="Select Package Date Type"
                  menuItems={packageAgeTypes}
                  helperText={error?.message ? error.message : ""}
                  error={!!error}
                  required
                />
              );
            }}
          />
          {packageAgeType && (
            <Controller
              name="conditions.packageAgeDaysOld"
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "Min # of days old is required",
                },
                min: {
                  value: 1,
                  message: "Please enter number greater than 0",
                },
                pattern: {
                  value: /^[0-9]*$/,
                  message: "Count cannot be in decimals",
                },
              }}
              render={({ field, fieldState: { error } }) => {
                const { ref, value, ...rest } = field;
                return (
                  <StyledInputWrapper data-testid="package-age-days-old-input">
                    <Input
                      {...rest}
                      testId="min-number-of-days-old-input"
                      value={value || ""}
                      label="Min # of Days Old"
                      helperText={error?.message ? error.message : ""}
                      error={!!error}
                      required
                      type="number"
                    />
                  </StyledInputWrapper>
                );
              }}
            />
          )}
        </StyledInputContainer>
      ),
    },
  };

  const onConditionSelected = (ids: string[]) => {
    setSelectedConditions(ids);

    Object.keys(conditionsEnabledProps).forEach((conditionId) => {
      const propName = conditionsEnabledProps[conditionId];
      const isEnabled = ids.includes(conditionId);

      if (propName && propName.endsWith("Enabled")) {
        setValue(`conditions.${propName}`, ids.includes(conditionId));
      }

      if (propName && propName === "packageAgeEnabled") {
        if (!isEnabled) {
          setValue(`conditions.packageAgeType`, null);
          setValue(`conditions.packageAgeDaysOld`, null);
        }
      }

      if (propName && propName === "customerGroupsEnabled") {
        if (!isEnabled) {
          setValue(`customerGroups`, []);
        }
      }
    });
  };

  useEffect(() => {
    const values = getValues();
    const updatedSelectedConditions: string[] = [];

    Object.entries(conditionsEnabledProps).forEach(
      ([conditionId, propName]) => {
        if (
          propName &&
          propName.endsWith("Enabled") &&
          values.conditions[propName]
        ) {
          updatedSelectedConditions.push(conditionId);
        }
      }
    );

    const updatedConditionsList = conditionsList.map((condition) => ({
      ...condition,
      checked: updatedSelectedConditions.includes(condition.key),
    }));

    setConditionsList(updatedConditionsList);
    setSelectedConditions(updatedSelectedConditions);
  }, [getValues, conditionsEnabledProps]);

  return (
    <StyledConditionsStepWrapper data-testid="automated-set-conditions-step">
      <StyledSetConditionsContainer>
        <StyledColumnContent>
          <Typography variant="h6">Set Conditions</Typography>
          <Typography variant="mediumText" color="secondaryText">
            Apply conditions to determine when this discount becomes eligible
            for checkout at your organization stores. Tailor its availability
            based on specific criteria.
          </Typography>
        </StyledColumnContent>
        <StyledColumnContent>
          <DropdownSelect
            testId="conditions-list"
            label="Set conditions"
            data={conditionsList}
            onChange={onConditionSelected}
          />
        </StyledColumnContent>
      </StyledSetConditionsContainer>
      {selectedConditions.map((conditionId) => (
        <AccordionPanel
          testId={conditionId}
          key={conditionId}
          title={accordionData[conditionId].title}
          subtitle={accordionData[conditionId].subtitle}
          expanded
        >
          {accordionData[conditionId].children}
        </AccordionPanel>
      ))}
    </StyledConditionsStepWrapper>
  );
};

export default SetConditionsStep;
