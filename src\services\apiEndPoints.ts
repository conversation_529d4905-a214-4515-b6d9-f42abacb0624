const domainUrl = process.env.DOMAIN_URL;
const organizationBaseUrl = process.env.ORGANIZATION_API_BASE_URL;
const discountsBaseUrl = process.env.DISCOUNTS_API_BASE_URL;
const collectionsApiVersion = process.env.COLLECTIONS_API_VERSION;
export const entityListUrl = (orgId?: string) =>
  `${domainUrl}/${organizationBaseUrl}/organizations/${orgId}/entities`;
export const upsertDiscountUrl = `${domainUrl}/${discountsBaseUrl}/org-discounts`;
export const listDiscountUrl = `${domainUrl}/${discountsBaseUrl}/org-discounts/list`;
export const getProductCollectionsUrl = `${domainUrl}/product-collection/${collectionsApiVersion}`;
export const deleteDiscountUrl = (orgDiscountId: string) =>
  `${domainUrl}/${discountsBaseUrl}/org-discounts/${orgDiscountId}`;
export const getDiscountByIdUrl = (orgDiscountId: string) =>
  `${domainUrl}/${discountsBaseUrl}/org-discounts/${orgDiscountId}`;
export const getDiscountLogHistoryByIdUrl = (orgDiscountId: string) =>
  `${domainUrl}/${discountsBaseUrl}/org-discounts/${orgDiscountId}/log-history`;
export const orgFeatureFlagsUrl = ({
  orgId,
  featureFlag,
}: {
  orgId: string;
  featureFlag?: string;
}) =>
  featureFlag
    ? `${domainUrl}/${organizationBaseUrl}/organizations/${orgId}/feature-flag?service=${featureFlag}`
    : `${domainUrl}/${organizationBaseUrl}/organizations/${orgId}/feature-flag`;
export const getOrgTagGroupUrl = `${domainUrl}/${discountsBaseUrl}/organization/tags/group`;
