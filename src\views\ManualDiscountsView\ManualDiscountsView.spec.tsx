import React from "react";
import { screen, waitFor, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ManualDiscountsView from ".";
import ApiService from "../../services/api/apiService";
import renderWithProviders from "../../test/renderWithProviders";
import { tokens, entities, testDiscountsResponse } from "../../test/constants";

const mockNavigate = jest.fn();
jest.mock("react-router-dom", () => ({
  ...(jest.requireActual("react-router-dom") as any),
  useNavigate: () => mockNavigate,
}));

jest.mock("../../hooks/queries/useGetEntities");
const mockUseGetEntities =
  require("../../hooks/queries/useGetEntities").default;

const clearTokens = () => {};

const apiService = new ApiService(() => tokens, clearTokens);

const writeText = jest.fn();

Object.assign(navigator, {
  clipboard: {
    writeText,
  },
});

beforeEach(() => {
  mockUseGetEntities.mockReturnValue([
    {
      error: null,
      loading: false,
      data: entities,
    },
  ]);
});

afterEach(() => {
  jest.clearAllMocks();
});

describe("ManualDiscountsView", () => {
  const renderManualDiscounts = (writePermission = true) => {
    const { getByTestId, queryAllByTestId, queryByTestId } = screen;

    const testQueryClient = renderWithProviders(
      <ManualDiscountsView
        api={apiService}
        permissions={{ read: true, write: writePermission }}
      />,
      {
        route: "/",
      }
    );

    const pageHeader = getByTestId("manual-discounts-page-header");
    const pageHeaderButton = getByTestId("add-discount-button");
    const container = getByTestId("manual-discount-table-wrapper");
    const loadingSpinner = () => queryByTestId("loading-spinner");
    const getCountDiscounts = () => getByTestId("count-discounts");
    const storeFilterDropdownButton = () =>
      getByTestId("filterstores-dropdown");
    const expandButtons = () => queryAllByTestId("expanddiscountrow-button");
    const firstExpandButton = expandButtons()[0];
    const discountActionMenus = () => queryAllByTestId("discount-action-menu");
    const discountActionChildMenus = () =>
      queryAllByTestId("discount-action-child-menu");

    return {
      pageHeader,
      pageHeaderButton,
      container,
      loadingSpinner,
      getCountDiscounts,
      storeFilterDropdownButton,
      expandButtons,
      firstExpandButton,
      discountActionMenus,
      discountActionChildMenus,
      testQueryClient,
    };
  };

  it("should show the discount list wrapper", async () => {
    const { container } = renderManualDiscounts();

    expect(container).toBeInTheDocument();
    expect(container).toBeDefined();
  });

  it("should render the page header", async () => {
    const { pageHeader } = renderManualDiscounts();

    expect(pageHeader).toBeInTheDocument();
    expect(pageHeader).toBeDefined();
  });

  it("should navigate to add discount page on button click", async () => {
    const { pageHeaderButton } = renderManualDiscounts();
    await userEvent.click(pageHeaderButton);
    expect(mockNavigate).toHaveBeenCalledWith("add");
  });

  it("should not navigate to add discount page on button click when user lacks write permissions", async () => {
    const { pageHeaderButton } = renderManualDiscounts(false);
    fireEvent.click(pageHeaderButton);

    await waitFor(() => {
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  it("should show the count of discounts in the header", async () => {
    const { container, getCountDiscounts } = renderManualDiscounts();

    expect(container).toBeInTheDocument();
    expect(container).toBeDefined();

    await waitFor(() => {
      expect(getCountDiscounts()).toHaveTextContent(
        `${testDiscountsResponse.length} Discounts`
      );
    });
  });

  describe("Form Filter", () => {
    it("should show the stores filter dropdown button", async () => {
      const { storeFilterDropdownButton } = renderManualDiscounts();

      await waitFor(() => {
        expect(storeFilterDropdownButton()).toBeInTheDocument();
      });
    });

    it("should show all stores in the dropdown filter", async () => {
      const { storeFilterDropdownButton } = renderManualDiscounts();

      await waitFor(() => {
        expect(storeFilterDropdownButton()).toBeInTheDocument();
      });

      fireEvent.click(storeFilterDropdownButton());

      await waitFor(() => {
        expect(screen.getAllByTestId("menuitemfilter-option")).toHaveLength(2);
      });
    });

    it("should filter the discounts when a store is selected from stores dropdown filter", async () => {
      const { storeFilterDropdownButton, getCountDiscounts } =
        renderManualDiscounts();

      await waitFor(() => {
        expect(storeFilterDropdownButton()).toBeInTheDocument();
      });

      fireEvent.click(storeFilterDropdownButton());

      const storeItems = screen.getAllByTestId("menuitemfilter-option");
      fireEvent.click(storeItems[0]);

      await waitFor(() => {
        expect(getCountDiscounts()).toHaveTextContent(`1 Discounts`);
      });
    });

    it("should show all discounts when no filter is selected", async () => {
      const { storeFilterDropdownButton, getCountDiscounts } =
        renderManualDiscounts();

      await waitFor(() => {
        expect(storeFilterDropdownButton()).toBeInTheDocument();
      });

      fireEvent.click(storeFilterDropdownButton());

      fireEvent.click(screen.getByTestId("allmenuitemfilter-option"));

      await waitFor(() => {
        expect(getCountDiscounts()).toHaveTextContent(`2 Discounts`);
      });
    });
  });

  describe("Manual Discount Table", () => {
    it("should show the manual discount action menu icon buttons on each row in the datagrid", async () => {
      const { loadingSpinner, discountActionMenus } = renderManualDiscounts();
      await waitFor(() => {
        expect(loadingSpinner()).not.toBeInTheDocument();
      });

      await waitFor(() => {
        expect(discountActionMenus()).toHaveLength(2);
      });
    });

    it("should show the expand button on each row the datagrid", async () => {
      const { expandButtons } = renderManualDiscounts();

      await waitFor(() => {
        expect(expandButtons()).toHaveLength(2);
      });
    });

    describe("Discount Row Menu", () => {
      it("should show the discount action menu icon button for storeCustomizations in the datagrid", async () => {
        const { expandButtons, discountActionChildMenus } =
          renderManualDiscounts();
        await waitFor(() => {
          expect(expandButtons()).toHaveLength(2);
        });
        const firstExpandButton = expandButtons()[0];

        fireEvent.click(firstExpandButton);

        await waitFor(() => {
          expect(discountActionChildMenus()).toHaveLength(2);
        });
      });

      it("should unassign the corresponding store from the discount when the unassign menu option is clicked", async () => {
        const { expandButtons, discountActionChildMenus } =
          renderManualDiscounts();
        await waitFor(() => {
          expect(expandButtons()).toHaveLength(2);
        });

        const firstExpandButton = expandButtons()[0];

        fireEvent.click(firstExpandButton);

        const childActionMenus = discountActionChildMenus();

        await waitFor(() => {
          expect(childActionMenus.length).toBe(2);
        });
        const firstchildActionMenu = childActionMenus[0];

        fireEvent.click(firstchildActionMenu);

        const unassignButton = screen.getByText("Unassign");
        expect(unassignButton).toBeInTheDocument();

        fireEvent.click(unassignButton);

        await waitFor(() => {
          expect(
            screen.getByTestId("manual-discounts-action-modal")
          ).toBeInTheDocument();
        });

        fireEvent.click(screen.getByTestId("action-modal-primary-button"));

        await waitFor(() => {
          expect(discountActionChildMenus().length).toBe(1);
        });
      });

      it("should deactivate the discount when the deactivate menu option is clicked", async () => {
        const { discountActionMenus } = renderManualDiscounts();
        await waitFor(() => {
          expect(discountActionMenus()).toHaveLength(2);
        });

        const activeStoreMenu = discountActionMenus()[1];

        await waitFor(() => {
          expect(activeStoreMenu).toBeInTheDocument();
        });

        fireEvent.click(activeStoreMenu);

        const discountActionDeactivateButton = screen.getByText("Deactivate");

        expect(discountActionDeactivateButton).toBeInTheDocument();

        fireEvent.click(discountActionDeactivateButton);

        await waitFor(() => {
          expect(
            screen.getByTestId("manual-discounts-action-modal")
          ).toBeInTheDocument();
        });

        fireEvent.click(screen.getByTestId("action-modal-primary-button"));
        await waitFor(() => {
          expect(discountActionDeactivateButton).not.toBeInTheDocument();
        });
      });

      it("should activate the discount when the activate menu option is clicked", async () => {
        const { discountActionMenus } = renderManualDiscounts();
        await waitFor(() => {
          expect(discountActionMenus()).toHaveLength(2);
        });

        const inactiveStoreMenu = discountActionMenus()[0];

        await waitFor(() => {
          expect(inactiveStoreMenu).toBeInTheDocument();
        });

        fireEvent.click(inactiveStoreMenu);

        expect(inactiveStoreMenu).toBeInTheDocument();

        const discountActionActivateButton = screen.getByText("Activate");
        expect(discountActionActivateButton).toBeInTheDocument();

        fireEvent.click(discountActionActivateButton);

        await waitFor(() => {
          expect(
            screen.getByTestId("manual-discounts-action-modal")
          ).toBeInTheDocument();
        });

        fireEvent.click(screen.getByTestId("action-modal-primary-button"));

        await waitFor(() => {
          expect(discountActionActivateButton).not.toBeInTheDocument();
        });
      });

      it("should delete the discount when the delete menu option is clicked", async () => {
        const { discountActionMenus } = renderManualDiscounts();
        await waitFor(() => {
          expect(discountActionMenus()).toHaveLength(2);
        });

        const activeStoreMenu = discountActionMenus()[1];

        await waitFor(() => {
          expect(activeStoreMenu).toBeInTheDocument();
        });

        fireEvent.click(activeStoreMenu);

        const discountActionDeleteButton = screen.getByText("Delete");
        expect(discountActionDeleteButton).toBeInTheDocument();

        fireEvent.click(discountActionDeleteButton);

        await waitFor(() => {
          expect(
            screen.getByTestId("manual-discounts-action-modal")
          ).toBeInTheDocument();
        });

        fireEvent.click(screen.getByTestId("action-modal-primary-button"));

        await waitFor(() => {
          expect(activeStoreMenu).not.toBeInTheDocument();
        });
      });
    });
  });
});
