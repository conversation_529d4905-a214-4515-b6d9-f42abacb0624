import { LicenseTypes } from "../constants/discounts";
import {
  BogoConditions,
  BundleConditions,
  CustomEndType,
  CustomerEvent,
  CustomRepeatEvery,
  DaysOfWeek,
  FulfillmentTypesMap,
  RepeatType,
} from "./discounts";

export interface OrgDiscountReqBody {
  id?: string;
  title: string;
  organizationId: string;
  displayTitle?: string | null;
  description?: string | null;
  amount: string;
  method: string;
  isActive: boolean;
  isAdjustment: boolean;
  isCart: boolean;
  isManual: boolean;
  isStackable?: boolean | null;
  requireCoupon: boolean;
  requirePin: boolean;
  requireReason: boolean;
  showEcommerce: boolean;
  showCustomerFacing: boolean;
  showSellTreez: boolean;
  imageUrl?: string;
  customerGroups?: CustomerGroupsReqBody[];
  collections?: ProductCollectionReqBody[];
  collectionsRequired?: ProductCollectionReqBody[];
  conditions?: DiscountConditionsReqBody | {};
  manualConditions?: ManualDiscountConditionsReqBody | {};
  schedule?: ScheduleReqBody | null;
  storeCustomizations: StoreCustomizationReqBody[];
  coupons?: CouponReqBody[];
  externalIds: string[];
  internalIds: string[];
}

export interface DiscountConditionsReqBody {
  customerCapEnabled: boolean;
  customerCapValue: number | null;
  customerLimitEnabled: boolean;
  customerLimitValue: number | null;
  purchaseMinimumEnabled: boolean;
  purchaseMinimumValue: number | null;
  purchaseMinimumType: string | null;
  customerEventEnabled: boolean;
  customerEvents: CustomerEvent[] | null;
  customerGroupsEnabled: boolean;
  itemLimitEnabled: boolean;
  itemLimitValue: number | null;
  fulfillmentTypesEnabled: boolean;
  fulfillmentTypes: FulfillmentTypesMap | null;
  customerLicenseTypeEnabled: boolean;
  customerLicenseType: LicenseTypes | null;
  bogoConditions: BogoConditions | null;
  bundleConditions: BundleConditions | null;
}

export interface ManualDiscountConditionsReqBody {
  customerCapEnabled: boolean;
  customerCapValue: number | null;
  purchaseMinimumEnabled: boolean;
  purchaseMinimumValue: number | null;
  purchaseMinimumType: string | null;
  itemLimitEnabled: boolean;
  itemLimitValue: number | null;
}

export interface ScheduleReqBody {
  startDate: string;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  allDay: boolean;
  spansMultipleDays: boolean;
  repeatType: RepeatType;
  customRepeatEvery: CustomRepeatEvery | null;
  customRepeatIntervalCount: number | null;
  customRepeatDaysOfWeek: DaysOfWeek | null;
  customEndType: CustomEndType | null;
  customEndDate: string | null;
  customEndRepeatCount: number | null;
}

export interface StoreCustomizationReqBody {
  id?: string;
  entityId: string;
  entityName: string;
  amount?: string | null;
  requireReason?: boolean | null;
  requirePin?: boolean | null;
  createdAt?: Date;
  isActive: null;
}
export interface CouponReqBody {
  title: string;
  code: string;
  startDate: string;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
}

export interface ProductCollectionReqBody {
  id?: string;
  productCollectionId: string;
  productCollectionName?: string | null;
}

export interface CustomerGroupsReqBody {
  tagId?: string;
}
