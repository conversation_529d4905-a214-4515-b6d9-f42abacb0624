import React from "react";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import TruncatedTooltip from ".";

describe("TruncatedTooltip", () => {
  const renderTruncatedTooltip = (children: string) => {
    const { container } = render(
      <TreezThemeProvider>
        <TruncatedTooltip>{children}</TruncatedTooltip>
      </TreezThemeProvider>
    );

    const { getByTestId, queryByRole } = screen;

    const tooltip = () => queryByRole("tooltip");
    const content = getByTestId("truncated-tooltip-content");

    return {
      tooltip,
      content,
      container,
    };
  };

  it("does not render tooltip when text does not overflow", async () => {
    const { content, tooltip } = renderTruncatedTooltip("Short Text");
    expect(content).toBeInTheDocument();

    // Manually set offsetWidth and scrollWidth to simulate fitting text
    Object.defineProperty(content, "offsetWidth", { value: 100 });
    Object.defineProperty(content, "scrollWidth", { value: 80 });

    fireEvent.mouseOver(content);

    await waitFor(() => {
      expect(content).toBeInTheDocument();
      expect(tooltip()).not.toBeInTheDocument();
    });
  });

  // TODO: Failing test in gitlab job
  it.skip("renders the tooltip when text overflows and is moused over", async () => {
    const longText = "This is long text that would overflow";
    const { content, tooltip } = renderTruncatedTooltip(longText);
    expect(content).toBeInTheDocument();

    // Manually set offsetWidth and scrollWidth to simulate overflowing text
    Object.defineProperty(content, "offsetWidth", { value: 100 });
    Object.defineProperty(content, "scrollWidth", { value: 150 });

    fireEvent.mouseOver(content);

    await waitFor(() => {
      expect(tooltip()).toBeInTheDocument();
      expect(screen.getAllByText(longText).length).toEqual(2);
    });

    fireEvent.mouseLeave(content);

    await waitFor(() => {
      expect(tooltip()).not.toBeInTheDocument();
      expect(screen.getAllByText(longText).length).toEqual(1);
    });
  });
});
