import React, { SyntheticEvent, useEffect, useRef, useState } from "react";
import {
  CheckboxListMenuItem,
  DropdownChip,
  Menu,
  MenuItemCheckboxProps,
  convertPxToRem,
} from "@treez-inc/component-library";
import { styled } from "@mui/material";

const ALL_OPTIONS_KEY = "all-options";

export type DropdownSelectOptionProps = {
  key: string;
  label: string;
  checked?: boolean;
};

interface DropdownSelectProps {
  label: string;
  allOption?: boolean;
  testId?: string;
  data: DropdownSelectOptionProps[];
  onChange: (ids: string[]) => void;
}
interface FilterState {
  selectedStores: string[];
}

const StyledDropdownSelect = styled("div")(({ theme }) => ({
  ".MuiChip-root": {
    border: `${convertPxToRem(1)} solid ${
      theme.palette.grey04.main
    } !important`,
    backgroundColor: theme.palette.primaryWhite.main,
    "&:hover": {
      backgroundColor: `${theme.palette.grey05.main} !important`,
    },
  },
}));

const useMenu = () => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: SyntheticEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return {
    anchorEl,
    open,
    handleClick,
    handleClose,
  };
};

const buildDropdownOptions = (
  data: DropdownSelectOptionProps[],
  allOption: boolean,
  handleAllCheckboxChange: (
    checkboxKey: string | number,
    isChecked: boolean
  ) => void,
  handleSimpleCheckboxChange: (
    checkboxKey: string | number,
    isChecked: boolean
  ) => void
) => {
  const dropdownOptions: MenuItemCheckboxProps[] =
    data.map((option) => ({
      key: option.key,
      label: option.label,
      checked: !!option.checked,
      onChange: handleSimpleCheckboxChange,
      value: option.key,
      testId: "menuitemfilter-option",
    })) || [];

  if (allOption) {
    dropdownOptions.unshift({
      key: ALL_OPTIONS_KEY,
      label: "Select All",
      checked: false,
      onChange: handleAllCheckboxChange,
      value: ALL_OPTIONS_KEY,
      testId: "allmenuitemfilter-option",
    });
  }

  return dropdownOptions;
};

function usePreviousCheckboxes(value: MenuItemCheckboxProps[]) {
  const ref = useRef<MenuItemCheckboxProps[]>();
  useEffect(() => {
    ref.current = value;
  });
  return ref.current;
}

const DropdownSelect: React.FC<DropdownSelectProps> = ({
  label,
  data,
  allOption = false,
  testId,
  onChange,
}) => {
  const [simpleCheckboxes, setSimpleCheckboxes] = useState<
    MenuItemCheckboxProps[]
  >([]);
  const [filterState, setFilterState] = useState<FilterState>({
    selectedStores: [],
  });
  const previousCheckboxes = usePreviousCheckboxes(simpleCheckboxes);

  const handleSimpleCheckboxChange = (
    checkboxKey: string | number,
    isChecked: boolean
  ) => {
    setSimpleCheckboxes((prevCheckboxes) =>
      prevCheckboxes.map((checkbox) => {
        if (checkbox.key === ALL_OPTIONS_KEY && !isChecked) {
          return { ...checkbox, checked: false };
        }

        if (checkbox.key === checkboxKey) {
          return { ...checkbox, checked: isChecked };
        }
        return checkbox;
      })
    );
  };

  const handleAllCheckboxChange = (
    checkboxKey: string | number,
    isChecked: boolean
  ) => {
    if (checkboxKey !== ALL_OPTIONS_KEY) return;

    setSimpleCheckboxes((prevCheckboxes) =>
      prevCheckboxes.map((checkbox) => ({ ...checkbox, checked: isChecked }))
    );
  };

  useEffect(() => {
    setSimpleCheckboxes(
      buildDropdownOptions(
        data,
        allOption,
        handleAllCheckboxChange,
        handleSimpleCheckboxChange
      )
    );
  }, [data]);

  useEffect(() => {
    const selectedItems = simpleCheckboxes
      .filter(
        (checkbox) => checkbox.checked && checkbox.value !== ALL_OPTIONS_KEY
      )
      .map((checkbox) => checkbox.value);

    if (previousCheckboxes && previousCheckboxes.length > 1) {
      onChange(selectedItems);
    }
    setFilterState((prevCheckboxes) => ({
      ...prevCheckboxes,
      selectedStores: selectedItems,
    }));
  }, [simpleCheckboxes]);

  const simpleMenu = useMenu();

  return (
    <StyledDropdownSelect>
      <DropdownChip
        label={label}
        id="simple-demo-dropdown-chip"
        menuId="demo-simple-checkboxlist"
        badgeContent={filterState?.selectedStores?.length || undefined}
        onClick={simpleMenu.handleClick}
        open={simpleMenu.open}
        testId={testId}
      />
      <Menu
        menuId="demo-simple-checkboxlist"
        MenuListProps={{
          "aria-labelledby": "simple-demo-dropdown-chip",
        }}
        anchorEl={simpleMenu.anchorEl}
        open={simpleMenu.open}
        onClose={simpleMenu.handleClose}
      >
        <CheckboxListMenuItem
          key="demo-simple-checkboxlist-menu-item"
          variant="simple"
          checkboxes={simpleCheckboxes}
        />
      </Menu>
    </StyledDropdownSelect>
  );
};

DropdownSelect.defaultProps = {
  allOption: false,
  testId: undefined,
};

export default DropdownSelect;
