import { OrgDiscountResponse } from "../interfaces/responseModels";

export const getDiscountModalMessage = (
  type: "activate" | "deactivate",
  discount: OrgDiscountResponse | undefined
) => {
  if (!discount) return "";

  const actionMessageMap = {
    activate: "become active",
    deactivate: "be deactivated",
  };

  return `Are you sure you want to ${type} the ${discount.title} discount? This discount will ${actionMessageMap[type]} in ${discount.storeCustomizations.length} stores.`;
};
