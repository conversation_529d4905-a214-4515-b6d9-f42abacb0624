import React from "react";
import {
  styled,
  Box,
  Typography,
  Grid,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import {
  DatePicker,
  Input,
  Select,
  convertPxToRem,
  DaysOfWeekPicker,
} from "@treez-inc/component-library";
import { Controller, useFormContext } from "react-hook-form";
import {
  AutomatedDiscountFormData,
  CustomEndType,
  CustomRepeatEvery,
  DaysOfWeek,
  DaysOfWeekValues,
  Schedule,
} from "../../../../../../interfaces/discounts";

const StyledCusomRecurrenceContainer = styled(Box)({
  marginTop: convertPxToRem(21),
});

const StyledRecurrenceDatePickerContainer = styled(Box)({
  marginTop: "3.2rem",
});

const StyledRecurrenceOnInputContainer = styled(Box)({
  marginTop: convertPxToRem(12),
});

const StyledErrorMessage = styled(Box)(({ theme }) => ({
  color: theme.palette.error.main,
  marginLeft: "1rem",
  marginTop: "0.5rem",
  fontWeight: 400,
  fontSize: "0.75rem",
  lineHeight: "1rem",
  fontFamily: "Roboto",
}));

const ORDERED_WEEK_ARRAY = [
  DaysOfWeekValues.SUN,
  DaysOfWeekValues.MON,
  DaysOfWeekValues.TUE,
  DaysOfWeekValues.WED,
  DaysOfWeekValues.THU,
  DaysOfWeekValues.FRI,
  DaysOfWeekValues.SAT,
];

export const SCHEDULE_FIELD = "schedule";

export const toCustomRepeatDaysOfWeek = (intArray: number[]) => {
  const intSet = new Set(intArray);
  return {
    SUN: intSet.has(0),
    MON: intSet.has(1),
    TUE: intSet.has(2),
    WED: intSet.has(3),
    THU: intSet.has(4),
    FRI: intSet.has(5),
    SAT: intSet.has(6),
  };
};

export const daysOfWeekToIntArray = (
  daysOfWeek: DaysOfWeek | undefined | null
) => {
  if (!daysOfWeek) {
    return [];
  }

  const result: number[] = [];

  ORDERED_WEEK_ARRAY.forEach((week, idx) => {
    const value = daysOfWeek[week];
    if (value) {
      result.push(idx);
    }
  });

  return result;
};

const CustomRecurrenceForm = () => {
  const { control, setValue, getValues, trigger } =
    useFormContext<AutomatedDiscountFormData>();

  const handleScheduleFieldChange = (fieldName: string, value: any) => {
    const newSchedule = {
      ...getValues().schedule,
      [fieldName]: value,
    } as Schedule;

    setValue(SCHEDULE_FIELD, newSchedule);
  };

  return (
    <StyledCusomRecurrenceContainer data-testid="custom-recurrence-container">
      <Grid container spacing={convertPxToRem(12)}>
        <Grid item xs={12}>
          <Typography variant="largeText">Repeat at Interval</Typography>
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            name="schedule.customRepeatIntervalCount"
            control={control}
            rules={{
              required: {
                value: true,
                message: "Interval count is required",
              },
              min: { value: 1, message: "Please enter number greater than 0" },
              pattern: { value: /^[0-9]*$/, message: "Must be a whole number" },
            }}
            render={({ fieldState: { error } }) => (
              <Input
                type="number"
                testId="custom-repeat-interval-count-input"
                label="# of"
                helperText={error?.message || ""}
                error={!!error}
                value={getValues().schedule?.customRepeatIntervalCount}
                required
                onChange={(event) => {
                  handleScheduleFieldChange(
                    "customRepeatIntervalCount",
                    event.target.value
                  );

                  trigger("schedule.customRepeatIntervalCount");
                }}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            name="schedule.customRepeatEvery"
            control={control}
            rules={{
              required: {
                value: true,
                message: "Recurrence type is required",
              },
            }}
            render={({ fieldState: { error } }) => (
              <Select
                testId="custom-repeat-every-select"
                required
                label="Recurrence Type"
                helperText={error?.message || ""}
                error={!!error}
                value={getValues().schedule?.customRepeatEvery || ""}
                onChange={(event) => {
                  handleScheduleFieldChange(
                    "customRepeatEvery",
                    event.target.value
                  );

                  if (event.target.value === CustomRepeatEvery.WEEK) {
                    handleScheduleFieldChange(
                      "customRepeatDaysOfWeek",
                      toCustomRepeatDaysOfWeek([])
                    );
                  } else {
                    handleScheduleFieldChange("customRepeatDaysOfWeek", null);
                  }

                  trigger("schedule.customRepeatEvery");
                }}
                menuItems={[
                  {
                    displayName: "Day",
                    displayValue: CustomRepeatEvery.DAY,
                  },
                  {
                    displayName: "Week",
                    displayValue: CustomRepeatEvery.WEEK,
                  },
                  {
                    displayValue: CustomRepeatEvery.MONTH,
                    displayName: "Month",
                  },
                  {
                    displayName: "Year",
                    displayValue: CustomRepeatEvery.YEAR,
                  },
                ]}
              />
            )}
          />
        </Grid>
        {getValues().schedule?.customRepeatEvery === CustomRepeatEvery.WEEK && (
          <Grid item xs={12}>
            <Typography variant="largeText">Days Of Week</Typography>
            <div>
              <Controller
                name="schedule.customRepeatDaysOfWeek"
                control={control}
                render={() => (
                  <DaysOfWeekPicker
                    testId="custom-recurrence-day-of-week-picker"
                    initialValues={daysOfWeekToIntArray(
                      getValues().schedule?.customRepeatDaysOfWeek
                    )}
                    onChange={(event, value) => {
                      handleScheduleFieldChange(
                        "customRepeatDaysOfWeek",
                        toCustomRepeatDaysOfWeek(value)
                      );
                    }}
                  />
                )}
              />
            </div>
          </Grid>
        )}
        <Grid item xs={6}>
          <Controller
            name="schedule.customEndType"
            control={control}
            rules={{
              required: {
                value: true,
                message: "Custom End Type is Required",
              },
            }}
            render={({ fieldState: { error } }) => (
              <>
                <Typography variant="largeText">Ends</Typography>

                <RadioGroup
                  data-testid="custom-end-type-radio-buttons"
                  name="custom-end-type-radio-buttons-group"
                  value={
                    getValues().schedule?.customEndType || CustomEndType.NEVER
                  }
                  onChange={(event) => {
                    handleScheduleFieldChange(
                      "customEndType",
                      event.target.value
                    );

                    trigger("schedule.customEndType");

                    handleScheduleFieldChange("customEndDate", null);

                    handleScheduleFieldChange("customEndRepeatCount", null);
                  }}
                >
                  <FormControlLabel
                    value={CustomEndType.NEVER}
                    control={<Radio />}
                    label="Never"
                  />
                  <FormControlLabel
                    value={CustomEndType.DATE}
                    control={<Radio />}
                    data-testid="on-date-radio-button-option"
                    label="On (Date)"
                  />
                  <FormControlLabel
                    value={CustomEndType.REPEAT_COUNT}
                    control={<Radio />}
                    data-testid="after-recurrence-count-radio-button-option"
                    label="After (# of Occurrences)"
                  />
                </RadioGroup>
                <StyledErrorMessage>{error?.message || ""}</StyledErrorMessage>
              </>
            )}
          />
        </Grid>
        <Grid item xs={6}>
          <Controller
            name="schedule.customEndDate"
            control={control}
            rules={{
              required: {
                value:
                  getValues().schedule?.customEndType === CustomEndType.DATE,
                message: "Custom End Date is Required",
              },
            }}
            render={({ fieldState: { error } }) => (
              <StyledRecurrenceDatePickerContainer>
                <DatePicker
                  helperText={error?.message || ""}
                  error={!!error}
                  value={getValues().schedule?.customEndDate || null}
                  disabled={
                    !(
                      getValues().schedule?.customEndType === CustomEndType.DATE
                    )
                  }
                  label="Ocurrences End Date"
                  testId="custom-end-date-picker"
                  onChange={(value) => {
                    handleScheduleFieldChange("customEndDate", value.$d);

                    trigger("schedule.customEndDate");
                  }}
                />
              </StyledRecurrenceDatePickerContainer>
            )}
          />
          <Controller
            name="schedule.customEndRepeatCount"
            control={control}
            rules={{
              required: {
                value:
                  getValues().schedule?.customEndType ===
                  CustomEndType.REPEAT_COUNT,
                message: "Custom # Recurrences is required",
              },
              min: { value: 1, message: "Please enter number greater than 0" },
              pattern: { value: /^[0-9]*$/, message: "Must be a whole number" },
            }}
            render={({ fieldState: { error } }) => (
              <StyledRecurrenceOnInputContainer>
                <Input
                  type="number"
                  helperText={error?.message || ""}
                  error={!!error}
                  value={getValues().schedule?.customEndRepeatCount || ""}
                  disabled={
                    !(
                      getValues().schedule?.customEndType ===
                      CustomEndType.REPEAT_COUNT
                    )
                  }
                  testId="custom-end-repeat-count-input"
                  label="# of Occurrences"
                  onChange={(event) => {
                    handleScheduleFieldChange(
                      "customEndRepeatCount",
                      event.target.value
                    );

                    trigger("schedule.customEndRepeatCount");
                  }}
                />
              </StyledRecurrenceOnInputContainer>
            )}
          />
        </Grid>
      </Grid>
    </StyledCusomRecurrenceContainer>
  );
};

export default CustomRecurrenceForm;
