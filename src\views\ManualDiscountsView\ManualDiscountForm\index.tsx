import React, { useEffect, useState } from "react";
import axios from "axios";
import { useNavigate, useParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { styled, Box } from "@mui/material";
import { convertPxToRem, Button, Tooltip } from "@treez-inc/component-library";
import { UseQueryResult, useQueryClient } from "@tanstack/react-query";
import { manualDiscountsPath } from "../../../constants/routes";
import LoadingSpinner from "../../../components/LoadingSpinner";
import WizardFormModal from "../../../components/WizardFormModal";
import VerticalStepper from "../../../components/WizardFormModal/VerticalStepper";
import HeaderBar from "../../../components/WizardFormModal/HeaderBar";
import LegacyFooter from "../../../components/WizardFormModal/LegacyFooter";
import DiscountInfoStep from "./components/DiscountInfoStep";
import SelectStoreStep from "./components/SelectStoreStep/SelectStoreStep";
import ReviewDiscount from "./components/ReviewDiscountStep/ReviewDiscountStep";
import {
  useGetEntities,
  useGetFeatureFlag,
  usePageLoading,
  useCreateDiscountMutation,
  useUpdateDiscountMutation,
  useDiscountByIdQuery,
} from "../../../hooks";
import ApiService from "../../../services/api/apiService";
import { ManualDiscountFormData } from "../../../interfaces/discounts";
import { EntityResponse } from "../../../interfaces/entity";
import {
  OrgDiscountReqBody,
  StoreCustomizationReqBody,
} from "../../../interfaces/requestModels";
import { OrgDiscountResponse } from "../../../interfaces/responseModels";
import {
  entityListUrl,
  orgFeatureFlagsUrl,
} from "../../../services/apiEndPoints";
import { useSnackbar } from "../../../providers/SnackbarProvider";
import {
  formatDateToTime,
  getDateFromTime,
  getDateLocaleString,
  parseDateString,
} from "../../../utils/date";
import { MANUAL_DISCOUNT_METHODS } from "../../../constants/discountForm";
import {
  getBannerTitle,
  getNextStepButtonLabel,
  parseJwt,
  truncateSnackbarMessage,
} from "../../../utils";
import { IDLE_FETCH_STATUS, PERMISSIONS_MESSAGES } from "../../../constants";
import SetConditionsStep from "./components/SetConditionsStep/SetConditionsStep";

interface ManualDiscountFormProps {
  api: ApiService;
  permissions: { read: boolean; write: boolean };
  isEditMode?: boolean;
}
const StepsWrapper = styled(Box)(() => ({
  marginBottom: "auto",
  display: "grid",
  gridTemplateColumns: `${convertPxToRem(208)} 1fr`,
  paddingTop: `${convertPxToRem(34)}`,
  maxWidth: `${convertPxToRem(900)}`,
}));

const RightButtonsWrapper = styled(Box)(() => ({
  display: "flex",
  gap: `${convertPxToRem(16)}`,
}));

const getAmountFormatted = (method: string, amount: string) => {
  const value = parseFloat(amount).toFixed(2);

  if (method === MANUAL_DISCOUNT_METHODS.DOLLAR) {
    return Number(value).toString();
  }

  return value;
};

const buildManualDiscountDefaultValues = (
  orgDiscount: OrgDiscountResponse
): ManualDiscountFormData => ({
  id: orgDiscount.id,
  title: orgDiscount.title,
  displayTitle: orgDiscount.displayTitle,
  method: orgDiscount.method,
  amount: getAmountFormatted(orgDiscount.method, orgDiscount.amount),
  isItem: !orgDiscount.isCart,
  isAdjustment: orgDiscount.isAdjustment,
  isActive: orgDiscount.isActive,
  isManual: orgDiscount.isManual,
  requirePin: orgDiscount.requirePin,
  requireReason: orgDiscount.requireReason,
  requireCoupon: orgDiscount.requireCoupon,
  manualConditions: orgDiscount.manualConditions
    ? {
      customerCapValue: orgDiscount.manualConditions.customerCapEnabled
        ? Number(orgDiscount.manualConditions.customerCapValue)
        : null,
      purchaseMinimumValue: orgDiscount.manualConditions.purchaseMinimumEnabled
        ? Number(orgDiscount.manualConditions.purchaseMinimumValue)
        : null,
      purchaseMinimumType: orgDiscount.manualConditions.purchaseMinimumEnabled
        ? orgDiscount.manualConditions.purchaseMinimumType
        : null,
      redemptionLimitValue: orgDiscount.manualConditions.redemptionLimitEnabled
        ? Number(orgDiscount.manualConditions.redemptionLimitValue)
        : null,
      customerCapEnabled: orgDiscount.manualConditions.customerCapEnabled,
      purchaseMinimumEnabled: orgDiscount.manualConditions.purchaseMinimumEnabled,
      redemptionLimitEnabled: orgDiscount.manualConditions.redemptionLimitEnabled,
    }
    : {
      customerCapValue: null,
      purchaseMinimumValue: null,
      purchaseMinimumType: null,
      redemptionLimitValue: null,
      customerCapEnabled: false,
      purchaseMinimumEnabled: false,
      redemptionLimitEnabled: false,
    },
  storeCustomizations:
    orgDiscount.storeCustomizations &&
    orgDiscount.storeCustomizations.map((store) => ({
      id: store.id,
      createdAt: new Date(store.createdAt),
      entityId: store.entityId,
      entityName: store.entityName,
      amount:
        store.amount !== null
          ? getAmountFormatted(orgDiscount.method, store.amount)
          : null,
      requireReason: store.requireReason,
      requirePin: store.requirePin,
      isActive: true,
    })),
  couponFormModel: {
    coupons:
      orgDiscount.coupons?.map((coupon) => {
        const startDate = parseDateString(coupon.startDate);
        const endDate = coupon.endDate
          ? parseDateString(coupon.endDate)
          : undefined;

        const isAllDay = !coupon.startTime;
        const ignoreExpiration = !coupon.endDate;

        const startTime = coupon.startTime
          ? getDateFromTime(coupon.startTime)
          : undefined;
        const endTime = coupon.endTime
          ? getDateFromTime(coupon.endTime)
          : undefined;

        return {
          id: coupon.id,
          code: coupon.code,
          startDate,
          startTime,
          endDate,
          endTime,
          isAllDay,
          ignoreExpiration,
        };
      }) || [],
    couponToAdd: "",
  },
  organizationId: orgDiscount.organizationId,
});

const ManualDiscountForm: React.FC<ManualDiscountFormProps> = ({
  api,
  permissions,
  isEditMode = false,
}) => {
  const { isPageLoading, setIsPageLoading } = usePageLoading();
  const { orgDiscountId, storeCustomizationId } = useParams();
  const [isDialogueOpen, setDialogueOpen] = useState<boolean>(true);
  const [isEditConfirmModal, setEditConfirmModal] = useState(false);
  const [buttonAction, setButtonAction] = useState<string>("");
  const [activeStep, setActiveStep] = useState<number>(0);
  const [entityLists, setEntityLists] = useState<EntityResponse[]>([]);
  const shouldIncludeStoresStep = entityLists?.length > 1;

  const decodedToken = parseJwt(api.getTokens().accessToken);

  const defaultValues: ManualDiscountFormData = {
    title: "",
    displayTitle: "",
    method: MANUAL_DISCOUNT_METHODS.DOLLAR,
    amount: "",
    isItem: false,
    isAdjustment: false,
    isActive: true,
    isManual: true,
    requirePin: false,
    requireReason: false,
    requireCoupon: false,
    storeCustomizations: [],
    couponFormModel: {
      coupons: [],
      couponToAdd: "",
    },
    manualConditions: {
      customerCapValue: null,
      purchaseMinimumValue: null,
      purchaseMinimumType: null,
      redemptionLimitValue: null,
      customerCapEnabled: false,
      purchaseMinimumEnabled: false,
      redemptionLimitEnabled: false,
    },
    organizationId: decodedToken?.orgId,
  };

  // TODO: Use useFormContext instead of passing the various form methods to the child components
  const {
    getValues,
    handleSubmit,
    control,
    trigger,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<ManualDiscountFormData>({
    defaultValues,
    mode: "all",
  });

  const tryGoToStep = async (stepIdx: number) => {
    /* Create Flow is Allowed to go back validation errors */
    const canNavigate = isEditMode ? await trigger() : true;

    if (canNavigate) {
      setActiveStep(stepIdx);
    }
  };

  const handleNext = async () => {
    const noErrorExist = await trigger(); // false if there is error

    if (noErrorExist && errors && Object.keys(errors).length === 0) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const [steps, setSteps] = useState([
    {
      label: "Discount Information",
      completed: isEditMode,
      disabled: !isEditMode,
      onClick: () => {
        tryGoToStep(0);
      },
    },
    {
      label: "Select Stores",
      completed: isEditMode,
      disabled: !isEditMode,
      onClick: () => {
        tryGoToStep(1);
      },
    },
    {
      label: "Set Conditions",
      completed: isEditMode,
      disabled: !isEditMode,
      onClick: () => {
        tryGoToStep(shouldIncludeStoresStep ? 2 : 1);
      },
    },
    {
      label: "Review",
      completed: isEditMode,
      disabled: !isEditMode,
      onClick: () => {
        tryGoToStep(3);
      },
    },
  ]);

  const { openSnackbar } = useSnackbar();

  const queryClient = useQueryClient();
  const mutateCreateDiscount = useCreateDiscountMutation({ api, queryClient });
  const mutateUpdateDiscount = useUpdateDiscountMutation({ api, queryClient });
  const navigate = useNavigate();

  const useDiscountQuery: UseQueryResult | undefined = orgDiscountId
    ? useDiscountByIdQuery(api, orgDiscountId)
    : undefined;

  const [entityList] = useGetEntities(api, entityListUrl(decodedToken?.orgId));

  const [couponFeatureFlag] = useGetFeatureFlag(
    api,
    orgFeatureFlagsUrl({
      orgId: decodedToken?.orgId,
      featureFlag: "Manual Discounts - Coupons",
    })
  );

  const [priceAtFeatureFlag] = useGetFeatureFlag(
    api,
    orgFeatureFlagsUrl({
      orgId: decodedToken?.orgId,
      featureFlag: "Manual Discounts - Pricing Adjustment",
    })
  );

  useEffect(() => {
    setIsPageLoading(!!useDiscountQuery?.isLoading);

    if (useDiscountQuery?.error && !useDiscountQuery?.isLoading) {
      navigate(manualDiscountsPath);
      openSnackbar({
        severity: "error",
        message: "There was an error retrieving the discount details",
      });
    }

    if (
      isEditMode &&
      useDiscountQuery?.data &&
      useDiscountQuery?.fetchStatus === IDLE_FETCH_STATUS
    ) {
      reset(
        buildManualDiscountDefaultValues(
          useDiscountQuery?.data as OrgDiscountResponse
        )
      );
    }
  }, [useDiscountQuery?.data, useDiscountQuery?.fetchStatus]);

  useEffect(() => {
    if (!isEditMode) {
      setSteps((currentSteps) =>
        currentSteps.map((currentStep, i) => ({
          ...currentStep,
          disabled: i > activeStep,
          completed: i < activeStep,
        }))
      );
    }
  }, [activeStep]);

  const onSubmit = async (data: ManualDiscountFormData) => {
    const couponsReqBody = data.couponFormModel.coupons.map((coupon) => {
      const { startDate, endDate, startTime, endTime } = coupon;

      return {
        id: coupon.id,
        code: coupon.code,
        title: coupon.code,
        startDate: getDateLocaleString(startDate),
        endDate: endDate ? getDateLocaleString(endDate) : null,
        startTime: startTime ? formatDateToTime(startTime) : null,
        endTime: endTime ? formatDateToTime(endTime) : null,
      };
    });

    let storeCustomizationsReqBody: StoreCustomizationReqBody[] = [];

    if (entityLists?.length === 1 && !isEditMode) {
      storeCustomizationsReqBody = [
        {
          entityId: entityLists[0].id,
          entityName: entityLists[0].name,
          amount: null,
          requirePin: null,
          requireReason: null,
          isActive: null,
        },
      ];
    } else {
      storeCustomizationsReqBody = data.storeCustomizations
        .filter((store) => store.isActive)
        .map((store) => {
          const storeCustomization: StoreCustomizationReqBody = {
            id: store.id,
            entityId: store.entityId,
            entityName: store.entityName,
            amount: store.amount !== null ? store.amount : null,
            requirePin: store.requirePin !== null ? store.requirePin : null,
            requireReason:
              store.requireReason !== null ? store.requireReason : null,
            createdAt: store.createdAt,
            isActive: null,
          };

          return storeCustomization;
        });
    }

    const manualConditionsReqBody = {
      ...data.manualConditions,
      customerCapValue: data.manualConditions.customerCapEnabled
        ? data.manualConditions.customerCapValue
        : null,
      purchaseMinimumValue: data.manualConditions.purchaseMinimumEnabled
        ? data.manualConditions.purchaseMinimumValue
        : null,
      purchaseMinimumType: data.manualConditions.purchaseMinimumEnabled
        ? data.manualConditions.purchaseMinimumType
        : null,
      redemptionLimitValue: data.manualConditions.redemptionLimitEnabled
        ? data.manualConditions.redemptionLimitValue
        : null,
    };

    const orgDiscount: OrgDiscountReqBody = {
      id: data.id,
      title: data.title,
      displayTitle: null,
      description: null,
      amount: data.amount,
      method: data.method,
      isActive: data.isActive,
      isAdjustment: false,
      isCart: !data.isItem,
      isManual: true,
      coupons: couponsReqBody,
      requireCoupon: !data.isItem ? data.requireCoupon : false,
      requirePin: data.requirePin,
      requireReason: data.requireReason,
      showEcommerce: true,
      showCustomerFacing: true,
      showSellTreez: true,
      storeCustomizations: storeCustomizationsReqBody,
      organizationId: decodedToken?.orgId,
      externalIds: [],
      internalIds: [],
      manualConditions: manualConditionsReqBody,
    };

    try {
      if (isEditMode) {
        await mutateUpdateDiscount.mutateAsync(orgDiscount);
      } else {
        await mutateCreateDiscount.mutateAsync(orgDiscount);
      }

      openSnackbar({
        severity: "info",
        iconName: "Success",
        message: isEditMode
          ? `"${orgDiscount.title}" discount has been updated`
          : `"${orgDiscount.title}" discount has been created`,
      });

      navigate(manualDiscountsPath);
    } catch (error: Error | unknown) {
      let message = isEditMode
        ? "Unable to update the discount. Please try again"
        : "Unable to create the discount. Please try again";

      if (axios.isAxiosError(error)) {
        message = (
          error?.response?.data?.errorMsgs || [
            "Your request could not be completed due to a network error",
          ]
        ).join(", ");
      }

      openSnackbar({
        message: truncateSnackbarMessage(message),
        severity: "error",
      });
    }
  };

  const handleClose = () => {
    setDialogueOpen(false);
    navigate(manualDiscountsPath);
  };

  const handleDoneEditing = () => {
    if (activeStep === 0) {
      setEditConfirmModal(true);
      setButtonAction("Done Editing");
    } else {
      setButtonAction("");
      handleSubmit((d) => onSubmit(d))();
    }
  };

  const handleEditConfirmModal = () => {
    if (buttonAction === "Done Editing") {
      handleSubmit((d) => onSubmit(d))();
    } else {
      handleNext();
    }
    setEditConfirmModal(false);
    setButtonAction("");
  };

  const closeEditConfirmModal = () => {
    setButtonAction("");
    setEditConfirmModal(false);
  };

  useEffect(() => {
    if (entityList.data && !!entityList.data.length) {
      setEntityLists(entityList.data);
    }
  }, [entityList]);

  useEffect(() => {
    if (storeCustomizationId && activeStep !== 1) {
      setActiveStep(1);
    }
  }, [storeCustomizationId]);

  useEffect(() => {
    const isLoading = entityList.loading;
    setIsPageLoading(isLoading);
  }, [entityList.loading]);

  useEffect(() => {
    if (entityList.data && entityList.data.length <= 1) {
      setSteps((currentSteps) =>
        currentSteps.filter((step) => step.label !== "Select Stores")
      );
    }
  }, [entityList]);

  const renderForm = (step: number) => {
    let currentStep = step;
    if (!shouldIncludeStoresStep && currentStep !== 0) {
      currentStep += 1;
    }

    switch (currentStep) {
      case 0:
        return (
          <DiscountInfoStep
            trigger={trigger}
            errors={errors}
            control={control}
            getValues={getValues}
            setValue={setValue}
            watch={watch}
            isEditMode={isEditMode}
            isEditConfirmModal={isEditConfirmModal}
            closeEditConfirmModal={closeEditConfirmModal}
            handleEditConfirmModal={handleEditConfirmModal}
            couponFeatureFlag={couponFeatureFlag?.data?.status || false}
            priceAtFeatureFlag={priceAtFeatureFlag?.data?.status || false}
          />
        );
      case 1:
        return (
          <SelectStoreStep
            isEditMode={isEditMode}
            errors={errors}
            entityList={entityLists}
            getValues={getValues}
            control={control}
            setValue={setValue}
            storeCustomizationId={storeCustomizationId}
          />
        );
      case 2:
        return (
          <SetConditionsStep
            getValues={getValues}
            control={control}
            setValue={setValue}
          />
        );
      default:
        return (
          <ReviewDiscount
            getValues={getValues}
            entityList={entityLists}
            shouldShowStoresReview={shouldIncludeStoresStep || isEditMode} />
        );
    }
  };

  return (
    <WizardFormModal
      bannerTitle={getBannerTitle({
        orgDiscount: useDiscountQuery?.data as OrgDiscountResponse,
        entities: entityLists,
        storeCustomizationId,
      })}
      isOpen={isDialogueOpen}
      handleClose={handleClose}
      testId="manual-discount-add-modal"
    >
      {(isPageLoading ||
        queryClient.isMutating() > 0 ||
        useDiscountQuery?.isLoading) && <LoadingSpinner />}
      <HeaderBar
        header={isEditMode ? defaultValues.title : "Add Manual Discount"}
        subheader={
          isEditMode && defaultValues.displayTitle
            ? defaultValues.displayTitle
            : ""
        }
        iconName="Dollar"
      />

      <Box sx={{ overflowY: "scroll", height: "100%" }}>
        <StepsWrapper>
          <Box>
            <VerticalStepper steps={steps} activeStep={activeStep} />
          </Box>
          {renderForm(activeStep)}
        </StepsWrapper>
      </Box>

      <LegacyFooter data-testid="manualdiscountadd-footer">
        <>
          {activeStep === 0 ? (
            <Box />
          ) : (
            <Button
              testId="previous-btn"
              iconName="ChevronLeft"
              label={steps[activeStep - 1].label}
              onClick={handleBack}
              variant="secondary"
            />
          )}
          <RightButtonsWrapper>
            {isEditMode &&
              activeStep !== steps.length - 1 &&
              (!permissions.write ? (
                <Tooltip
                  title={PERMISSIONS_MESSAGES.NO_EDIT}
                  variant="multiRow"
                >
                  <Button
                    testId="done-editing-button"
                    variant="text"
                    label="Done Editing"
                    onClick={handleDoneEditing}
                    disabled
                  />
                </Tooltip>
              ) : (
                <Button
                  testId="done-editing-button"
                  variant="text"
                  label="Done Editing"
                  onClick={handleDoneEditing}
                />
              ))}
            {activeStep === steps.length - 1 && !permissions.write ? (
              <Tooltip
                title={
                  isEditMode
                    ? PERMISSIONS_MESSAGES.NO_EDIT
                    : PERMISSIONS_MESSAGES.NO_CREATE
                }
                variant="multiRow"
              >
                <Button
                  testId="next-step-button"
                  label={getNextStepButtonLabel(
                    activeStep,
                    steps.length,
                    isEditMode
                  )}
                  disabled
                  onClick={() => { }}
                />
              </Tooltip>
            ) : (
              <Button
                testId="next-step-button"
                label={getNextStepButtonLabel(
                  activeStep,
                  steps.length,
                  isEditMode
                )}
                disabled={isPageLoading}
                onClick={
                  activeStep === steps.length - 1
                    ? handleSubmit((d) => onSubmit(d))
                    : () => handleNext()
                }
              />
            )}
          </RightButtonsWrapper>
        </>
      </LegacyFooter>
    </WizardFormModal>
  );
};

ManualDiscountForm.defaultProps = {
  isEditMode: false,
};

export default ManualDiscountForm;
