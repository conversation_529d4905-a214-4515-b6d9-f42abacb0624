import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  IconButton,
  Menu,
  TextMenuItem,
  TextMenuItemProps,
} from "@treez-inc/component-library";
import { IconName } from "@treez-inc/component-library/dist/components/Icon/types";
import { OrgDiscountRow } from "../../interfaces/table";
import { useSnackbar } from "../../providers/SnackbarProvider";
import copyDiscountId from "./utils/copyDiscountId";
import {
  editAutomatedDiscountPath,
  editManualDiscountsPath,
  editStoreCustomizationPath,
} from "../../constants/routes";
import { OrgDiscountResponse } from "../../interfaces/responseModels";

const convertDiscountRowToDiscountResponse = (
  row: OrgDiscountRow
): Omit<
  OrgDiscountResponse,
  | "createdAt"
  | "updatedAt"
  | "conditions"
  | "collections"
  | "collectionsRequired"
> => {
  const {
    createdAt,
    updatedAt,
    conditions,
    collections,
    collectionsRequired,
    ...rest
  } = row;
  return rest;
};
export interface DiscountActionMenuProps {
  row: OrgDiscountRow;
  testId?: string;
  openDiscountModal: (
    type: string,
    {
      discount,
      parentDiscountId,
      storeToUnassignId,
    }: {
      discount?: OrgDiscountResponse;
      parentDiscountId?: string;
      storeToUnassignId?: string;
    }
  ) => void;
  openDiscountLog: (discount: OrgDiscountResponse) => void;
}

const DiscountActionMenu: React.FC<DiscountActionMenuProps> = ({
  row,
  testId,
  openDiscountModal,
  openDiscountLog,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const { id, isActive, isChild, parentId, isManual } = row;
  const navigate = useNavigate();
  const { openSnackbar } = useSnackbar();

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  // ensures the correct callback is invoked during the onClick event
  const handleCopyDiscountId = copyDiscountId(id, openSnackbar);

  const handleEdit = () => {
    navigate(
      isManual ? editManualDiscountsPath(id) : editAutomatedDiscountPath(id)
    );
  };

  const handleDelete = () => {
    openDiscountModal("delete", {
      discount: convertDiscountRowToDiscountResponse(
        row
      ) as OrgDiscountResponse,
    });
  };

  const handleToggleActive = () => {
    openDiscountModal(isActive ? "deactivate" : "activate", {
      discount: convertDiscountRowToDiscountResponse(
        row
      ) as OrgDiscountResponse,
    });
  };

  const handleEditStoreConditions = () => {
    navigate(editStoreCustomizationPath(parentId!, id));
  };

  const handleUnassign = async () => {
    openDiscountModal("unassign", {
      discount: undefined,
      parentDiscountId: parentId,
      storeToUnassignId: id,
    });
  };

  const handleDiscountLog = async () => {
    openDiscountLog(
      convertDiscountRowToDiscountResponse(row) as OrgDiscountResponse
    );
  };

  const menuItems: TextMenuItemProps[] = !isChild
    ? [
        {
          testId: "edit-discount-menu-item",
          displayName: "Edit",
          displayValue: "Edit",
          iconProps: { iconName: "Edit" as IconName },
          onClick: handleEdit,
        },
        {
          testId: isActive
            ? "deactivate-discount-menu-item"
            : "activate-discount-menu-item",
          displayName: isActive ? "Deactivate" : "Activate",
          displayValue: isActive ? "Deactivate" : "Activate",
          iconProps: {
            iconName: isActive ? "Deactivate" : ("Checkmark" as IconName),
          },
          onClick: handleToggleActive,
        },
        {
          testId: "delete-discount-menu-item",
          displayName: "Delete",
          displayValue: "Delete",
          iconProps: { iconName: "Delete" as IconName },
          onClick: handleDelete,
        },
        ...(isManual
          ? [
              {
                testId: "copy-discount-menu-item",
                displayName: "Copy Discount ID",
                displayValue: "Copy Discount ID",
                iconProps: { iconName: "Copy" as IconName },
                onClick: handleCopyDiscountId,
              },
            ]
          : []),
        {
          testId: "discount-logs-menu-item",
          displayName: "View History Log",
          displayValue: "View History Log",
          iconProps: {
            iconName: "History" as IconName,
          },
          onClick: handleDiscountLog,
        },
      ]
    : [
        ...(isManual
          ? [
              {
                testId: "edit-store-conditions-menu-item",
                displayName: "Edit Store Conditions",
                displayValue: "Edit Store Conditions",
                iconProps: { iconName: "Edit" as IconName },
                onClick: handleEditStoreConditions,
              },
              {
                testId: "unassign-store-conditions-menu-item",
                displayName: "Unassign",
                displayValue: "Unassign",
                iconProps: { iconName: "Revoke" as IconName },
                onClick: handleUnassign,
              },
            ]
          : []),
      ];

  return (
    <div key={`action-menu-${id}`} data-testid={testId}>
      <IconButton
        iconName="More"
        variant="secondary"
        onClick={handleClick}
        testId={isChild ? "discount-action-child-menu" : "discount-action-menu"}
        // TODO: Remove ts-ignore when IconButton is updated
        // @ts-ignore
        iconButtonId="discount-action-list-button"
        aria-controls={open ? "discount-action-list-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
      />
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{ "aria-labelledby": "discount-action-list-button" }}
        small
        testId="discount-action-list"
        menuId="discount-action-list-menu"
      >
        {menuItems.map(
          ({
            testId: textMenuItemTestId,
            displayName,
            displayValue,
            iconProps,
            onClick,
          }: TextMenuItemProps) => (
            <TextMenuItem
              key={`discount-action-menu-item-${displayName}`}
              testId={textMenuItemTestId}
              displayName={displayName}
              displayValue={displayValue}
              iconProps={iconProps}
              onClick={(event: React.MouseEvent<HTMLLIElement, MouseEvent>) => {
                handleClose();
                onClick(event);
              }}
            />
          )
        )}
      </Menu>
    </div>
  );
};

export default DiscountActionMenu;
