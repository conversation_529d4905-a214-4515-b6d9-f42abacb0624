import { UndefinedInitialDataOptions, useQuery } from "@tanstack/react-query";
import ApiService from "../../services/api/apiService";
import { getDiscountByIdUrl } from "../../services/apiEndPoints";

const useDiscountByIdQuery = (
  api: ApiService,
  id: string,
  options?: Omit<UndefinedInitialDataOptions, "queryKey" | "queryFn">
) =>
  useQuery({
    ...options,
    queryKey: ["discounts", { id }],
    queryFn: async () => {
      const result = await api.get(getDiscountByIdUrl(id));
      return result.data;
    },
  });

export default useDiscountByIdQuery;
