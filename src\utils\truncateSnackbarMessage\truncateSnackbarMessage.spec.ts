import { truncateSnackbarMessage } from ".";

describe("truncateSnackbarMessage", () => {
  it("should return the original message if its length is less than or equal to 60 characters", () => {
    const message = "This is a short message.";
    const truncatedMessage = truncateSnackbarMessage(message);
    expect(truncatedMessage).toBe(message);
  });

  it("should truncate the message to 57 characters and append '...' if its length exceeds 60 characters", () => {
    const longMessage =
      "This is a long message. It exceeds 60 characters limit, so it should be truncated.";
    const expectedTruncatedMessage =
      "This is a long message. It exceeds 60 characters limit, s...";
    const truncatedMessage = truncateSnackbarMessage(longMessage);

    expect(truncatedMessage).toBe(expectedTruncatedMessage);
    expect(truncatedMessage.length).toBe(60);
  });

  it("should handle empty message", () => {
    const emptyMessage = "";
    const truncatedMessage = truncateSnackbarMessage(emptyMessage);
    expect(truncatedMessage).toBe(emptyMessage);
  });
});
