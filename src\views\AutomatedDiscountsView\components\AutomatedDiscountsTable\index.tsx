import React from "react";
import { convertPxToRem, DataGridPro } from "@treez-inc/component-library";
import {
  DataGridProProps,
  GridColDef,
  GridRenderCellParams,
  GridRowClassNameParams,
  useGridApiRef,
} from "@mui/x-data-grid-pro";
import { styled, Box, Typography, Skeleton } from "@mui/material";
import { GridApiPro } from "@mui/x-data-grid-pro/models/gridApiPro";
import { formatScheduledDateTime } from "../../../../utils/date";
import DiscountStatusBox from "../../../../components/DiscountStatusBox";
import RowActionButtons from "../../../../components/RowActionButtons";
import { OrgDiscountResponse } from "../../../../interfaces/responseModels";
import { OrgDiscountRow } from "../../../../interfaces/table";
import {
  buildRowsWithHierarchy,
  formatAmount,
  store<PERSON>ell<PERSON><PERSON>er,
  productCollectionsCell<PERSON><PERSON>er,
} from "../../../../utils";
import { AUTOMATED_DISCOUNT_METHODS } from "../../../../constants/discountForm";
import TruncatedTooltip from "../../../../components/TruncatedTooltip";

export interface AutomatedDiscountsTableProps {
  discounts: OrgDiscountResponse[] | undefined;
  // discountData supports both row types for RowActionButtons compatibility
  openDiscountModal: (
    type: string,
    {
      discount,
      parentDiscountId,
      storeToUnassignId,
    }: {
      discount?: OrgDiscountResponse;
      parentDiscountId?: string;
      storeToUnassignId?: string;
    }
  ) => void;
  openDiscountLog: (discount: OrgDiscountResponse) => void;
}

type DataRow = {
  title: string;
  method: keyof typeof AUTOMATED_DISCOUNT_METHODS;
  amount: number;
  storeCustomizations: string[];
  isActive: boolean;
};

const StyledTableContainer = styled(Box)(({ theme }) => ({
  height: "calc(100% - 9rem)",
  padding: `${convertPxToRem(40)} 0`,
  [theme.breakpoints.up("sm")]: {
    padding: `${convertPxToRem(40)} ${convertPxToRem(20)}`,
  },
  [theme.breakpoints.up("lg")]: {
    padding: `${convertPxToRem(40)} ${convertPxToRem(52)}`,
  },
  [theme.breakpoints.up("xl")]: {
    padding: `${convertPxToRem(40)} ${convertPxToRem(80)}`,
  },
}));

const StyledDataGridContainer = styled(Box)(({ theme }) => ({
  height: "100%",
  display: "flex",
  flexDirection: "column",
  paddingBottom: convertPxToRem(1),
  "& .DataGrid-Row-Child": {
    backgroundColor: theme.palette.grey01.main,
  },
}));

const StyledDiscountTableHeaderContainer = styled("div")(() => ({
  display: "block",
}));

const dataTableColumns: GridColDef<DataRow>[] = [
  {
    field: "title",
    headerName: "Name",
    flex: 3,
    renderCell: (params: any) => (
      <TruncatedTooltip>{params.row.title}</TruncatedTooltip>
    ),
  },
  {
    field: "displayTitle",
    headerName: "Discount Title",
    hideable: true,
  },
  {
    field: "method",
    headerName: "Method",
    flex: 1,
    renderCell: (params: GridRenderCellParams<DataRow>) =>
      AUTOMATED_DISCOUNT_METHODS[params.row.method],
  },
  {
    field: "amount",
    headerName: "Amount",
    flex: 1,
    valueGetter: ({ value }) => {
      if (!value) {
        return 0;
      }
      return Number(value);
    },
    renderCell: (params: GridRenderCellParams) =>
      formatAmount(params.row.amount, params.row.method, params.row.conditions),
  },
  {
    field: "storeCustomizations",
    headerName: "Stores",
    sortable: false,
    groupable: true,
    flex: 1,
    renderCell: storeCellRenderer,
  },
  {
    field: "schedule",
    headerName: "Start",
    sortable: false,
    flex: 1,
    renderCell: (params: GridRenderCellParams) =>
      formatScheduledDateTime(params.row.schedule),
  },
  {
    field: "collections",
    headerName: "Product Collections",
    sortable: false,
    flex: 1,
    renderCell: productCollectionsCellRenderer,
  },
  {
    field: "isActive",
    headerName: "Discount Status",
    flex: 1,
    renderCell: (params: GridRenderCellParams) => (
      <DiscountStatusBox isActive={params.row.isActive} />
    ),
  },
  {
    field: "createdAt",
    headerName: "Created Date",
    flex: 0.125,
    minWidth: 120,
    renderCell: (params: any) =>
      new Date(params.row.createdAt).toLocaleDateString("en-US", {
        month: "2-digit",
        day: "2-digit",
        year: "numeric",
      }),
  },
  {
    field: "updatedAt",
    headerName: "Last Update",
    renderCell: (params: any) =>
      new Date(params.row.updatedAt).toLocaleDateString("en-US", {
        month: "2-digit",
        day: "2-digit",
        year: "numeric",
      }),
  },
];

const AutomatedDiscountTable: React.FC<AutomatedDiscountsTableProps> = ({
  discounts,
  openDiscountModal,
  openDiscountLog,
}) => {
  const tableColumns = dataTableColumns;
  const rows = discounts && buildRowsWithHierarchy(discounts);

  const getTreeDataPath: DataGridProProps["getTreeDataPath"] = (
    row: OrgDiscountRow
  ) => row.hierarchy;
  const getRowClassName: DataGridProProps["getRowClassName"] = (
    params: GridRowClassNameParams
  ) => (params.row.isChild ? "DataGrid-Row-Child" : "");

  const rowActionColumn: DataGridProProps["groupingColDef"] = {
    headerName: "",
    width: 100,
    renderCell: (params) => (
      <RowActionButtons
        id={params.id}
        row={params.row}
        rowNode={params.rowNode}
        field={params.field}
        openDiscountModal={openDiscountModal}
        openDiscountLog={openDiscountLog}
      />
    ),
  };

  const apiRef: React.MutableRefObject<GridApiPro> = useGridApiRef();

  return (
    <StyledTableContainer data-testid="automated-discounts-list-container">
      <StyledDataGridContainer data-testid="data-grid-pro-container">
        <StyledDiscountTableHeaderContainer>
          <Typography
            variant="largeTextStrong"
            data-testid="automated-discounts-count"
          >
            {discounts ? (
              `${discounts.length} Discounts`
            ) : (
              <Skeleton width={100} animation="wave" />
            )}
          </Typography>
        </StyledDiscountTableHeaderContainer>
        <DataGridPro
          apiRef={apiRef}
          onFilterModelChange={() => {
            apiRef?.current?.setPage(0);
          }}
          columns={tableColumns}
          rows={rows || []}
          rowCount={discounts ? discounts.length : 0}
          treeData
          loading={!discounts}
          getTreeDataPath={getTreeDataPath}
          groupingColDef={rowActionColumn}
          getRowClassName={getRowClassName}
          columnVisibilityModel={{
            displayTitle: false,
          }}
        />
      </StyledDataGridContainer>
    </StyledTableContainer>
  );
};

export default React.memo(AutomatedDiscountTable);
