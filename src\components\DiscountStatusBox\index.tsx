import React from "react";
import { styled, Box, Typography } from "@mui/material";
import { Icon, convertPxToRem } from "@treez-inc/component-library";

export interface DiscountStatusBoxProps {
  isActive: boolean;
}

const StyledDiscountStatusBox = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "space-around",
  backgroundColor: theme.palette.grey02.main,
  paddingRight: convertPxToRem(6),
  borderRadius: convertPxToRem(4),
  minWidth: convertPxToRem(66),
}));

const DiscountStatusBox: React.FC<DiscountStatusBoxProps> = ({ isActive }) => (
  <StyledDiscountStatusBox data-testid="discount-status-box">
    <Icon
      iconName={isActive ? "Checkmark" : "Deactivate"}
      testId="discount-status-icon"
    />{" "}
    <Typography variant="smallText" data-testid="discount-status">
      {isActive ? "Active" : "Inactive"}
    </Typography>
  </StyledDiscountStatusBox>
);

export default DiscountStatusBox;
