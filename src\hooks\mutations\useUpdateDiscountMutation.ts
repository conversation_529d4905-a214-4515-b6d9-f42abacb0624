import { QueryClient, useMutation } from "@tanstack/react-query";
import ApiService from "../../services/api/apiService";
import { upsertDiscountUrl } from "../../services/apiEndPoints";
import { OrgDiscountReqBody } from "../../interfaces/requestModels";
import { OrgDiscountResponse } from "../../interfaces/responseModels";

const useUpdateDiscountMutation = ({
  api,
  queryClient,
}: {
  api: ApiService;
  queryClient: QueryClient;
}) =>
  useMutation({
    mutationFn: async (
      updatedDiscount: Pick<OrgDiscountReqBody, "id"> &
        Partial<OrgDiscountReqBody>
    ) => {
      const result = await api.put(upsertDiscountUrl, updatedDiscount);
      return result.data;
    },
    onSuccess: (data: OrgDiscountResponse) => {
      queryClient.setQueryData(
        ["discounts"],
        (oldDiscountsList: OrgDiscountResponse[]): OrgDiscountResponse[] => {
          if (oldDiscountsList) {
            const existingDiscountIndex = oldDiscountsList.findIndex(
              (discount) => discount.id === data.id
            );
            if (existingDiscountIndex > -1) {
              const newDiscountsList = [...oldDiscountsList];
              newDiscountsList[existingDiscountIndex] = data;
              return newDiscountsList;
            }
          }
          return oldDiscountsList;
        }
      );
    },
  });

export default useUpdateDiscountMutation;
