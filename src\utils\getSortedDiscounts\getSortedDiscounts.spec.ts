import { getSortedDiscounts } from ".";

describe("getSortedDiscounts()", () => {
  const mockDiscounts = [
    { title: "B Discount", isManual: true },
    { title: "A Discount", isManual: false },
    { title: "C Discount", isManual: false },
    { title: "D Discount", isManual: true },
  ];

  it("should correctly filter and sort automated discounts", () => {
    // @ts-ignore
    const result = getSortedDiscounts(mockDiscounts, false);
    expect(result).toEqual([
      { title: "A Discount", isManual: false },
      { title: "C Discount", isManual: false },
    ]);
  });

  it("should correctly filter and sort manual discounts", () => {
    // @ts-ignore
    const result = getSortedDiscounts(mockDiscounts, true);
    expect(result).toEqual([
      { title: "B Discount", isManual: true },
      { title: "D Discount", isManual: true },
    ]);
  });

  it("should handle an empty array correctly", () => {
    const result = getSortedDiscounts([], true);
    expect(result).toEqual([]);
  });

  it("should sort correctly when titles are the same but other properties differ", () => {
    const additionalDiscounts = [
      ...mockDiscounts,
      { title: "A Discount", isManual: true },
    ];
    // @ts-ignore
    const result = getSortedDiscounts(additionalDiscounts, true);
    expect(result).toEqual([
      { title: "A Discount", isManual: true },
      { title: "B Discount", isManual: true },
      { title: "D Discount", isManual: true },
    ]);
  });
});
