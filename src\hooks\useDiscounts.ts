import { useState } from "react";
import axios from "axios";
import { useQueryClient } from "@tanstack/react-query";
import ApiService from "../services/api/apiService";
import {
  DiscountLogResponse,
  OrgDiscountResponse,
} from "../interfaces/responseModels";
import { OrgDiscountReqBody } from "../interfaces/requestModels";
import usePageLoading from "./usePageLoading";
import useDeleteDiscountMutation from "./mutations/useDeleteDiscountMutation";
import { getDiscountModalMessage } from "../utils";
import { useDrawer } from "../providers/DrawerProvider";
import { useModal } from "../providers/ModalProvider";
import { useSnackbar } from "../providers/SnackbarProvider";
import useUpdateDiscountMutation from "./mutations/useUpdateDiscountMutation";
import {
  getDiscountByIdUrl,
  getDiscountLogHistoryByIdUrl,
} from "../services/apiEndPoints";

export interface DiscountModalStateProps {
  id?: string;
  title: string;
  content: string;
  open: boolean;
  submit?: () => void;
}

export interface DiscountLogDrawerStateProps {
  title: string;
  open: boolean;
  data: {
    discount: OrgDiscountResponse;
    discountLogs: DiscountLogResponse[];
  };
}

const buildOrgDiscountReqBody = (
  discount: OrgDiscountResponse
): OrgDiscountReqBody => {
  const defaultConditions = {
    customerCapEnabled: false,
    customerLimitEnabled: false,
    purchaseMinimumEnabled: false,
    customerEventEnabled: false,
    redemptionLimitEnabled: false,
    fulfillmentTypesEnabled: false,
    customerLicenseTypeEnabled: false,
    packageAgeEnabled: false,
  };

  const defaultManualConditions = {
    customerCapEnabled: false,
    purchaseMinimumEnabled: false,
    redemptionLimitEnabled: false,
  };

  const conditions = {
    ...defaultConditions,
    ...discount.conditions,
  };

  const manualConditions = {
    ...defaultManualConditions,
    ...discount.manualConditions,
  };

  return {
    id: discount.id,
    title: discount.title,
    organizationId: discount.organizationId,
    displayTitle: discount.displayTitle,
    amount: discount.amount,
    method: discount.method,
    isActive: discount.isActive,
    isAdjustment: discount.isAdjustment,
    isCart: discount.isCart,
    isManual: discount.isManual,
    ...(!discount.isManual && {
      isStackable: discount.isStackable,
      conditions,
      collections: discount.collections,
      collectionsRequired: discount.collectionsRequired,
    }),
    showCustomerFacing: discount.showCustomerFacing,
    showEcommerce: discount.showEcommerce,
    showSellTreez: discount.showSellTreez,
    requireCoupon: discount.requireCoupon,
    requireReason: discount.requireReason,
    requirePin: discount.requirePin,
    storeCustomizations:
      discount.storeCustomizations &&
      discount.storeCustomizations.map((store) => ({
        ...store,
        createdAt: new Date(store.createdAt),
        updatedAt: new Date(store.updatedAt),
        isActive: null,
      })),
    schedule: discount.schedule,
    customerGroups: discount.customerGroups,
    manualConditions,
    externalIds: [],
    internalIds: [],
  };
};

const useDiscounts = (api: ApiService) => {
  const [discounts, setDiscounts] = useState<OrgDiscountResponse[]>([]);
  const { isPageLoading, setIsPageLoading } = usePageLoading();
  const { openDrawer, closeDrawer, drawerState } = useDrawer();
  const { openModal, closeModal, modalState } = useModal();
  const { openSnackbar } = useSnackbar();
  const queryClient = useQueryClient();

  const mutateUpdateDiscount = useUpdateDiscountMutation({
    api,
    queryClient,
  });
  const mutateDeleteDiscount = useDeleteDiscountMutation({
    api,
    queryClient,
  });

  const handleDeleteDiscount = async (discount: OrgDiscountResponse) => {
    try {
      await mutateDeleteDiscount.mutateAsync(discount.id);
      openSnackbar({
        iconName: "Success",
        severity: "info",
        message: `"${discount.title}" discount has been deleted`,
      });
    } catch (error: any) {
      // eslint-disable-next-line no-console
      console.error(error);

      let message = "Unable to delete the discount. Please try again";

      if (error.name === "AxiosError") {
        message = "Your request could not be completed due to a network error";
      }
      openSnackbar({
        severity: "error",
        message,
      });
    }
  };

  const handleActivateDiscount = async (discount: OrgDiscountResponse) => {
    try {
      const fullDiscount = await api.get(getDiscountByIdUrl(discount.id));

      await mutateUpdateDiscount.mutateAsync({
        ...buildOrgDiscountReqBody(fullDiscount.data),
        isActive: true,
      });
      closeModal();

      openSnackbar({
        severity: "info",
        message: `"${discount.title}" discount is now active`,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error);

      openSnackbar({
        severity: "error",
        message: `Error activating "${discount.title}" discount`,
      });
    }
  };

  const handleDeactivateDiscount = async (discount: OrgDiscountResponse) => {
    try {
      const fullDiscount = await api.get(getDiscountByIdUrl(discount.id));

      await mutateUpdateDiscount.mutateAsync({
        ...buildOrgDiscountReqBody(fullDiscount.data),
        isActive: false,
      });
      closeModal();

      openSnackbar({
        severity: "info",
        iconName: "Success",
        message: `"${discount.title}" discount is now inactive`,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error);

      openSnackbar({
        severity: "error",
        message: `Error deactivating "${discount.title}" discount`,
      });
    }
  };

  const handleUnassignStore = async (
    parentDiscountId: string,
    storeToUnassignId: string
  ) => {
    try {
      const discountsList: OrgDiscountResponse[] | undefined =
        queryClient.getQueryData(["discounts"]);
      if (!discountsList) return;

      const discountIndex = discountsList.findIndex(
        (discount) => discount.id === parentDiscountId
      );
      const discount = discountsList[discountIndex];

      if (!discount) return;

      const storeIndexToUnassign = discount?.storeCustomizations.findIndex(
        (store) => store.id === storeToUnassignId
      );

      const modifiedDiscount = { ...discount };

      if (storeIndexToUnassign || storeIndexToUnassign >= 0) {
        modifiedDiscount?.storeCustomizations.splice(storeIndexToUnassign, 1);
      } else {
        throw new Error("Store not found");
      }

      await mutateUpdateDiscount.mutateAsync(
        buildOrgDiscountReqBody(modifiedDiscount)
      );
      closeModal();

      openSnackbar({
        severity: "info",
        iconName: "Success",
        message: `Store has been unassigned from "${discount.title}"`,
      });
    } catch (error: Error | unknown) {
      // eslint-disable-next-line no-console
      console.error(error);

      openSnackbar({
        severity: "error",
        message: "There was an error unassigning the store",
      });
    }
  };

  const openDiscountModal = (
    type: string,
    data: {
      discount?: OrgDiscountResponse;
      parentDiscountId?: string;
      storeToUnassignId?: string;
    }
  ) => {
    const { discount, parentDiscountId, storeToUnassignId } = data;

    const modalActions: Record<string, DiscountModalStateProps> = {};

    if (discount) {
      modalActions.delete = {
        id: discount.id,
        title: "Delete Discount",
        content: `Are you sure you want to delete this discount?`,
        open: true,
        submit: () => {
          handleDeleteDiscount(discount!);
        },
      };
      modalActions.deactivate = {
        id: discount.id,
        title: "Deactivate Discount",
        content: getDiscountModalMessage("deactivate", discount),
        open: true,
        submit: () => {
          handleDeactivateDiscount(discount!);
        },
      };
      modalActions.activate = {
        id: discount.id,
        title: "Activate Discount",
        content: getDiscountModalMessage("activate", discount),
        open: true,
        submit: () => {
          handleActivateDiscount(discount!);
        },
      };
    }
    if (parentDiscountId && storeToUnassignId) {
      modalActions.unassign = {
        id: parentDiscountId,
        title: "Unassign Store",
        content: "Are you sure you want to unassign this Store?",
        open: true,
        submit: () => handleUnassignStore(parentDiscountId, storeToUnassignId),
      };
    }

    if (modalActions[type]) {
      openModal({
        title: modalActions[type].title!,
        content: modalActions[type].content!,
        open: modalActions[type].open!,
        submit: modalActions[type].submit,
      });
    }
  };

  const openDiscountLog = async (discount: OrgDiscountResponse) => {
    try {
      const discountLogs = await api.get(
        getDiscountLogHistoryByIdUrl(discount.id)
      );

      openDrawer({
        ...drawerState,
        title: "Discount History Log",
        data: {
          discount,
          discountLogs: discountLogs.data?.sort(
            (logA: DiscountLogResponse, logB: DiscountLogResponse) =>
              logB.createdAt.localeCompare(logA.createdAt)
          ),
        },
      });
    } catch (error) {
      let errorMessage = `Error retrievieng logs for "${discount.title}" discount`;

      if (axios.isAxiosError(error)) {
        const axiosError = error;
        errorMessage = axiosError.response?.data?.errorResponse || errorMessage;
      }

      openSnackbar({
        severity: "error",
        message: errorMessage,
      });
    }
  };

  return {
    discounts,
    setDiscounts,
    isPageLoading,
    setIsPageLoading,
    closeModal,
    modalState,
    handleDeleteDiscount,
    handleActivateDiscount,
    handleDeactivateDiscount,
    handleUnassignStore,
    openDiscountModal,
    openDiscountLog,
    closeDrawer,
    drawerState,
  };
};

export default useDiscounts;
