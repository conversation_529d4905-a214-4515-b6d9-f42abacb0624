import React, { useMemo } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { fireEvent, render, screen, within } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { defaultAutomatedDiscountFormValues as defaultValues } from "../../../../../constants/discountForm";
import SelectStoreStep from "./index";
import { EntityContext } from "../../../../../providers/EntityProvider";
import { EntityResponse } from "../../../../../interfaces/entity";

describe("SelectStoreStep", () => {
  const renderSelectStoreStep = () => {
    const EntityContextProviderWrapper: React.FC<{
      children: React.ReactNode;
    }> = ({ children }) => {
      const providerValue: EntityResponse[] = useMemo(
        () => [
          {
            id: "1",
            name: "Entity 1",
            customerType: "Medical",
            phoneNumber: "1",
            address: {},
            license: [],
          },
          {
            id: "2",
            name: "Entity 2",
            customerType: "Medical",
            phoneNumber: "2",
            address: {},
            license: [],
          },
        ],
        []
      );
      return (
        <EntityContext.Provider value={providerValue}>
          {children}
        </EntityContext.Provider>
      );
    };

    const FormProviderWrapper: React.FC<{ children: React.ReactNode }> = ({
      children,
    }) => {
      const methods = useForm({ defaultValues });
      return <FormProvider {...methods}>{children}</FormProvider>;
    };

    render(
      <TreezThemeProvider>
        <EntityContextProviderWrapper>
          <FormProviderWrapper>
            <SelectStoreStep />
          </FormProviderWrapper>
        </EntityContextProviderWrapper>
      </TreezThemeProvider>
    );

    const { getByTestId, getAllByTestId } = screen;

    const selectStoresStepContainer = getByTestId(
      "automated-select-store-step"
    );

    const storeSearchInputWrapper = getByTestId("store-search-input");

    const storeSearchInput = within(storeSearchInputWrapper).getByRole(
      "searchbox"
    );

    const allStoreCheckbox = within(
      getByTestId("automated-discount-select-all-stores-checkbox")
    ).getByRole("checkbox");

    const entityOneCheckbox = within(getByTestId("store-active-1")).getByRole(
      "checkbox"
    );

    const entityTwoCheckbox = within(getByTestId("store-active-2")).getByRole(
      "checkbox"
    );

    const selectStoresPanel = getByTestId(
      "automated-discount-select-store-panel"
    );

    const selectStoresBoxes = getAllByTestId(
      "automated-discount-select-store-box"
    );

    return {
      selectStoresStepContainer,
      storeSearchInputWrapper,
      storeSearchInput,
      allStoreCheckbox,
      entityOneCheckbox,
      entityTwoCheckbox,
      selectStoresPanel,
      selectStoresBoxes,
    };
  };

  it("Should render the SelectStoreStep container", () => {
    const { selectStoresStepContainer } = renderSelectStoreStep();
    expect(selectStoresStepContainer).toBeInTheDocument();
  });

  it("Should render the 'All stores' checkbox", () => {
    const { allStoreCheckbox } = renderSelectStoreStep();
    expect(allStoreCheckbox).toBeInTheDocument();
  });

  it("Should render the 'All stores' and 'Individual store' checkboxes as checked on initial render", () => {
    const { allStoreCheckbox, entityOneCheckbox, entityTwoCheckbox } =
      renderSelectStoreStep();
    expect(allStoreCheckbox).toBeChecked();
    expect(entityOneCheckbox).toBeChecked();
    expect(entityTwoCheckbox).toBeChecked();
  });

  it("Should de-select all of the store checkboxes when the 'All store' checkbox is unchecked", () => {
    const { allStoreCheckbox, entityOneCheckbox, entityTwoCheckbox } =
      renderSelectStoreStep();
    fireEvent.click(allStoreCheckbox);
    expect(entityOneCheckbox).not.toBeChecked();
    expect(entityTwoCheckbox).not.toBeChecked();
  });

  it("Should re-check all of the store checkboxes when the 'All store' checkbox is re-checked after having been unchecked", () => {
    const { allStoreCheckbox, entityOneCheckbox, entityTwoCheckbox } =
      renderSelectStoreStep();
    fireEvent.click(allStoreCheckbox);
    fireEvent.click(allStoreCheckbox);
    expect(entityOneCheckbox).toBeChecked();
    expect(entityTwoCheckbox).toBeChecked();
  });

  it("Should render the 'All stores' checkbox as unchecked when any stores are not selected", () => {
    const { allStoreCheckbox, entityOneCheckbox } = renderSelectStoreStep();
    fireEvent.click(entityOneCheckbox);
    expect(allStoreCheckbox).not.toBeChecked();
  });

  it("Should render the 'All stores' checkbox as checked when a store checkbox is unchecked and re-checked", () => {
    const { allStoreCheckbox, entityOneCheckbox } = renderSelectStoreStep();
    fireEvent.click(entityOneCheckbox);
    fireEvent.click(entityOneCheckbox);
    expect(allStoreCheckbox).toBeChecked();
  });

  it("Should render the Select Stores panel", () => {
    const { selectStoresPanel } = renderSelectStoreStep();
    expect(selectStoresPanel).toBeInTheDocument();
  });

  it("Should render the title", () => {
    const { selectStoresPanel } = renderSelectStoreStep();
    expect(selectStoresPanel).toHaveTextContent("Select Stores");
  });

  it("Should render the subtitle", () => {
    const { selectStoresPanel } = renderSelectStoreStep();
    expect(selectStoresPanel).toHaveTextContent(
      "Select the stores that will use this discount."
    );
  });

  it("Should render the SearchInput", () => {
    const { storeSearchInputWrapper } = renderSelectStoreStep();
    expect(storeSearchInputWrapper).toBeInTheDocument();
  });

  it("Should only render stores that match the search query", () => {
    const { storeSearchInput, entityOneCheckbox, entityTwoCheckbox } =
      renderSelectStoreStep();
    fireEvent.change(storeSearchInput, { target: { value: "1" } });
    expect(entityOneCheckbox).toBeInTheDocument();
    expect(entityTwoCheckbox).not.toBeInTheDocument();
  });

  it("Should check only the matched store when checking the 'All search results' checkbox after searching", () => {
    const {
      storeSearchInput,
      allStoreCheckbox,
      entityOneCheckbox,
      entityTwoCheckbox,
    } = renderSelectStoreStep();
    fireEvent.click(allStoreCheckbox);
    fireEvent.change(storeSearchInput, { target: { value: "2" } });
    fireEvent.click(allStoreCheckbox);
    fireEvent.change(storeSearchInput, { target: { value: "" } });
    expect(entityOneCheckbox).not.toBeChecked();
    expect(entityTwoCheckbox).toBeChecked();
  });

  it("Should render the select store boxes", () => {
    const { selectStoresBoxes } = renderSelectStoreStep();
    expect(selectStoresBoxes[0]).toBeInTheDocument();
    expect(selectStoresBoxes[1]).toBeInTheDocument();
  });

  it("Should render the correct number of stores", () => {
    const { selectStoresBoxes } = renderSelectStoreStep();
    expect(selectStoresBoxes).toHaveLength(2);
  });

  it("Should render the store checkboxes", () => {
    const { entityOneCheckbox, entityTwoCheckbox } = renderSelectStoreStep();
    expect(entityOneCheckbox).toBeInTheDocument();
    expect(entityTwoCheckbox).toBeInTheDocument();
  });

  it("Should render the correct number of results for various searches", () => {
    const { selectStoresPanel, storeSearchInput } = renderSelectStoreStep();
    fireEvent.change(storeSearchInput, { target: { value: "Entity" } });
    expect(selectStoresPanel).toHaveTextContent("2 stores found under ");
    fireEvent.change(storeSearchInput, { target: { value: "1" } });
    expect(selectStoresPanel).toHaveTextContent("1 stores found under ");
    fireEvent.change(storeSearchInput, { target: { value: "foo" } });
    expect(selectStoresPanel).toHaveTextContent("0 stores found under ");
  });
});
