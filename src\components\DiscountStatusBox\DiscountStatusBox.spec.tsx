import React from "react";
import { render, screen } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import iconAriaLabels from "@treez-inc/component-library/dist/components/Icon/icon-library/icon-aria-labels";
import DiscountStatusBox, { DiscountStatusBoxProps } from ".";

describe("DiscountStatusBox", () => {
  const renderDiscountStatusBox = (props: DiscountStatusBoxProps) => {
    render(
      <TreezThemeProvider>
        <DiscountStatusBox {...props} />
      </TreezThemeProvider>
    );

    const { getByTestId } = screen;

    const discountStatusBox = getByTestId("discount-status-box");
    const discountStatusIcon = getByTestId(
      "discount-status-icon"
    ).querySelector("span");
    const discountStatus = getByTestId("discount-status");

    return {
      discountStatusBox,
      discountStatusIcon,
      discountStatus,
    };
  };

  it("renders the <DiscountStatusBox /> component", () => {
    const { discountStatusBox } = renderDiscountStatusBox({
      isActive: true,
    });

    expect(discountStatusBox).toBeInTheDocument();
  });
  it('renders with "Active" status and checkmark icon when isActive = true', () => {
    const { discountStatusIcon, discountStatus } = renderDiscountStatusBox({
      isActive: true,
    });

    expect(discountStatusIcon).toHaveAccessibleName(iconAriaLabels.Checkmark);
    expect(discountStatus).toHaveTextContent("Active");
  });

  it('renders with "Inactive" status and deactivate icon when isActive = false', () => {
    const { discountStatusIcon, discountStatus } = renderDiscountStatusBox({
      isActive: false,
    });
    expect(discountStatusIcon).toHaveAccessibleName(iconAriaLabels.Deactivate);
    expect(discountStatus).toHaveTextContent("Inactive");
  });
});
