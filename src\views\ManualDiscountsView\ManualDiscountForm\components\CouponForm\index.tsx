import React from "react";
import {
  Control,
  Controller,
  FieldErrors,
  UseFormTrigger,
} from "react-hook-form";
import { Box, styled } from "@mui/material";
import {
  Button,
  Input,
  Panel,
  convertPxToRem,
} from "@treez-inc/component-library";
import { CouponFormModel } from "../../../../../interfaces/couponFormModel";
import { ManualDiscountFormData } from "../../../../../interfaces/discounts";
import CouponPanel from "../CouponPanel";
import { validateCouponCode } from "../../../../../utils";

export interface CouponFormProps {
  trigger: UseFormTrigger<ManualDiscountFormData>;
  control: Control<ManualDiscountFormData>;
  errors?: FieldErrors<ManualDiscountFormData>;
}

const AddCouponButtonContainer = styled(Box)({
  marginTop: convertPxToRem(20),
});

const CouponForm: React.FC<CouponFormProps> = ({
  trigger,
  control,
  errors,
}) => {
  const handleCouponNameChange = (
    couponForm: CouponFormModel,
    value: string
  ) => ({ ...couponForm, couponToAdd: value });

  const scrollTo = (id: string) => {
    setTimeout(() => {
      const element = document.getElementById(id);

      if (!element) return;

      element.scrollIntoView({ behavior: "smooth" });
    }, 300);
  };

  const validateAddCoupon = async () => trigger("couponFormModel.couponToAdd");

  const computeStateAddCoupon = (couponForm: CouponFormModel) => ({
    coupons: [
      ...couponForm.coupons,
      {
        code: couponForm.couponToAdd,
        startDate: new Date(),
        isAllDay: true,
        ignoreExpiration: true,
      },
    ],
    couponToAdd: "",
  });

  const computeStateRemoveCoupon = (
    couponForm: CouponFormModel,
    couponIdx: number
  ) => {
    const coupons = [...couponForm.coupons];
    coupons.splice(couponIdx, 1);

    return {
      ...couponForm,
      coupons,
    };
  };

  const handleExistingCouponChange = (
    couponForm: CouponFormModel,
    couponIdx: number,
    field: string,
    value: any
  ) => {
    const coupon = {
      ...couponForm.coupons[couponIdx],
      [field]: value,
    };

    if (field === "isAllDay" && value === true) {
      coupon.startTime = undefined;
      coupon.endTime = undefined;
    }

    if (field === "ignoreExpiration" && value === true) {
      coupon.endDate = undefined;
      coupon.endTime = undefined;
    }

    const coupons = [...couponForm.coupons];
    coupons[couponIdx] = coupon;

    return {
      ...couponForm,
      coupons,
    };
  };

  const handleAddCoupon = async (
    couponForm: CouponFormModel,
    onCouponsChange: (...event: any[]) => void
  ) => {
    if (await validateAddCoupon()) {
      scrollTo(`coupon-row-${couponForm.couponToAdd}`);
      onCouponsChange(computeStateAddCoupon(couponForm));
    }
  };

  return (
    <Controller
      control={control}
      name="couponFormModel"
      render={({ field: { onChange: onCouponsChange, value: couponForm } }) => (
        <>
          <Panel
            testId="add-coupon-form"
            title="Coupons"
          >
            <Controller
              control={control}
              name="couponFormModel.couponToAdd"
              rules={{
                validate: validateCouponCode(couponForm.coupons, false),
              }}
              render={() => (
                <Input
                  testId="add-coupon-name-input"
                  label="Coupon Name"
                  value={couponForm.couponToAdd}
                  helperText={errors?.couponFormModel?.couponToAdd?.message?.toString()}
                  error={
                    !!errors?.couponFormModel?.couponToAdd?.message?.toString()
                  }
                  onChange={(evt) =>
                    onCouponsChange(
                      handleCouponNameChange(couponForm, evt.target.value)
                    )
                  }
                />
              )}
            />
            <AddCouponButtonContainer>
              <Button
                iconName="Add"
                label="Add Coupon"
                disabled={!couponForm.couponToAdd}
                onClick={() => {
                  handleAddCoupon(couponForm, onCouponsChange);
                }}
                testId="add-coupon-button"
                variant="secondary"
              />
            </AddCouponButtonContainer>
          </Panel>

          {couponForm.coupons.map((coupon, couponIndex) => {
            const couponErrors =
              errors?.couponFormModel?.coupons &&
              errors?.couponFormModel?.coupons[couponIndex];

            const couponKey = coupon.id
              ? `coupon-${coupon.id}`
              : `coupon-${couponIndex}}`;

            return (
              <CouponPanel
                key={couponKey}
                control={control}
                couponIdx={couponIndex}
                coupon={coupon}
                existingCoupons={couponForm.coupons}
                couponErrors={couponErrors}
                onDelete={() => {
                  onCouponsChange(
                    computeStateRemoveCoupon(couponForm, couponIndex)
                  );
                }}
                onFieldChange={(field, value) =>
                  onCouponsChange(
                    handleExistingCouponChange(
                      couponForm,
                      couponIndex,
                      field,
                      value
                    )
                  )
                }
              />
            );
          })}
        </>
      )}
    />
  );
};

export default CouponForm;
