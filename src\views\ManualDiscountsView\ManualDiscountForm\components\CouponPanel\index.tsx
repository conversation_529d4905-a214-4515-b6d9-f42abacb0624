import React from "react";
import { Control, Controller, FieldErrorsImpl } from "react-hook-form";
import { Box, Grid, Typography, styled } from "@mui/material";
import {
  Accordion,
  Button,
  Checkbox,
  DatePicker,
  Input,
  convertPxToRem,
} from "@treez-inc/component-library";
import dayjs from "dayjs";
import { Coupon } from "../../../../../interfaces/coupon";
import { ManualDiscountFormData } from "../../../../../interfaces/discounts";
import TimePicker from "../../../../../components/TimePicker";
import { validateCouponCode } from "../../../../../utils";

export interface CouponPanelProps {
  control: Control<ManualDiscountFormData>;
  couponIdx: number;
  coupon: Coupon;
  existingCoupons: Coupon[];
  couponErrors: FieldErrorsImpl<Coupon> | undefined;
  onDelete: () => void;
  onFieldChange: (field: string, value: any) => void;
}

const StyledCouponPanel = styled(Box)(({ theme }) => ({
  padding: convertPxToRem(24),
  borderRadius: convertPxToRem(16),
  border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}}`,
  "&.MuiAccordionSummary-root": {
    height: "unset !important",
  },
  "& .MuiAccordionDetails-root": {
    padding: `0 !important`,
  },
}));

const CouponDetailsWrapper = styled(Box)({
  marginTop: convertPxToRem(8),
});

const StyledInputWrapper = styled(Grid)({
  marginTop: convertPxToRem(6),
});

const ValidityPeriodWrapper = styled(Box)({
  width: "100%",
  display: "flex",
  marginTop: convertPxToRem(30),
  flexDirection: "column",
});

const StyledValidityDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.secondaryText.main,
  marginTop: convertPxToRem(12),
  marginBottom: convertPxToRem(12),
}));

const StyledStartEndContainer = styled(Grid)({
  display: "flex",
  justifyContent: "space-between",
  marginBottom: convertPxToRem(12),
});

const StyledDateTimeInputContainer = styled(Box)({
  "& .MuiTextField-root": {
    width: "100%",
  },
});

const CouponPanel: React.FC<CouponPanelProps> = ({
  control,
  couponIdx,
  coupon,
  existingCoupons,
  couponErrors,
  onDelete,
  onFieldChange,
}) => {
  const [isExpanded, setIsExpanded] = React.useState(true);
  const handleChange = () => {
    setIsExpanded((prevExpanded) => !prevExpanded);
  };

  return (
    <StyledCouponPanel data-testid="coupon-panel">
      <Accordion
        label={`#${coupon.code}`}
        labelElement={
          <Button
            testId="coupon-delete-button"
            label="Delete"
            iconName="Delete"
            onClick={onDelete}
            variant="secondary"
            small
          />
        }
        expanded={isExpanded}
        onChange={handleChange}
      >
        <CouponDetailsWrapper>
          <Controller
            control={control}
            name={`couponFormModel.coupons.${couponIdx}.code`}
            rules={{
              required: {
                value: true,
                message: "Coupon name is required",
              },
              validate: validateCouponCode(existingCoupons, true),
            }}
            render={() => (
              <Input
                testId="coupon-code-input"
                label="Coupon Name"
                value={coupon.code}
                required
                helperText={couponErrors?.code?.message?.toString()}
                error={!!couponErrors?.code?.message?.toString()}
                onChange={(event) => onFieldChange("code", event.target.value)}
              />
            )}
          />
          <ValidityPeriodWrapper>
            <Typography variant="largeText">
              Validity Period (Optional)
            </Typography>
            <StyledValidityDescription variant="mediumText">
              Set the date and time the coupon will be redeemable for the
              customer.
            </StyledValidityDescription>

            <StyledStartEndContainer container spacing={2}>
              <StyledInputWrapper item xs={12} lg={6} md={6} sm={12}>
                <Controller
                  control={control}
                  name={`couponFormModel.coupons.${couponIdx}.startDate`}
                  rules={{
                    required: {
                      value: true,
                      message: "Start date is required",
                    },
                  }}
                  render={() => (
                    <StyledDateTimeInputContainer>
                      <DatePicker
                        testId="coupon-start-date-picker"
                        label="Start date"
                        value={coupon.startDate && dayjs(coupon.startDate)}
                        helperText={couponErrors?.startDate?.message?.toString()}
                        error={!!couponErrors?.startDate?.message?.toString()}
                        onChange={(value) =>
                          onFieldChange("startDate", value.$d)
                        }
                        required
                      />
                    </StyledDateTimeInputContainer>
                  )}
                />
              </StyledInputWrapper>

              <StyledInputWrapper item xs={12} lg={6} md={6} sm={12}>
                <Controller
                  control={control}
                  name={`couponFormModel.coupons.${couponIdx}.startTime`}
                  rules={{
                    required: {
                      value: !coupon.isAllDay,
                      message: "Start time is required",
                    },
                  }}
                  render={() => (
                    <StyledDateTimeInputContainer>
                      <TimePicker
                        testId="coupon-start-time-picker"
                        label="Start time"
                        value={coupon.startTime && dayjs(coupon.startTime)}
                        disabled={coupon.isAllDay}
                        required={!coupon.isAllDay}
                        onChange={(value) =>
                          onFieldChange("startTime", value.$d)
                        }
                        helperText={couponErrors?.startTime?.message?.toString()}
                        error={!!couponErrors?.startTime?.message?.toString()}
                      />
                    </StyledDateTimeInputContainer>
                  )}
                />
              </StyledInputWrapper>

              <StyledInputWrapper item>
                <Checkbox
                  testId="coupon-is-all-day-checkbox"
                  label="All day"
                  onChange={(checked) => onFieldChange("isAllDay", checked)}
                  value={coupon.isAllDay}
                  checked={coupon.isAllDay}
                />
              </StyledInputWrapper>
            </StyledStartEndContainer>
            <StyledStartEndContainer container spacing={2}>
              <StyledInputWrapper item xs={12} lg={6} md={6} sm={12}>
                <Controller
                  control={control}
                  name={`couponFormModel.coupons.${couponIdx}.endDate`}
                  rules={{
                    required: {
                      value: !coupon.ignoreExpiration,
                      message: "End date is required",
                    },
                    validate: (value) => {
                      if (value) {
                        const { startDate, startTime, endTime } = coupon;

                        if (startTime) {
                          startDate.setHours(startTime.getHours());
                          startDate.setMinutes(startTime.getMinutes());
                        }

                        if (endTime) {
                          value.setHours(endTime.getHours());
                          value.setMinutes(endTime.getMinutes());
                        }

                        if (startDate >= value) {
                          return "End date must be after start date";
                        }
                      }

                      return true;
                    },
                  }}
                  render={() => (
                    <StyledDateTimeInputContainer>
                      <DatePicker
                        testId="coupon-end-date-picker"
                        label="Expiration date"
                        value={coupon.endDate && dayjs(coupon.endDate)}
                        helperText={couponErrors?.endDate?.message?.toString()}
                        error={!!couponErrors?.endDate?.message?.toString()}
                        onChange={(value) => onFieldChange("endDate", value.$d)}
                        required={!coupon.ignoreExpiration}
                        disabled={coupon.ignoreExpiration}
                      />
                    </StyledDateTimeInputContainer>
                  )}
                />
              </StyledInputWrapper>

              <StyledInputWrapper item xs={12} lg={6} md={6} sm={12}>
                <Controller
                  control={control}
                  name={`couponFormModel.coupons.${couponIdx}.endTime`}
                  rules={{
                    required: {
                      value: !coupon.isAllDay && !coupon.ignoreExpiration,
                      message: "End time is required",
                    },
                  }}
                  render={() => (
                    <StyledDateTimeInputContainer>
                      <TimePicker
                        testId="coupon-end-time-picker"
                        label="End time"
                        value={coupon.endTime && dayjs(coupon.endTime)}
                        onChange={(value) => onFieldChange("endTime", value.$d)}
                        helperText={couponErrors?.endTime?.message?.toString()}
                        error={!!couponErrors?.endTime?.message?.toString()}
                        disabled={coupon.isAllDay || coupon.ignoreExpiration}
                        required={!coupon.isAllDay && !coupon.ignoreExpiration}
                      />
                    </StyledDateTimeInputContainer>
                  )}
                />
              </StyledInputWrapper>
              <StyledInputWrapper item>
                <Checkbox
                  testId="coupon-does-not-expire-checkbox"
                  label="Does not expire"
                  onChange={(checked) =>
                    onFieldChange("ignoreExpiration", checked)
                  }
                  value={coupon.ignoreExpiration}
                  checked={coupon.ignoreExpiration}
                />
              </StyledInputWrapper>
            </StyledStartEndContainer>
          </ValidityPeriodWrapper>
        </CouponDetailsWrapper>
      </Accordion>
    </StyledCouponPanel>
  );
};

export default CouponPanel;
