import {
  validateDiscountAmountInput,
  validateCouponCode,
  validateRequireReasonDiscountType,
} from ".";
import {
  MANUAL_DISCOUNT_METHODS as DiscountMethod,
  AUTOMATED_DISCOUNT_METHODS as AutomatedDiscountMethod,
} from "../../constants/discountForm";
import { Coupon } from "../../interfaces/coupon";
import { ManualDiscountFormData } from "../../interfaces/discounts";

describe("Validations", () => {
  const couponList: Coupon[] = [
    { code: "ABC123", startDate: new Date("01-02-2025") },
    { code: "XYZ456", startDate: new Date("01-02-2025") },
    { code: "DEF789", startDate: new Date("01-02-2025") },
  ];

  describe("validateDiscountAmountInput()", () => {
    it("should validate input for dollar amount", () => {
      const validationDollarInput = validateDiscountAmountInput(
        DiscountMethod.DOLLAR
      );

      expect(validationDollarInput("abc")).toBe(
        "Please enter a number greater than 0"
      );

      expect(validationDollarInput("-10")).toBe(
        "Please enter a number greater than 0"
      );

      expect(validationDollarInput("123.4567")).toBe(
        "Please enter a maximum of 2 decimal places"
      );

      expect(validationDollarInput("10")).toBe(true);
      expect(validationDollarInput(".12")).toBe(true);
      expect(validationDollarInput("99.99")).toBe(true);
    });

    it("should validate input for percent amount", () => {
      const validatePercentageInput = validateDiscountAmountInput(
        DiscountMethod.PERCENT
      );

      expect(validatePercentageInput("abc")).toBe(
        "Please enter a number greater than 0"
      );

      expect(validatePercentageInput("-10")).toBe(
        "Please enter a number greater than 0"
      );

      expect(validatePercentageInput("101")).toBe(
        "Please enter a number smaller than or equal to 100%"
      );

      expect(validatePercentageInput("12.4567")).toBe(
        "Please enter a maximum of 2 decimal places"
      );

      expect(validatePercentageInput("10")).toBe(true);
      expect(validatePercentageInput(".10")).toBe(true);
      expect(validatePercentageInput("99.99")).toBe(true);
    });

    it("should validate input for cost plus amount", () => {
      const validateCostPlusInput = validateDiscountAmountInput(
        AutomatedDiscountMethod.COST_PLUS
      );

      expect(validateCostPlusInput("abc")).toBe(
        "Please enter a number greater than 0"
      );

      expect(validateCostPlusInput("-10")).toBe(
        "Please enter a number greater than 0"
      );

      expect(validateCostPlusInput("12.4567")).toBe(
        "Please enter a maximum of 2 decimal places"
      );

      expect(validateCostPlusInput("10")).toBe(true);
      expect(validateCostPlusInput(".10")).toBe(true);
      expect(validateCostPlusInput("99.99")).toBe(true);
    });
  });

  describe("validateCouponCode", () => {
    it("should validate coupon code", () => {
      const validateCoupon = validateCouponCode(couponList);

      expect(validateCoupon(" ")).toBe("Spaces are not allowed");
      expect(validateCoupon("A")).toBe(
        "Length must be between 3 and 255 characters"
      );
      expect(validateCoupon("ABCDEF123")).toBe(true);
      expect(validateCoupon("ABC123")).toBe("Coupons should not be duplicated");
    });
  });

  describe("validateRequireReasonDiscountType", () => {
    const manualDiscount: Partial<ManualDiscountFormData> = {
      title: "Test Discount",
      amount: "10",
      method: "DOLLAR",
      isActive: true,
      isManual: true,
      requireCoupon: true,
      couponFormModel: {
        coupons: [],
      },
    };

    it("should validate require reason discount type", () => {
      expect(
        validateRequireReasonDiscountType(true, {
          ...manualDiscount,
          requireReason: true,
          couponFormModel: {
            coupons: couponList,
          },
        } as ManualDiscountFormData)
      ).toBe("Require Reason cannot be used with coupons");

      expect(
        validateRequireReasonDiscountType(false, {
          ...manualDiscount,
          requireReason: false,
        } as ManualDiscountFormData)
      ).toBe(true);
    });
  });
});
