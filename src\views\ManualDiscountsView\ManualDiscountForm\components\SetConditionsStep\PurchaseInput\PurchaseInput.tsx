import React from "react";
import {
  Input,
  Select,
  SelectProps,
  convertPxToRem,
} from "@treez-inc/component-library";
import { Control, Controller, Path } from "react-hook-form";
import { Box, styled } from "@mui/material";
import { validateDiscountAmountInput } from "../../../../../../utils/validations";
import { AutomatedDiscountMethods } from "../../../../../../constants/discounts";
import { ManualDiscountFormData } from "../../../../../../interfaces/discounts";

const StyledInputContainer = styled(Box)({
  display: "grid",
  gridTemplateColumns: "1fr 1fr",
  gap: convertPxToRem(16),
});

export interface PurchaseInputProps {
  inputName: Path<ManualDiscountFormData>;
  inputLabel?: string;
  selectName: Path<ManualDiscountFormData>;
  selectLabel?: string;
  control: Control<ManualDiscountFormData>;
  options: SelectProps["menuItems"];
}

const PurchaseInput = ({
  inputName,
  inputLabel,
  selectName,
  selectLabel,
  control,
  options,
}: PurchaseInputProps) => (
  <StyledInputContainer data-testid="purchase-input">
    <Controller
      name={inputName}
      control={control}
      rules={{
        required: "Min amount is required",
        validate: (value) => validateDiscountAmountInput(AutomatedDiscountMethods.DOLLAR)(value as string),
      }}
      render={({ field, fieldState: { error } }) => {
        const { ref, value, ...rest } = field;
        return (
          <Input
            {...rest}
            value={value || ""}
            testId="amount-input"
            label={inputLabel ?? "Min Amount"}
            error={!!error}
            helperText={error?.message}
            required
            type="number"
          />
        );
      }}
    />
    <Controller
      name={selectName}
      control={control}
      rules={{ required: "Purchase amount type is required" }}
      render={({ field, fieldState: { error } }) => {
        const { ref, value, ...rest } = field;
        return (
          <Select
            {...rest}
            value={value || ""}
            testId="purchase-amount-select"
            label={selectLabel ?? "Purchase Amount Type"}
            menuItems={options}
            error={!!error}
            helperText={error?.message}
          />
        );
      }}
    />
  </StyledInputContainer>
);

export default PurchaseInput;
