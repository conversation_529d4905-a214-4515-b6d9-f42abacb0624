import React from "react";
import { <PERSON><PERSON>erRouter as Router } from "react-router-dom";
import { render, screen } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import Footer, { FooterProps } from ".";

const testPrimaryButtonProps = {
  label: "Next",
  onClick: () => {},
  testId: "test-primary-button",
};

const testSecondaryButtonProps = {
  label: "Back",
  onClick: () => {},
  testId: "test-secondary-button",
};

const testTertiaryButtonProps = {
  label: "Done Editing",
  onClick: () => {},
  testId: "test-tertiary-button",
};

describe("<Footer />", () => {
  const totalSteps = 5;

  const renderFooter = (
    props: Pick<
      FooterProps,
      | "activeStep"
      | "secondaryButtonProps"
      | "tertiaryButtonProps"
      | "totalSteps"
    >
  ) => {
    render(
      <Router>
        <TreezThemeProvider>
          <Footer
            {...props}
            primaryButtonProps={testPrimaryButtonProps}
            testId="test-footer"
            totalSteps={totalSteps}
          />
        </TreezThemeProvider>
      </Router>
    );

    const { getByTestId, queryByTestId } = screen;

    const footer = getByTestId("test-footer");
    const primaryButton = getByTestId("test-primary-button");
    const secondaryButton = () => queryByTestId("test-secondary-button");
    const tertiaryButton = () => queryByTestId("test-tertiary-button");

    return {
      footer,
      primaryButton,
      secondaryButton,
      tertiaryButton,
    };
  };

  it("should render the <Footer /> component", () => {
    const { footer } = renderFooter({ activeStep: 0 });
    expect(footer).toBeInTheDocument();
  });

  it("should render the primary button", () => {
    const { primaryButton } = renderFooter({ activeStep: 0 });
    expect(primaryButton).toBeInTheDocument();
  });

  describe("secondary button", () => {
    it("should render the secondary button when secondaryButtonProps is passed and activeStep is > 0", () => {
      const { secondaryButton } = renderFooter({
        activeStep: 1,
        secondaryButtonProps: testSecondaryButtonProps,
      });

      expect(secondaryButton()).toBeInTheDocument();
    });

    it("should not render the secondary button when secondaryButtonProps is passed and activeStep is 0", () => {
      const { secondaryButton } = renderFooter({
        activeStep: 0,
        secondaryButtonProps: testSecondaryButtonProps,
      });

      expect(secondaryButton()).not.toBeInTheDocument();
    });
  });

  describe("tertiary button", () => {
    it("should render the tertiary button when tertiaryButtonProps is passed and not the last step", () => {
      const { tertiaryButton } = renderFooter({
        activeStep: 0,
        tertiaryButtonProps: testTertiaryButtonProps,
      });

      expect(tertiaryButton()).toBeInTheDocument();
    });

    it("should not render the tertiary button when tertiaryButtonProps is passed and the last step", () => {
      const { tertiaryButton } = renderFooter({
        activeStep: totalSteps,
        tertiaryButtonProps: testTertiaryButtonProps,
      });

      expect(tertiaryButton()).not.toBeInTheDocument();
    });
  });
});
