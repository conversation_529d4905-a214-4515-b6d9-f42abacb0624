import { getBannerTitle } from ".";
import { testDiscountsResponse, entities } from "../../../test/constants";
import { testAutomatedDiscountsResponse } from "../../../test/fixtures";

describe("getBannerTitle", () => {
  it("should return an empty string if no orgDiscount is passed", () => {
    const title = getBannerTitle({ orgDiscount: undefined });

    expect(title).toEqual("");
  });

  it("should return the Edit string if orgDiscount is passed", () => {
    const title = getBannerTitle({
      orgDiscount: testAutomatedDiscountsResponse[0],
    });

    expect(title).toEqual("Edit Discount");
  });

  describe("Store Customization Edit", () => {
    it("should return an empty string if entities and storeCustomizationId are provided, but no entity matches the storeCustomizationId", () => {
      const title = getBannerTitle({
        orgDiscount: testDiscountsResponse[0],
        entities,
        storeCustomizationId: "clearlyNotARealStoreCustomizationId",
      });

      expect(title).toEqual("");
    });

    it("should return the Edit string with the correct storename appended", () => {
      const title = getBannerTitle({
        orgDiscount: testDiscountsResponse[0],
        entities,
        storeCustomizationId: "disc1-store1",
      });

      expect(title).toEqual('Edit Discount - "Entity"');
    });
  });
});
