import React, { useState } from "react";
import { Accordion, Panel, convertPxToRem } from "@treez-inc/component-library";
import { Box, Typography, styled } from "@mui/material";

export interface AccordionPanelProps {
  title: string;
  subtitle?: string;
  children?: React.ReactNode;
  testId?: string;
  expanded?: boolean;
}

const StyledAccordionPanel = styled(Box)({
  marginTop: convertPxToRem(16),
  // Panel
  "> div": {
    padding: `0 ${convertPxToRem(16)}`,
    // Accordion Details
    "> div > .MuiCollapse-root > div > div > div > div": {
      padding: `0 0 ${convertPxToRem(16)} 0`,
    },
  },
});

const StyledAccordionSubtitle = styled(Typography)(({ theme }) => ({
  ...theme.typography.mediumText,
  color: theme.palette.secondaryText.main,
  marginBottom: convertPxToRem(12),
}));

const AccordionPanel = ({
  title,
  subtitle,
  children,
  testId,
  expanded = false,
}: AccordionPanelProps) => {
  const [isExpanded, setIsExpanded] = useState(expanded);

  return (
    <StyledAccordionPanel data-testid={testId && `${testId}-accordion-panel`}>
      <Panel>
        <Accordion
          label={title}
          expanded={isExpanded}
          onChange={() => setIsExpanded((expandValue) => !expandValue)}
        >
          <StyledAccordionSubtitle>{subtitle}</StyledAccordionSubtitle>
          {children}
        </Accordion>
      </Panel>
    </StyledAccordionPanel>
  );
};

export default AccordionPanel;
