import React, { useEffect, useRef, useState } from "react";
import { Grid, Typography, styled } from "@mui/material";
import {
  Checkbox,
  Icon,
  IconButton,
  Link,
  Panel,
  SearchInput,
  Tooltip,
  convertPxToRem,
} from "@treez-inc/component-library";
import { Controller, useFormContext } from "react-hook-form";
import { ProductCollectionResponse } from "../../../../../../interfaces/responseModels";
import { ProductCollection } from "../../../../../../interfaces/discounts";
import { useProductCollectionsQuery } from "../../../../../../hooks";
import {
  ProductCollectionItemSkeleton,
  StyledCollectionItem,
} from "./ProductCollectionItemSkeleton";

/* eslint-disable-next-line prefer-destructuring */
const PAGE_URL = process.env.PAGE_URL;

export interface ProductCollectionsSelectionCardProps {
  testId?: string;
  title: string;
  subtitle: string;
  formName: string;
  errorMessage: string;
}

const StyledTitle = styled(Typography)(({ theme }) => ({
  ...theme.typography.h6,
  color: theme.palette.primaryBlackText.main,
  marginBottom: convertPxToRem(12),
}));

const StyledSubtitle = styled(Typography)(({ theme }) => ({
  ...theme.typography.mediumText,
  color: theme.palette.secondaryText.main,
  marginBottom: convertPxToRem(24),
}));

const StyledCollectionsWrapper = styled("div")({
  display: "flex",
  flexDirection: "column",
  flex: 1,
  overflow: "auto",
});

const StyledProductCollectionsToolbar = styled("div")({
  marginBottom: convertPxToRem(6),
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
});

const StyledSelectAllCheckboxWrapper = styled("div")({
  display: "flex",
  gap: convertPxToRem(4),
  alignItems: "center",
});

const StyledProductCollectionsList = styled("div")({
  maxHeight: convertPxToRem(380),
  overflowY: "auto",
});

const StyledVerticalCenteringBox = styled("div")({
  display: "flex",
  alignItems: "center",
});

const IconLink = styled("div")({
  marginTop: convertPxToRem(6),
  marginLeft: convertPxToRem(4),
});

const ProductCollectionsSelectionCard = ({
  testId,
  title,
  subtitle,
  formName,
  errorMessage,
}: ProductCollectionsSelectionCardProps) => {
  const { control, getValues, setValue } = useFormContext();
  const {
    data: productCollections,
    isSuccess,
    isRefetching,
    isFetched,
    refetch,
  } = useProductCollectionsQuery();
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCollections, setFilteredCollections] = useState<
    ProductCollectionResponse[]
  >(isSuccess ? productCollections : []);

  const orgDiscountCollections = useRef<ProductCollection[]>(
    getValues(formName)
  );

  const [selectedItems, setSelectedItems] = useState<Record<string, boolean>>(
    Object.fromEntries(
      filteredCollections.map((collection) => [collection.id, false])
    )
  );

  useEffect(() => {
    if (!isRefetching && isFetched && isSuccess) {
      setSelectedItems(
        Object.fromEntries(
          productCollections.map((collection) => [
            collection.id,
            orgDiscountCollections.current.some(
              ({ productCollectionId }) => productCollectionId === collection.id
            ),
          ])
        )
      );

      const orderedCollections = productCollections?.sort((a, b) => {
        /* First Sort Selected To The Top */
        const aIsSelected = orgDiscountCollections.current.some(
          ({ productCollectionId }) => productCollectionId === a.id
        );

        const bIsSelected = orgDiscountCollections.current.some(
          ({ productCollectionId }) => productCollectionId === b.id
        );

        if (aIsSelected && !bIsSelected) {
          return -1;
        }

        if (bIsSelected && !aIsSelected) {
          return 1;
        }

        /* Then sort by most recently updated */
        return a.updatedAt > b.updatedAt ? -1 : 1;
      });

      setFilteredCollections(
        searchTerm
          ? orderedCollections?.filter((collection) =>
              collection.name.toLowerCase().includes(searchTerm)
            )
          : orderedCollections
      );
    }
  }, [isRefetching, isFetched, isSuccess]);

  useEffect(() => {
    if (selectedItems) {
      const selectedCollections = productCollections?.filter(
        ({ id }) => selectedItems[id]
      );

      if (selectedCollections && selectedCollections.length > 0) {
        setValue(
          formName,
          selectedCollections.map((collection) => ({
            ...collection,
            // Use the id from orgDiscountCollections if in edit mode
            id:
              orgDiscountCollections.current.length > 0
                ? orgDiscountCollections.current.find(
                    (orgCollection) =>
                      orgCollection.productCollectionId === collection.id
                  )?.id
                : undefined,
            productCollectionId: collection.id,
            productCollectionName: collection.name,
          }))
        );
      }
    }
  }, [selectedItems, orgDiscountCollections]);

  const handleCollectionSearch = (
    event: React.ChangeEvent<HTMLInputElement> | null
  ) => {
    const searchText = event?.target.value.toLowerCase() || "";
    setSearchTerm(searchText);
    const filtered = searchText
      ? productCollections?.filter((collection) =>
          collection.name.toLowerCase().includes(searchText)
        )
      : productCollections;
    setFilteredCollections(filtered || []);
  };

  const handleSelectAllChange = (checked?: boolean) => {
    const isChecked = !!checked;
    const updatedSelectedItems: Record<string, boolean> = { ...selectedItems };
    filteredCollections.forEach((collection) => {
      updatedSelectedItems[collection.id] = isChecked;
    });
    setSelectedItems(updatedSelectedItems);
  };

  const handleCollectionCheckboxChange = (
    collection: ProductCollectionResponse
  ) => {
    setSelectedItems((prevItems) => ({
      ...prevItems,
      [collection.id]: !prevItems[collection.id],
    }));
  };

  return (
    <>
      <Grid container spacing={2} data-testid={testId}>
        <Grid item xs={6} md={8}>
          <StyledTitle>{title}</StyledTitle>
          <StyledSubtitle>{subtitle}</StyledSubtitle>
        </Grid>
        <Grid item xs={6} md={4}>
          <SearchInput
            value={searchTerm}
            id="automated-product-collections-search-input"
            onChange={handleCollectionSearch}
            testId="automated-product-collections-search-input"
          />
        </Grid>
      </Grid>
      <Controller
        name={formName}
        control={control}
        rules={{
          validate: () => {
            if (isRefetching) {
              return orgDiscountCollections.current.length > 0;
            }
            const hasSelectedItems = Object.values(selectedItems).some(
              (value) => value === true
            );
            return hasSelectedItems || errorMessage;
          },
        }}
        render={() => (
          <StyledCollectionsWrapper>
            <StyledProductCollectionsToolbar data-testid="automated-product-collections-select-all-wrapper">
              <StyledSelectAllCheckboxWrapper>
                <Checkbox
                  value=""
                  label={`Select all Collections (${
                    filteredCollections.length || 0
                  })`}
                  checked={filteredCollections.every(
                    (collection) => selectedItems[collection.id]
                  )}
                  onChange={handleSelectAllChange}
                  testId="automated-product-collections-select-all-checkbox"
                />
                <Tooltip
                  title="Refresh Product Collections list"
                  variant="context"
                >
                  <IconButton
                    iconName="Sync"
                    size="small"
                    onClick={() => refetch()}
                  />
                </Tooltip>
              </StyledSelectAllCheckboxWrapper>
              <Tooltip
                title="A new tab on the Collections page will open. After creating the collection, return to this discount and refresh the list or search by the collection name to add it."
                variant="multiRow"
                cursor="default"
              >
                <Link
                  underline="always"
                  colorVariant="primary"
                  variant="largeTextStrong"
                  href={`${PAGE_URL}/product-collection`}
                  newTab
                  ariaLabel="Add New Product Collection"
                >
                  + Add New Product Collection
                </Link>
              </Tooltip>
            </StyledProductCollectionsToolbar>
            <StyledProductCollectionsList>
              {isRefetching ? (
                <ProductCollectionItemSkeleton
                  length={filteredCollections.length}
                />
              ) : (
                <>
                  {filteredCollections.map((collection) => {
                    const { id, name } = collection;

                    return (
                      <StyledCollectionItem key={`product-collection-${id}`}>
                        <Panel testId={`product-collection-${id}`}>
                          <StyledVerticalCenteringBox>
                            <Checkbox
                              key={id}
                              value={id}
                              label={name}
                              checked={!!selectedItems[id]}
                              onChange={() =>
                                handleCollectionCheckboxChange(collection)
                              }
                              testId="automated-product-collection-checkbox"
                            />
                            <Link
                              underline="always"
                              colorVariant="primary"
                              variant="largeTextStrong"
                              testId={`link-to-${collection.id}`}
                              href={
                                collection.sync
                                  ? `${PAGE_URL}/product-collection/automated/${collection.id}`
                                  : `${PAGE_URL}/product-collection/${collection.id}`
                              }
                              newTab
                              ariaLabel="Add New Product Collection"
                            >
                              <IconLink>
                                <Icon iconName="EditSquare" color="green09" />
                              </IconLink>
                            </Link>
                          </StyledVerticalCenteringBox>
                        </Panel>
                      </StyledCollectionItem>
                    );
                  })}
                </>
              )}
            </StyledProductCollectionsList>
          </StyledCollectionsWrapper>
        )}
      />
    </>
  );
};

export default ProductCollectionsSelectionCard;
