import React, { ReactElement } from "react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { MemoryRouter } from "react-router-dom";
import { render } from "@testing-library/react";
import { ModalProvider, SnackbarProvider } from "../providers";

const ProvidersWrapper = ({
  children,
  route,
}: {
  children: React.ReactNode;
  route: string;
}) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <TreezThemeProvider>
      <QueryClientProvider client={queryClient}>
        <ModalProvider>
          <SnackbarProvider>
            <MemoryRouter initialEntries={[route]}>{children}</MemoryRouter>
          </SnackbarProvider>
        </ModalProvider>
      </QueryClientProvider>
    </TreezThemeProvider>
  );
};

const renderWithProviders = (ui: ReactElement<any>, options?: any) =>
  render(ui, {
    wrapper: (
      props: JSX.IntrinsicAttributes & {
        children: React.ReactNode;
        route: string;
      }
    ) => <ProvidersWrapper {...props} route={options.route} />,
    ...options,
  });

export * from "@testing-library/react";
export default renderWithProviders;
