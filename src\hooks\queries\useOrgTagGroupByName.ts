import { useQuery } from "@tanstack/react-query";
import { OrgTags } from "../../interfaces/responseModels";
import { getOrgTagGroupUrl } from "../../services/apiEndPoints";
import { api } from "../../services/api";

const useOrgTagGroupByName = (name: string) =>
  useQuery({
    queryKey: ["tags", name],
    queryFn: async () => {
      const result = await api.get(getOrgTagGroupUrl, { params: { name } });
      return result.data.tags as OrgTags[];
    },
  });

export default useOrgTagGroupByName;
