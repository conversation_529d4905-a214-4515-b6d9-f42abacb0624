import React from "react";
import { Input, convertPxToRem } from "@treez-inc/component-library";
import { Control, Controller, FieldValues } from "react-hook-form";
import { Box, styled } from "@mui/material";
import { DiscountConditionFormData } from "../../../../../../interfaces/discounts";

interface MaxCountInputProps {
  label: string;
  name: string;
  control: Control<FieldValues, DiscountConditionFormData>;
  testId?: string;
}

const StyledInputWrapper = styled(Box)({
  maxWidth: convertPxToRem(300),
});

const MaxCountInput = ({
  label,
  name,
  control,
  testId,
}: MaxCountInputProps) => (
  <Controller
    name={name}
    control={control}
    rules={{
      required: { value: true, message: "Max count is required" },
      min: { value: 1, message: "Please enter number greater than 0" },
      pattern: { value: /^[0-9]*$/, message: "Count cannot be in decimals" },
    }}
    render={({ field, fieldState: { error } }) => {
      const { ref, value, ...rest } = field;
      return (
        <StyledInputWrapper data-testid={testId && `${testId}-max-count-input`}>
          <Input
            {...rest}
            value={value || ""}
            label={label}
            helperText={error?.message ? error.message : ""}
            error={!!error}
            required
            type="number"
          />
        </StyledInputWrapper>
      );
    }}
  />
);

export default MaxCountInput;
