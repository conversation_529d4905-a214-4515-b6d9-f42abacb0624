const copyDiscountId =
  (idToCopy: string, openSnackbar: any) =>
  async (event: React.MouseEvent<HTMLLIElement>) => {
    event.preventDefault();

    try {
      await navigator.clipboard.writeText(idToCopy);
      openSnackbar({
        message: "Discount ID was copied to clipboard",
      });
    } catch (err) {
      openSnackbar({
        message: "Unable to copy Discount ID at the moment",
        severity: "error",
      });
    }
  };

export default copyDiscountId;
