import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { ObjectType } from "@treez-inc/file-management";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { v4 as uuidv4 } from "uuid";
import ApiService from "../../services/api/apiService";
import Images, { ImagesProps } from ".";

const testData = {
  tokens: {
    accessToken: "accessToken",
    expiresIn: 300,
    refreshToken: "refreshToken",
    idToken: "idToken",
  },
};
const clearTokens = () => {};
const apiService = new ApiService(() => testData.tokens, clearTokens);

describe("Images", () => {
  const renderImages = (props: Partial<ImagesProps>) => {
    const objectId = uuidv4();
    const onNewImageCreatedMock = jest.fn();
    const onDeleteImageMock = jest.fn();

    render(
      <TreezThemeProvider>
        <Images
          imageUrl={props.imageUrl || null}
          api={apiService}
          objectId={objectId}
          objectType={ObjectType.DISCOUNT_IMAGE}
          onNewImageCreated={onNewImageCreatedMock}
          onDeleteImage={onDeleteImageMock}
          {...props}
        />
      </TreezThemeProvider>
    );

    const { getByTestId } = screen;

    const imagePreview = () => getByTestId("image-preview-wrapper");
    const imageFileUpload = getByTestId("image-file-upload-component");
    const imageDetailsModal = () => getByTestId("image-details-modal");
    const imageViewer = () => getByTestId("image-viewer");
    const imageDeleteButton = () => getByTestId("image-preview-delete-button");

    return {
      imagePreview,
      imageFileUpload,
      imageDetailsModal,
      imageViewer,
      imageDeleteButton,
      onDeleteImageMock,
    };
  };

  it("should render image preview when imageUrl is provided", () => {
    const { imagePreview } = renderImages({
      imageUrl: "http://localhost/image",
    });
    expect(imagePreview()).toBeDefined();
  });

  it("should show update image message if imageUrl is provided", () => {
    const { imageFileUpload } = renderImages({
      imageUrl: "http://localhost/image",
    });

    expect(imageFileUpload).toHaveTextContent("Update image");
  });

  it("should show image details modal when image viewer is clicked", async () => {
    const { imageViewer, imageDetailsModal } = renderImages({
      imageUrl: "http://localhost/image",
    });

    imageViewer().click();

    await waitFor(() => {
      expect(imageDetailsModal()).toBeDefined();
    });
  });

  it("should call onDeleteImage when delete button is clicked", async () => {
    const {
      imageViewer,
      imageDetailsModal,
      imageDeleteButton,
      onDeleteImageMock,
    } = renderImages({
      imageUrl: "http://localhost/image",
    });

    imageViewer().click();

    await waitFor(() => {
      expect(imageDetailsModal()).toBeDefined();
    });

    imageDeleteButton().click();

    expect(onDeleteImageMock).toHaveBeenCalled();
  });
});
