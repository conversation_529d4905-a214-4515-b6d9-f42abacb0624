import React, { useEffect, useState } from "react";
import {
  Navigate,
  create<PERSON><PERSON>er<PERSON><PERSON>er,
  RouterProvider,
} from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { Box, styled } from "@mui/material";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { ContentContainer } from "@treez/nav-sidebar";
import routes from "./constants/routes";
import ApiService from "./services/api/apiService";
import { initializeApi } from "./services/api";
import { FrameworkProps } from "./interfaces/tokens";
import AutomatedDiscountsView from "./views/AutomatedDiscountsView";
import AutomatedDiscountsForm from "./views/AutomatedDiscountsView/AutomatedDiscountsForm";
import ManualDiscountsView from "./views/ManualDiscountsView";
import ManualDiscountForm from "./views/ManualDiscountsView/ManualDiscountForm";
import ErrorView from "./views/ErrorView";
import { ModalProvider, SnackbarProvider } from "./providers";
import { useGetFeatureFlag } from "./hooks";
import { orgFeatureFlagsUrl } from "./services/apiEndPoints";
import { parseJwt } from "./utils";
import { Permissions } from "./constants";
import DrawerProvider from "./providers/DrawerProvider";

const DiscountManagementWrapper = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.primaryWhite.main,
  height: "100%",
  overflowY: "auto",
}));

const queryClient = new QueryClient();

const Root = ({
  getTokens,
  clearTokens,
  redirectToLogin,
  getPermissions,
}: FrameworkProps) => {
  if (getTokens() === null) {
    redirectToLogin();
  }
  // TODO: Migrate ApiService class to importable instance of axios
  initializeApi(getTokens, clearTokens);
  const apiService = new ApiService(getTokens, clearTokens);
  const decodedToken = parseJwt(apiService.getTokens().accessToken);
  const [automatedDiscountsFeatureFlag] = useGetFeatureFlag(
    apiService,
    orgFeatureFlagsUrl({
      orgId: decodedToken?.orgId,
      featureFlag: "Automated Discounts",
    })
  );
  const [permissions, setPermissions] = useState<{
    write: boolean;
    read: boolean;
  }>({ write: false, read: false });

  useEffect(() => {
    const getPermissionsAsync = async () => {
      const result = await getPermissions();
      const updatedDiscountPermissions = { write: false, read: false };
      result?.permissions.forEach((permission) => {
        if (permission === Permissions.WRITE_DISCOUNTS) {
          updatedDiscountPermissions.write = true;
        }
        if (permission === Permissions.READ_DISCOUNTS) {
          updatedDiscountPermissions.read = true;
        }
      });
      setPermissions(updatedDiscountPermissions);
    };
    getPermissionsAsync();
  }, []);

  if (!automatedDiscountsFeatureFlag?.data) {
    return null;
  }

  const router = createBrowserRouter(
    [
      {
        path: "/",
        element: (
          <Navigate
            to={
              automatedDiscountsFeatureFlag?.data?.status
                ? routes.automatedDiscountsPath
                : routes.manualDiscountsPath
            }
            replace
          />
        ),
        errorElement: <ErrorView />,
      },
      {
        path: routes.automatedDiscountsPath,
        element: (
          <AutomatedDiscountsView api={apiService} permissions={permissions} />
        ),
        children: [
          {
            path: "add",
            element: (
              <AutomatedDiscountsForm
                api={apiService}
                permissions={permissions}
                userPermissions={result?.permissions}
              />
            ),
          },
          {
            path: "edit/:orgDiscountId",
            element: (
              <AutomatedDiscountsForm
                api={apiService}
                permissions={permissions}
                userPermissions={result?.permissions}
                isEditMode
              />
            ),
          },
        ],
      },
      {
        path: routes.manualDiscountsPath,
        element: (
          <ManualDiscountsView api={apiService} permissions={permissions} />
        ),
        children: [
          {
            path: "add",
            element: (
              <ManualDiscountForm api={apiService} permissions={permissions} />
            ),
          },
          {
            path: "edit/:orgDiscountId",
            element: (
              <ManualDiscountForm
                api={apiService}
                permissions={permissions}
                isEditMode
              />
            ),
          },
          {
            path: "edit/:orgDiscountId/stores/:storeCustomizationId",
            element: (
              <ManualDiscountForm
                api={apiService}
                permissions={permissions}
                isEditMode
              />
            ),
          },
        ],
      },
    ],
    { basename: routes.basePath }
  );

  return (
    <TreezThemeProvider>
      <ContentContainer>
        <DiscountManagementWrapper data-testid="discount-management-wrapper">
          <QueryClientProvider client={queryClient}>
            <ReactQueryDevtools />
            <ModalProvider>
              <SnackbarProvider>
                <DrawerProvider>
                  <RouterProvider router={router} />
                </DrawerProvider>
              </SnackbarProvider>
            </ModalProvider>
          </QueryClientProvider>
        </DiscountManagementWrapper>
      </ContentContainer>
    </TreezThemeProvider>
  );
};

export default Root;
