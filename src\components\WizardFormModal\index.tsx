import React, { ReactNode } from "react";
import { Dialog, Box, styled } from "@mui/material";
import { TopInfoBar } from "@treez-inc/component-library";
import LeftPanel from "./LeftPanel";

interface WizardFormModalProps {
  handleClose: () => void;
  isOpen: boolean;
  children: ReactNode;
  bannerTitle?: string;
  testId?: string;
}
interface ChildrenContainerProps {
  isBannerPresent: boolean;
}

const PageLayout = styled(Box)`
  display: grid;
  grid-template-columns: 17.5rem 1fr;
`;

const StyledDialog = styled(Dialog)(() => ({
  "& .MuiDialog-paper": {
    overflow: "hidden",
  },
}));

const ChildrenContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== "isBannerPresent",
})<ChildrenContainerProps>(({ isBannerPresent }) => ({
  display: "flex",
  flexDirection: "column",
  justifyContent: "flex-start",
  height: isBannerPresent ? "calc(100vh - 48px)" : "100vh",
}));

const StickyContainer = styled(Box)({
  position: "relative",
  top: 0,
});

const WizardFormModal: React.FC<WizardFormModalProps> = ({
  handleClose,
  isOpen,
  children,
  bannerTitle,
  testId = "",
}) => (
  <StyledDialog fullScreen open={isOpen} onClose={handleClose}>
    {bannerTitle && (
      <StickyContainer>
        <TopInfoBar
          message={bannerTitle}
          testId="wizard-form-modal-top-info-bar"
        />
      </StickyContainer>
    )}
    <PageLayout data-testid={testId}>
      <LeftPanel onClose={handleClose} />
      <ChildrenContainer isBannerPresent={!!bannerTitle}>
        {children}
      </ChildrenContainer>
    </PageLayout>
  </StyledDialog>
);

WizardFormModal.defaultProps = {
  bannerTitle: "",
  testId: "",
};

export default WizardFormModal;
