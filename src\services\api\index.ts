import axios, { AxiosError, AxiosInstance, AxiosRequestConfig } from "axios";
import retry from "./retry";
import { ClearTokens, Tokens } from "../../interfaces/tokens";

export interface ApiService extends AxiosInstance {
  getTokens: () => Tokens;
  clearTokens: ClearTokens;
}

export const api = axios.create({}) as ApiService;

export const initializeApi = (
  getTokens: () => Tokens,
  clearTokens: ClearTokens
): ApiService => {
  // Retrieves current token stored in localStorage and sets Authorization header
  api.interceptors.request.use((config: AxiosRequestConfig) => {
    const newConfig = { ...config };
    newConfig.headers = newConfig.headers ?? {};
    newConfig.headers.Authorization = `Bearer ${getTokens().accessToken}`;
    return newConfig;
  });

  api.interceptors.response.use(undefined, async (err: AxiosError) => {
    const { config, message } = err;
    // incase of bad token
    if (err?.response?.status === 401) {
      clearTokens();
      throw new Error("An error has occurred. Please try again");
    }

    // commenting since status code and response object not reaching front end.
    // if (err?.response?.status === 403) {
    //   throw new Error("You don't have permissions to make this request.");
    // }

    // retry while Network timeout or Network Error
    if (!(message.includes("timeout") || message.includes("Network Error"))) {
      // eslint-disable-next-line no-console
      console.error(err);
      return Promise.reject(err);
    }
    if (config && config.headers && config.headers.retry) {
      const retryHeader = config.headers.retry as string;
      let retryHeaderInt = parseInt(retryHeader, 10);
      if (retryHeaderInt > 3) return Promise.reject(err);
      retryHeaderInt += 1;
      config.headers.retry = retryHeaderInt.toString();
      // eslint-disable-next-line no-console
      console.error(err);
      return retry.retryRequest(config);
    }
    // eslint-disable-next-line no-console
    console.error(err);
    return Promise.reject(err);
  });

  api.getTokens = getTokens;
  api.clearTokens = clearTokens;

  return api;
};
