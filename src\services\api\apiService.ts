import { AxiosRequestConfig, AxiosResponse } from "axios";
import apiInterceptor from "./apiInterceptor";
import { ClearTokens, Tokens } from "../../interfaces/tokens";

export default class ApiService {
  getTokens;

  clearTokens;

  api;

  constructor(getTokens: () => Tokens, clearTokens: ClearTokens) {
    this.getTokens = getTokens;
    this.clearTokens = clearTokens;
    this.api = apiInterceptor(this.clearTokens);
  }

  addHeader(): AxiosRequestConfig {
    return {
      headers: {
        Authorization: `Bearer ${this.getTokens().accessToken}`,
      },
    };
  }

  // eslint-disable-next-line class-methods-use-this
  async get(url: string): Promise<AxiosResponse<any, any>> {
    return this.api.get(url, this.addHeader());
  }

  // eslint-disable-next-line class-methods-use-this
  async post(url: string, payload: any): Promise<AxiosResponse<any, any>> {
    return this.api.post(url, payload, this.addHeader());
  }

  async put(url: string, payload: any): Promise<AxiosResponse<any, any>> {
    return this.api.put(url, payload, this.addHeader());
  }

  async delete(url: string, payload?: any): Promise<AxiosResponse<any, any>> {
    return this.api.delete(url, {
      ...(payload && { data: payload }),
      ...this.addHeader(),
    });
  }
}
