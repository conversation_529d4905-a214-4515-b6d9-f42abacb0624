import React from "react";
import { MemoryRouter } from "react-router-dom";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import AutomatedDiscountsView from ".";
import ApiService from "../../services/api/apiService";
import { usePageLoading, useDiscounts } from "../../hooks";
import { testAutomatedDiscountsResponse } from "../../test/fixtures";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const testData = {
  tokens: {
    accessToken: "accessToken",
    expiresIn: 300,
    refreshToken: "refreshToken",
    idToken: "idToken",
  },
};
const clearTokens = () => {};
const apiService = new ApiService(() => testData.tokens, clearTokens);
const mockNavigate = jest.fn();
const openDiscountModalMock = jest.fn();
const openDiscountLogMock = jest.fn();

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => mockNavigate,
}));

jest.mock("../../hooks/usePageLoading");
jest.mock("../../hooks/useDiscounts");

describe("AutomatedDiscountsView", () => {
  beforeEach(() => {
    mockNavigate.mockReset();
  });

  const renderAutomatedDiscountsView = (
    isLoading = false,
    writePermission = true
  ) => {
    (usePageLoading as jest.Mock).mockReturnValue({ isPageLoading: isLoading });
    (useDiscounts as jest.Mock).mockReturnValue({
      discounts: testAutomatedDiscountsResponse,
      openDiscountModal: openDiscountModalMock,
      openDiscountLog: openDiscountLogMock,
      closeModal: jest.fn(),
      modalState: { open: false },
      drawerState: { open: false },
      setDiscounts: jest.fn(),
      isPageLoading: isLoading,
      setIsPageLoading: jest.fn(),
      handleDeleteDiscount: jest.fn(),
      handleActivateDiscount: jest.fn(),
      handleDeactivateDiscount: jest.fn(),
      handleUnassignStore: jest.fn(),
    });
    render(
      <MemoryRouter initialEntries={["/automated"]}>
        <TreezThemeProvider>
          <QueryClientProvider client={queryClient}>
            <AutomatedDiscountsView
              api={apiService}
              permissions={{ read: true, write: writePermission }}
            />
          </QueryClientProvider>
        </TreezThemeProvider>
      </MemoryRouter>
    );

    const { getByTestId, queryByTestId, queryAllByTestId } = screen;

    const loadingSpinner = () => queryByTestId("loading-spinner");
    const pageHeader = getByTestId("automated-discounts-page-header");
    const pageHeaderButton = getByTestId("add-discount-button");
    const discountsTable = getByTestId("automated-discounts-list-container");
    const actionMenus = queryAllByTestId("discount-action-menu");
    const actionModal = () => queryByTestId("automated-discounts-action-modal");
    const activeStoreMenu = actionMenus[0];
    const inactiveStoreMenu = actionMenus[1];

    return {
      loadingSpinner,
      pageHeader,
      pageHeaderButton,
      discountsTable,
      actionMenus,
      actionModal,
      activeStoreMenu,
      inactiveStoreMenu,
    };
  };

  describe("page header", () => {
    it("should render the page header", () => {
      const { pageHeader } = renderAutomatedDiscountsView();
      expect(pageHeader).toBeInTheDocument();
    });

    it("should navigate to add discount page on button click", async () => {
      const { pageHeaderButton } = renderAutomatedDiscountsView();
      fireEvent.click(pageHeaderButton);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith("add");
      });
    });

    it("should not navigate to add discount page on button click when user lacks write permissions", async () => {
      const { pageHeaderButton } = renderAutomatedDiscountsView(true, false);
      fireEvent.click(pageHeaderButton);

      await waitFor(() => {
        expect(mockNavigate).not.toHaveBeenCalled();
      });
    });
  });

  it("should render the automated discounts table ", () => {
    const { discountsTable } = renderAutomatedDiscountsView();
    expect(discountsTable).toBeInTheDocument();
  });
});
