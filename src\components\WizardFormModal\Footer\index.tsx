import React from "react";
import { styled, Box } from "@mui/material";
import {
  convertPxToRem,
  Button,
  ButtonProps,
  Tooltip,
} from "@treez-inc/component-library";
import { PERMISSIONS_MESSAGES } from "../../../constants";
// TODO: Footer component should be added/moved to the component library
export interface FooterProps {
  activeStep: number;
  primaryButtonProps: Omit<ButtonProps, "iconName" | "small" | "variant">;
  secondaryButtonProps?: Omit<ButtonProps, "small" | "variant">;
  tertiaryButtonProps?: Omit<ButtonProps, "small" | "variant">;
  testId?: string;
  totalSteps?: number;
}

const FooterWrapper = styled(Box)(({ theme }) => ({
  flex: `0 0 ${convertPxToRem(84)}`,
  borderTop: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
}));

const ButtonsWrapper = styled(Box, {
  shouldForwardProp: (prop) => prop !== "showSecondaryButton",
})<{ showSecondaryButton: boolean }>(({ showSecondaryButton }) => ({
  maxWidth: convertPxToRem(882),
  height: "100%",
  display: "flex",
  alignItems: "center",
  justifyContent: showSecondaryButton ? "space-between" : "end",
}));

const RightButtonsWrapper = styled(Box)({
  display: "flex",
  gap: `${convertPxToRem(16)}`,
});

const Footer: React.FC<FooterProps> = ({
  activeStep,
  primaryButtonProps,
  secondaryButtonProps,
  tertiaryButtonProps,
  testId,
  totalSteps,
}: FooterProps) => {
  const showSecondaryButton = !!secondaryButtonProps && activeStep !== 0;
  const showTertiaryButton =
    !!tertiaryButtonProps && activeStep < totalSteps! - 1;

  return (
    <FooterWrapper data-testid={testId}>
      <ButtonsWrapper showSecondaryButton={showSecondaryButton}>
        {showSecondaryButton && secondaryButtonProps && (
          <Button variant="secondary" {...secondaryButtonProps} />
        )}
        <RightButtonsWrapper>
          {showTertiaryButton &&
            tertiaryButtonProps &&
            (tertiaryButtonProps.disabled ? (
              <Tooltip title={PERMISSIONS_MESSAGES.NO_EDIT} variant="multiRow">
                <Button variant="text" {...tertiaryButtonProps} />
              </Tooltip>
            ) : (
              <Button variant="text" {...tertiaryButtonProps} />
            ))}
          {primaryButtonProps.disabled ? (
            <Tooltip title={PERMISSIONS_MESSAGES.NO_EDIT} variant="multiRow">
              <Button variant="primary" {...primaryButtonProps} />
            </Tooltip>
          ) : (
            <Button variant="primary" {...primaryButtonProps} />
          )}
        </RightButtonsWrapper>
      </ButtonsWrapper>
    </FooterWrapper>
  );
};

export default Footer;
