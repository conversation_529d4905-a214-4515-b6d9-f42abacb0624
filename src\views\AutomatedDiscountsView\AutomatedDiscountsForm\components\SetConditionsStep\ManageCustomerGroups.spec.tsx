import React from "react";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import SetConditionsStep from ".";
import { defaultAutomatedDiscountFormValues as defaultValues } from "../../../../../constants/discountForm";
import { OrgTags } from "../../../../../interfaces/responseModels";
import { CustomerGroup } from "../../../../../interfaces/discounts";

// Mock the PAGE_URL environment variable
const mockPageUrl = "https://test-domain.com";
Object.defineProperty(process.env, 'PAGE_URL', {
  value: mockPageUrl,
  writable: true
});

// Mock customer groups data
const mockCustomerGroups: Array<OrgTags & CustomerGroup> = [
  {
    id: "group-1",
    name: "VIP Customers",
    tagGroupId: "tag-group-1",
    organizationId: "org-1",
    createdAt: "2023-01-01",
    updatedAt: "2023-01-01"
  },
  {
    id: "group-2", 
    name: "Regular Customers",
    tagGroupId: "tag-group-1",
    organizationId: "org-1",
    createdAt: "2023-01-01",
    updatedAt: "2023-01-01"
  }
];

// Mock query client
const createMockQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });
};

// Mock fetch for API calls
const mockFetch = jest.fn();
global.fetch = mockFetch;

describe("SetConditionsStep - Manage Customer Groups Feature", () => {
  let queryClient: QueryClient;

  const renderSetConditionsStep = (props: {
    customerGroups?: Array<OrgTags & CustomerGroup>;
    canManageTags?: boolean;
  } = {}) => {
    const FormProviderWrapper: React.FC<{ children: React.ReactNode }> = ({
      children,
    }) => {
      const methods = useForm({ defaultValues });
      return <FormProvider {...methods}>{children}</FormProvider>;
    };

    return render(
      <TreezThemeProvider>
        <QueryClientProvider client={queryClient}>
          <FormProviderWrapper>
            <SetConditionsStep
              customerGroups={props.customerGroups || mockCustomerGroups}
              canManageTags={props.canManageTags}
            />
          </FormProviderWrapper>
        </QueryClientProvider>
      </TreezThemeProvider>
    );
  };

  beforeEach(() => {
    queryClient = createMockQueryClient();
    mockFetch.mockClear();
    
    // Mock successful API response
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve([
        ...mockCustomerGroups,
        {
          id: "group-3",
          name: "New Customer Group",
          tagGroupId: "tag-group-1",
          organizationId: "org-1",
          createdAt: "2023-01-01",
          updatedAt: "2023-01-01"
        }
      ])
    });
  });

  describe("Permission-based rendering", () => {
    it("should render manage customer groups section when canManageTags is true", () => {
      renderSetConditionsStep({ canManageTags: true });

      // Should show refresh button
      expect(screen.getByLabelText("Refresh Customer Groups")).toBeInTheDocument();
      
      // Should show manage customer groups link
      expect(screen.getByText("Manage Customer Groups")).toBeInTheDocument();
    });

    it("should not render manage customer groups section when canManageTags is false", () => {
      renderSetConditionsStep({ canManageTags: false });

      // Should not show refresh button
      expect(screen.queryByLabelText("Refresh Customer Groups")).not.toBeInTheDocument();
      
      // Should not show manage customer groups link
      expect(screen.queryByText("Manage Customer Groups")).not.toBeInTheDocument();
    });

    it("should not render manage customer groups section when canManageTags is undefined", () => {
      renderSetConditionsStep({ canManageTags: undefined });

      // Should not show refresh button
      expect(screen.queryByLabelText("Refresh Customer Groups")).not.toBeInTheDocument();
      
      // Should not show manage customer groups link
      expect(screen.queryByText("Manage Customer Groups")).not.toBeInTheDocument();
    });
  });

  describe("Customer Groups Display", () => {
    it("should display customer groups in the autocomplete", () => {
      renderSetConditionsStep({ canManageTags: true });

      const autocomplete = screen.getByLabelText("Customer Group");
      expect(autocomplete).toBeInTheDocument();
    });

    it("should handle empty customer groups array", () => {
      renderSetConditionsStep({ 
        customerGroups: [],
        canManageTags: true 
      });

      const autocomplete = screen.getByLabelText("Customer Group");
      expect(autocomplete).toBeInTheDocument();
    });
  });

  describe("Refresh Customer Groups Functionality", () => {
    it("should call refresh API when refresh button is clicked", async () => {
      // Mock queryClient methods
      const invalidateQueriesSpy = jest.spyOn(queryClient, 'invalidateQueries');
      const fetchQuerySpy = jest.spyOn(queryClient, 'fetchQuery').mockResolvedValue([
        ...mockCustomerGroups,
        { id: "group-3", name: "New Group", tagGroupId: "tag-group-1" }
      ]);

      renderSetConditionsStep({ canManageTags: true });

      const refreshButton = screen.getByLabelText("Refresh Customer Groups");
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(invalidateQueriesSpy).toHaveBeenCalledWith({ 
          queryKey: ["tags", "Customer Group"] 
        });
        expect(fetchQuerySpy).toHaveBeenCalledWith({ 
          queryKey: ["tags", "Customer Group"] 
        });
      });
    });

    it("should update customer groups list after refresh", async () => {
      const fetchQuerySpy = jest.spyOn(queryClient, 'fetchQuery').mockResolvedValue([
        ...mockCustomerGroups,
        { id: "group-3", name: "Refreshed Group", tagGroupId: "tag-group-1" }
      ]);

      renderSetConditionsStep({ canManageTags: true });

      const refreshButton = screen.getByLabelText("Refresh Customer Groups");
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(fetchQuerySpy).toHaveBeenCalled();
      });
    });
  });

  describe("Manage Customer Groups Link", () => {
    it("should render link with correct href when tagGroupId is available", () => {
      renderSetConditionsStep({ canManageTags: true });

      const manageLink = screen.getByText("Manage Customer Groups");
      expect(manageLink).toHaveAttribute(
        "href", 
        `${mockPageUrl}/settings/tags/group/tag-group-1`
      );
    });

    it("should open link in new tab", () => {
      renderSetConditionsStep({ canManageTags: true });

      const manageLink = screen.getByText("Manage Customer Groups");
      expect(manageLink).toHaveAttribute("target", "_blank");
    });

    it("should have proper accessibility attributes", () => {
      renderSetConditionsStep({ canManageTags: true });

      const manageLink = screen.getByText("Manage Customer Groups");
      expect(manageLink).toHaveAttribute("aria-label", "Manage Customer Groups");
    });
  });

  describe("Tooltips", () => {
    it("should show refresh tooltip on hover", async () => {
      renderSetConditionsStep({ canManageTags: true });

      const refreshButton = screen.getByLabelText("Refresh Customer Groups");
      fireEvent.mouseEnter(refreshButton);

      await waitFor(() => {
        expect(screen.getByText("Refresh customer groups list")).toBeInTheDocument();
      });
    });

    it("should show manage customer groups tooltip on hover", async () => {
      renderSetConditionsStep({ canManageTags: true });

      const manageLink = screen.getByText("Manage Customer Groups");
      fireEvent.mouseEnter(manageLink);

      await waitFor(() => {
        expect(screen.getByText(/A new tab on the Tag Management page will open/)).toBeInTheDocument();
      });
    });
  });

  describe("Error Handling", () => {
    it("should handle refresh API errors gracefully", async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const fetchQuerySpy = jest.spyOn(queryClient, 'fetchQuery').mockRejectedValue(
        new Error("API Error")
      );

      renderSetConditionsStep({ canManageTags: true });

      const refreshButton = screen.getByLabelText("Refresh Customer Groups");
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(fetchQuerySpy).toHaveBeenCalled();
      });

      consoleSpy.mockRestore();
    });

    it("should handle missing tagGroupId gracefully", () => {
      const customerGroupsWithoutTagGroupId = [
        {
          id: "group-1",
          name: "VIP Customers",
          organizationId: "org-1",
          createdAt: "2023-01-01",
          updatedAt: "2023-01-01"
        }
      ] as Array<OrgTags & CustomerGroup>;

      renderSetConditionsStep({
        customerGroups: customerGroupsWithoutTagGroupId,
        canManageTags: true
      });

      const manageLink = screen.getByText("Manage Customer Groups");
      expect(manageLink).toHaveAttribute("href", `${mockPageUrl}/settings/tags/group/undefined`);
    });
  });

  describe("Component Re-rendering", () => {
    it("should re-render autocomplete when customer groups change", () => {
      const { rerender } = renderSetConditionsStep({
        customerGroups: mockCustomerGroups,
        canManageTags: true
      });

      const newCustomerGroups = [
        ...mockCustomerGroups,
        {
          id: "group-3",
          name: "Premium Customers",
          tagGroupId: "tag-group-1",
          organizationId: "org-1",
          createdAt: "2023-01-01",
          updatedAt: "2023-01-01"
        }
      ];

      const FormProviderWrapper: React.FC<{ children: React.ReactNode }> = ({
        children,
      }) => {
        const methods = useForm({ defaultValues });
        return <FormProvider {...methods}>{children}</FormProvider>;
      };

      rerender(
        <TreezThemeProvider>
          <QueryClientProvider client={queryClient}>
            <FormProviderWrapper>
              <SetConditionsStep
                customerGroups={newCustomerGroups}
                canManageTags={true}
              />
            </FormProviderWrapper>
          </QueryClientProvider>
        </TreezThemeProvider>
      );

      // Component should re-render with new key based on refreshKey state
      const autocomplete = screen.getByLabelText("Customer Group");
      expect(autocomplete).toBeInTheDocument();
    });

    it("should update refresh key when handleRefreshCustomerGroups is called", async () => {
      const fetchQuerySpy = jest.spyOn(queryClient, 'fetchQuery').mockResolvedValue([
        ...mockCustomerGroups,
        { id: "group-3", name: "New Group", tagGroupId: "tag-group-1" }
      ]);

      renderSetConditionsStep({ canManageTags: true });

      const refreshButton = screen.getByLabelText("Refresh Customer Groups");

      // Click refresh multiple times to test key increment
      fireEvent.click(refreshButton);
      await waitFor(() => expect(fetchQuerySpy).toHaveBeenCalledTimes(1));

      fireEvent.click(refreshButton);
      await waitFor(() => expect(fetchQuerySpy).toHaveBeenCalledTimes(2));
    });
  });

  describe("Integration with Form", () => {
    it("should integrate properly with react-hook-form", () => {
      renderSetConditionsStep({ canManageTags: true });

      const autocomplete = screen.getByLabelText("Customer Group");
      expect(autocomplete).toBeInTheDocument();

      // Should be part of the form
      expect(autocomplete.closest('form')).toBeTruthy();
    });
  });
});
