/** @type {import('ts-jest/dist/types').InitialOptionsTsJest} */
module.exports = {
  clearMocks: true,
  collectCoverage: true,
  collectCoverageFrom: [
    "src/components/**",
    "src/utils/**",
    "src/hooks/**",
    "src/root.component.tsx",
    "src/views/**",
  ],
  coverageDirectory: "coverage",
  coverageThreshold: {
    global: {
      // TODO: comment back in once tests are backfilled
      // branches: 95,
      // functions: 95,
      // lines: 95,
      // statements: 95,
    },
  },
  moduleNameMapper: {
    "@fontsource/roboto": "<rootDir>/src/root.component.tsx",
    "@treez/nav-sidebar": "<rootDir>/__mocks__/nav-sidebar.mock.tsx",
    "\\.(css|less)$": "identity-obj-proxy",
  },
  // so jest can recognize typescript
  preset: "ts-jest",
  setupFiles: ["<rootDir>/src/test/setupEnv.ts"],
  setupFilesAfterEnv: ["<rootDir>src/test/setupTests.js"],
  testEnvironment: "jest-environment-jsdom",
  testPathIgnorePatterns: ["/node_modules/"],
  transform: {
    "^.+\\.(js|jsx)$": "babel-jest",
  },
  transformIgnorePatterns: [
    "node_modules/?!(react-icons)",
    "node_modules/?!(@treez-inc)/",
  ],
};
