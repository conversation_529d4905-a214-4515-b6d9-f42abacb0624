/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react";
import { ErrorTemplate } from "@treez-inc/component-library";
import { styled } from "@mui/material/styles";
import { FrameworkProps } from "../../interfaces/tokens";

const StyledErrorContainer = styled("div")(() => ({
  display: "flex",
  height: "100%",
  justifyContent: "center",
  alignItems: "center",
  flexDirection: "column",
  width: "100%",
}));

const ErrorView = ({
  // err, info, and props are not currently used, but leaving them here so we know they are options for future use in the errorBoundary
  err,
  info,
  props,
}: {
  err?: Error;
  info?: React.ErrorInfo;
  props?: FrameworkProps;
}) => (
  <StyledErrorContainer>
    <ErrorTemplate
      title="Hmm… Something went wrong"
      body={
        <p>
          We’re having trouble loading this page. Refresh to try again. If the
          issue persists, please contact Customer Support.
        </p>
      }
      hideLogo
      buttonProps={{
        label: "Refresh",
        onClick: () => {
          window.location.reload();
        },
      }}
    />
  </StyledErrorContainer>
);

export default ErrorView;
