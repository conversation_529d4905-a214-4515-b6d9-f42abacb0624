import React from "react";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import { render, screen, waitFor } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { jest } from "@jest/globals";
import ManualDiscountTable from ".";
import { testManualDiscountsResponse } from "../../../../test/fixtures";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

describe("<ManualDiscountTable />", () => {
  const mockOpenDiscountModal = jest.fn();
  const mockOpenDiscountLog = jest.fn();

  const renderManualDiscountTable = () => {
    render(
      <Router>
        <TreezThemeProvider>
          <QueryClientProvider client={queryClient}>
            <ManualDiscountTable
              discounts={testManualDiscountsResponse}
              openDiscountModal={mockOpenDiscountModal}
              openDiscountLog={mockOpenDiscountLog}
            />
          </QueryClientProvider>
        </TreezThemeProvider>
      </Router>
    );

    const { getByTestId, queryAllByRole } = screen;

    const manualDiscountTable = getByTestId("manual-discount-list-container");
    const countDiscounts = getByTestId("count-discounts");
    const rows = queryAllByRole("row");
    const columnHeaders = queryAllByRole("columnheader");

    return {
      manualDiscountTable,
      countDiscounts,
      rows,
      columnHeaders,
    };
  };

  it("should render the manual discounts table", () => {
    const { manualDiscountTable } = renderManualDiscountTable();
    expect(manualDiscountTable).toBeInTheDocument();
  });

  it("should render the correct count of discounts in the header", () => {
    const { countDiscounts } = renderManualDiscountTable();

    expect(countDiscounts).toBeInTheDocument();
    expect(countDiscounts).toHaveTextContent(
      `${testManualDiscountsResponse.length} Discounts`
    );
  });

  it("should render the correct number of rows (including header row)", () => {
    const { rows } = renderManualDiscountTable();
    expect(rows).toHaveLength(3);
  });

  // Cannot test all headers - known issue with MUI DataGrid not rendering all column headers in test environment - 10/31/23
  it("should render the correct column headers", async () => {
    const { columnHeaders } = renderManualDiscountTable();
    await waitFor(() => {
      expect(columnHeaders).toHaveLength(3);
      expect(columnHeaders[1]).toHaveTextContent(/Name/);
      expect(columnHeaders[2]).toHaveTextContent(/Amount/);
    });
  });
});
