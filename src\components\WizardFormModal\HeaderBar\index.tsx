import React from "react";
import { Typography, Box, styled } from "@mui/material";
import { Icon, convertPxToRem } from "@treez-inc/component-library";
import { IconName } from "@treez-inc/component-library/dist/components/Icon/types";
// TODO: Move this to component library
export interface HeaderBarProps {
  header: string;
  subheader?: string;
  iconName?: IconName;
}

const HeaderWrapper = styled(Box)(() => ({
  flex: `0 0 ${convertPxToRem(96)}`,
  display: "flex",
  alignItems: "center",
}));

const StyledHeaderWrapper = styled(Box)(() => ({
  display: "flex",
  flexDirection: "column",
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  padding: convertPxToRem(16),
  backgroundColor: theme.palette.green03.main,
  borderRadius: convertPxToRem(8),
  marginRight: convertPxToRem(12),
}));

const HeaderBar: React.FC<HeaderBarProps> = (props) => {
  const { header, subheader, iconName } = props;
  return (
    <HeaderWrapper data-testid="header-bar">
      {iconName && (
        <IconWrapper>
          <Icon iconName={iconName} fontSize="large" testId="header-bar-icon" />
        </IconWrapper>
      )}
      <StyledHeaderWrapper>
        <Typography
          variant="h5"
          color="primaryBlackText"
          role="paragraph"
          data-testid="header-bar-header"
        >
          {header}
        </Typography>
        <Typography
          variant="mediumText"
          color="primaryBlackText"
          data-testid="header-bar-subheader"
        >
          {subheader}
        </Typography>
      </StyledHeaderWrapper>
    </HeaderWrapper>
  );
};

export default HeaderBar;
