import React, { useEffect, useState } from "react";
import {
  Control,
  Controller,
  UseFormGetValues,
  UseFormSetValue,
} from "react-hook-form";
// TODO: Replace Accordion with Treez Component Library Accordion
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Grid,
  Typography,
  styled,
} from "@mui/material";
import {
  convertPxToRem,
  Button,
  Checkbox,
  Icon,
  IconButton,
  Input,
  Tooltip,
  SearchInput,
} from "@treez-inc/component-library";
import { MANUAL_DISCOUNT_METHODS } from "../../../../../constants/discountForm";
import GlobalConditionItem from "../../../../../components/GlobalConditionItem";
import {
  ManualDiscountFormData,
  StoreCustomization,
} from "../../../../../interfaces/discounts";
import { EntityResponse, Store } from "../../../../../interfaces/entity";
import { getStores, validateAmountInput } from "../../../../../utils";

interface SelectStoreProps {
  control: Control<ManualDiscountFormData>;
  entityList: EntityResponse[];
  errors: any;
  getValues: UseFormGetValues<ManualDiscountFormData>;
  isEditMode: boolean;
  setValue: UseFormSetValue<ManualDiscountFormData>;
  storeCustomizationId?: string;
}

const STORE_CUSTOMIZATIONS_FIELD = "storeCustomizations";

const StyledGrid = styled(Grid)(({ theme }) => ({
  width: "100%",
  background: theme.palette.primaryWhite.main,
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  gap: convertPxToRem(24),
}));

const SelectStoresWrapper = styled(Box)(({ theme }) => ({
  border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
  borderRadius: convertPxToRem(16),
  padding: convertPxToRem(24),
}));

const DiscountInfoSection = styled(Box)(({ theme }) => ({
  width: "100%",
  height: "auto",
  display: "flex",
  justifyContent: "space-between",
  boxSizing: "border-box",
  background: theme.palette.primaryWhite.main,
}));

const StyledSection = styled(Box)(({ theme }) => ({
  width: "100%",
  height: "auto",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  boxSizing: "border-box",
  background: theme.palette.primaryWhite.main,
}));

const StyledInstructionsBox = styled(Box)({
  display: "flex",
  flexDirection: "column",
  marginBottom: convertPxToRem(12),
});

const SingleCustomStoreFieldBox = styled(Box)({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  "& .MuiIconButton-root": {
    paddingTop: 0,
    paddingBottom: 0,
  },
});

const StyledTitleTypography = styled(Typography)({
  marginBottom: convertPxToRem(16),
});

const StyledAccordionWrapper = styled(Grid)({
  width: "100%",
  height: "auto",
  marginTop: convertPxToRem(8),
});

const StyledAccordion = styled(Accordion)({
  border: "none",
  boxShadow: "none",
});

const StyledAccordionSummary = styled(AccordionSummary)({
  border: "none",
  boxShadow: "none",
  width: "100%",
  padding: 0,
});

const StyledAccordionSummaryRow = styled(Box)({
  display: "flex",
  alignItems: "center",
});

const SecuritySettingsGrid = styled(Grid)({
  paddingLeft: convertPxToRem(36),
});

const StyledAccordionDetails = styled(AccordionDetails)(() => ({
  padding: 0,
}));

const StyledStoreCustomizationBox = styled(Box)(({ theme }) => ({
  borderRadius: convertPxToRem(16),
  padding: convertPxToRem(20),
  backgroundColor: theme.palette.grey01.main,
  marginTop: convertPxToRem(12),
  marginBottom: convertPxToRem(12),
}));

const StyledCustomConditionsAppliedBadge = styled(Typography)(({ theme }) => ({
  backgroundColor: theme.palette.primaryBlack.main,
  padding: convertPxToRem(5),
  color: theme.palette.primaryWhiteText.main,
  marginLeft: convertPxToRem(16),
  borderRadius: convertPxToRem(8),
}));

const StyledStoreTypography = styled(Typography)({
  textTransform: "capitalize",
  marginLeft: convertPxToRem(8),
});

const StyledStoreBox = styled(Box)(({ theme }) => ({
  boxSizing: "border-box",
  boxShadow: "none",
  background: theme.palette.primaryWhite.main,
  border: `${convertPxToRem(2.5)} solid ${theme.palette.grey04.main}`,
  borderRadius: convertPxToRem(16),
  padding: `${convertPxToRem(24)}`,
  marginBottom: convertPxToRem(16),
}));

const DividerBox = styled(Box)(({ theme }) => ({
  borderTop: `${convertPxToRem(0.5)} solid`,
  width: "100%",
  margin: `${convertPxToRem(16)} 0 ${convertPxToRem(16)} ${convertPxToRem(20)}`,
  borderColor: theme.palette.grey05.main,
}));

const StyledStoreRowBox = styled(Box)({
  height: convertPxToRem(48),
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
});

const StyledErrorMsg = styled(Typography)(({ theme }) => ({
  fontSize: convertPxToRem(12),
  color: theme.palette.treezGrey[8],
  marginLeft: convertPxToRem(20),
  marginTop: convertPxToRem(12),
}));

const StyledGridContainer = styled(Grid)({
  height: "auto",
  display: "flex",
  justifyContent: "space-between",
  marginBottom: 18,
});

const StyledGlobalConditionsGrid = styled(Grid)(({ theme }) => ({
  width: "100%",
  height: "auto",
  boxSizing: "border-box",
  background: theme.palette.primaryWhite.main,
  border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
  borderRadius: convertPxToRem(16),
  padding: `${convertPxToRem(18)}`,
}));

const StyledGlobalConditionWithIconGrid = styled(Grid)({
  display: "flex",
  flexDirection: "row",
});

const StyledGlobalConditionGrid = styled(Grid)(({ theme }) => ({
  borderLeft: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
  padding: `0 ${convertPxToRem(16)}`,
}));

const IconBoxWrap = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  padding: convertPxToRem(16),
  backgroundColor: theme.palette.green02.main,
  borderRadius: convertPxToRem(8),
  marginRight: convertPxToRem(12),
}));

const StyledStoresFoundLabelContainer = styled(Box)(({ theme }) => ({
  fontFamily: theme.typography.fontFamily,
}));

const SelectStoreStep: React.FC<SelectStoreProps> = ({
  isEditMode,
  entityList,
  getValues,
  control,
  setValue,
  errors,
  storeCustomizationId = undefined,
}) => {
  const { isItem, requirePin, requireReason, method, amount } = getValues();
  const [active, setActive] = useState(false);
  const [clue, setClue] = useState("");
  const [filteredEntities, setFilteredEntities] = useState<Store[]>([]);

  const getStoreCustomizationObj = (
    storeCustomizations: StoreCustomization[],
    entityId: string
  ) => storeCustomizations?.find((sc) => sc.entityId === entityId);

  const isCustomConditionApplied = (
    storeCustomizations: StoreCustomization[],
    entityId: string
  ) => {
    const storeCustomization = storeCustomizations?.find(
      (sc) => sc.entityId === entityId
    );

    return (
      storeCustomization?.amount !== null ||
      storeCustomization?.requirePin !== null ||
      storeCustomization?.requireReason !== null
    );
  };

  const getStoreCustomizationIndex = (
    storeCustomizations: StoreCustomization[],
    entityId: string
  ) => storeCustomizations?.findIndex((sc) => sc.entityId === entityId);

  const handleSwitch = (storeCustomizations: StoreCustomization[]) => {
    setActive(!active);

    return storeCustomizations.map((store) => ({
      ...store,
      isActive: filteredEntities.find((entity) => entity.id === store.entityId)
        ? !active
        : store.isActive,
    }));
  };

  const handleStoreConditionCheck = (checkedId: string, checked?: boolean) => {
    setActive(false);
    const { storeCustomizations } = getValues();

    const storeCondition = storeCustomizations?.find(
      (sc) => sc.entityId === checkedId
    );

    if (!storeCondition) {
      return storeCustomizations;
    }

    storeCondition.isActive = checked || false;

    setValue(STORE_CUSTOMIZATIONS_FIELD, [...storeCustomizations]);

    if (
      storeCustomizations.filter((store) => store.isActive).length ===
      entityList.length
    ) {
      setActive(true);
    }

    return storeCustomizations;
  };

  const handleStoreCustomFieldChange = (
    storeCustomizations: StoreCustomization[],
    entityId: string,
    fieldName: string,
    value: any
  ) => {
    const newStoreCustomizations = [...storeCustomizations];

    const storeCustomizationIdx = getStoreCustomizationIndex(
      storeCustomizations,
      entityId
    );

    const storeCustomization = storeCustomizations[storeCustomizationIdx];

    const updatedStoreCustomization = {
      ...storeCustomization,
      [fieldName]: value,
    };

    newStoreCustomizations[storeCustomizationIdx] =
      updatedStoreCustomization as StoreCustomization;

    return [...newStoreCustomizations];
  };

  const handleResetToGlobal = (
    storeCustomizations: StoreCustomization[],
    entityId: string
  ) => {
    const newStoreCustomizations = [...storeCustomizations];

    const storeCustomizationIdx = getStoreCustomizationIndex(
      storeCustomizations,
      entityId
    );
    const storeCustomization = storeCustomizations[storeCustomizationIdx];

    const updatedStoreCustomization = {
      ...storeCustomization,
      amount: null,
      requirePin: null,
      requireReason: null,
    };

    newStoreCustomizations[storeCustomizationIdx] =
      updatedStoreCustomization as StoreCustomization;

    return [...newStoreCustomizations];
  };

  const handleStoreSearch = (
    event: React.ChangeEvent<HTMLInputElement> | null
  ) => {
    const searchTerm = event?.target.value || "";
    setClue(searchTerm);
  };

  const getAmountErrorMessageIfExists = (
    storeCustomizations: StoreCustomization[],
    entityId: string
  ) => {
    const { storeCustomizations: storeCustomizationErrors } = errors;

    if (!storeCustomizationErrors || !storeCustomizationErrors.length)
      return "";

    const storeCustomizationError =
      storeCustomizationErrors[
        getStoreCustomizationIndex(storeCustomizations, entityId)
      ];

    if (!storeCustomizationError || !storeCustomizationError.amount) return "";

    return storeCustomizationError.amount.message.toString() || "";
  };

  const getGlobalConditionAmountText = () => {
    if (method === MANUAL_DISCOUNT_METHODS.PERCENT) {
      return `${parseFloat(amount).toFixed(2)} %`;
    }

    const dollarAmount = Number(parseFloat(amount).toFixed(2));
    return `$${dollarAmount}`;
  };

  useEffect(() => {
    const { storeCustomizations } = getValues();

    let newFilteredEntityList: EntityResponse[] = entityList;

    if (clue) {
      newFilteredEntityList = entityList.filter(
        (entity) => entity.name.toLowerCase().indexOf(clue.toLowerCase()) >= 0
      );
    }

    setFilteredEntities(getStores(newFilteredEntityList));

    const isAllDisplayedStoresSelected =
      storeCustomizations.filter(
        (store) =>
          store.isActive &&
          newFilteredEntityList.find((entity) => entity.id === store.entityId)
      ).length === newFilteredEntityList.length;

    setActive(isAllDisplayedStoresSelected);
  }, [clue, entityList]);

  useEffect(() => {
    if (storeCustomizationId) {
      const currentStoreCondition = getValues().storeCustomizations?.find(
        (store) => store.id === storeCustomizationId
      );
      const elementToRedirect = document.getElementById(
        `store-condition-${currentStoreCondition?.entityId}`
      );

      if (!currentStoreCondition) return;
      if (elementToRedirect) {
        elementToRedirect.scrollIntoView({ behavior: "smooth" });
      }
    }
  }, [filteredEntities]);

  useEffect(() => {
    if (entityList.length === 0) return;

    if (isEditMode) {
      const { storeCustomizations } = getValues();

      setValue(
        "storeCustomizations",
        entityList.map(
          (entity) =>
            storeCustomizations.find(
              (store) => store.entityId === entity.id
            ) || {
              entityId: entity.id,
              entityName: entity.name,
              amount: null,
              requirePin: null,
              requireReason: null,
              isActive: false,
            }
        )
      );
    } else if (getValues().storeCustomizations.length === 0) {
      setValue(
        "storeCustomizations",
        entityList.map((entity) => ({
          entityId: entity.id,
          entityName: entity.name,
          amount: null,
          requirePin: null,
          requireReason: null,
          isActive: true,
        }))
      );
    }

    setActive(
      getValues().storeCustomizations?.filter((store) => store.isActive)
        .length === entityList.length
    );
  }, [entityList]);

  return (
    <StyledGrid data-testid="select-store-grid" item sm={9} xs={12}>
      <SelectStoresWrapper>
        <DiscountInfoSection>
          <StyledGridContainer container>
            <StyledTitleTypography variant="h6" color="primaryBlackText">
              Select stores to apply the discount
            </StyledTitleTypography>

            <StyledTitleTypography variant="largeText" color="secondaryText">
              By default, global conditions will apply unless you customize them
              at the store level.
            </StyledTitleTypography>

            <StyledTitleTypography variant="largeText" color="secondaryText">
              Global discount conditions
            </StyledTitleTypography>

            <StyledGlobalConditionsGrid container>
              <StyledGlobalConditionWithIconGrid
                item
                xs={3}
                lg={3}
                md={3}
                sm={3}
              >
                <IconBoxWrap>
                  <Icon
                    iconName={
                      method === MANUAL_DISCOUNT_METHODS.PERCENT
                        ? "Percent"
                        : "Dollar"
                    }
                  />
                </IconBoxWrap>
                <GlobalConditionItem
                  label={
                    method === MANUAL_DISCOUNT_METHODS.PERCENT
                      ? "PERCENT"
                      : "AMOUNT"
                  }
                  value={getGlobalConditionAmountText()}
                />
              </StyledGlobalConditionWithIconGrid>

              <StyledGlobalConditionGrid item xs={3} lg={3} md={3} sm={3}>
                <GlobalConditionItem
                  label="TYPE"
                  value={isItem ? "Item" : "Cart"}
                />
              </StyledGlobalConditionGrid>

              <StyledGlobalConditionGrid item xs={3} lg={3} md={3} sm={3}>
                <GlobalConditionItem
                  label="REASON"
                  value={requireReason ? "Required" : "Not Required"}
                />
              </StyledGlobalConditionGrid>

              <StyledGlobalConditionGrid item xs={3} lg={3} md={3} sm={3}>
                <GlobalConditionItem
                  label="MANAGER PIN"
                  value={requirePin ? "Required" : "Not Required"}
                />
              </StyledGlobalConditionGrid>
            </StyledGlobalConditionsGrid>
          </StyledGridContainer>
        </DiscountInfoSection>
        <StyledSection>
          <Typography variant="h6" color="primaryBlackText">
            Select Stores
          </Typography>

          <div>
            <SearchInput
              value={clue}
              id="store-search-input"
              onChange={handleStoreSearch}
              testId="store-search-input"
            />
          </div>
        </StyledSection>
        <StyledInstructionsBox>
          <Typography variant="mediumText" color="secondaryText">
            Select the stores that will use this discount.
          </Typography>
          <Typography variant="mediumText" color="secondaryText">
            You can also set custom conditions for a particular store by
            overriding values individually.
          </Typography>
          <Typography variant="mediumText" color="secondaryText">
            To restore conditions back to global, use the &quot;Reset&quot;
            buttons.
          </Typography>
        </StyledInstructionsBox>
        {clue && (
          <StyledSection>
            <StyledStoresFoundLabelContainer>
              <Typography variant="largeTextStrong" color="primaryBlackText">
                {`${
                  Object.values(filteredEntities).length
                } stores found under `}
              </Typography>
              {`"${clue}"`}
            </StyledStoresFoundLabelContainer>
          </StyledSection>
        )}

        <Controller
          name="storeCustomizations"
          control={control}
          render={({
            field: { onChange: onStoresChange, value: storeCustomizations },
          }) => (
            <>
              <Checkbox
                label={clue ? "All Search Results" : "All Stores"}
                onChange={() =>
                  onStoresChange(handleSwitch(storeCustomizations))
                }
                value={active}
                checked={active}
              />

              <StyledAccordionWrapper>
                {filteredEntities.map((storeObj) => {
                  const customizationObj = getStoreCustomizationObj(
                    storeCustomizations,
                    storeObj.id
                  );
                  const discountOrStoreAmount =
                    customizationObj?.amount !== null
                      ? customizationObj?.amount
                      : amount;

                  const isRequirePinChecked =
                    customizationObj?.requirePin !== null
                      ? customizationObj?.requirePin
                      : requirePin;

                  const isRequireReasonChecked =
                    customizationObj?.requireReason !== null
                      ? customizationObj?.requireReason
                      : requireReason;

                  return (
                    <StyledStoreBox
                      // eslint-disable-next-line react/no-array-index-key
                      key={`store-condition-${storeObj.id}`}
                      id={`store-condition-${storeObj.id}`}
                      data-testid="entities-store-box"
                    >
                      <StyledAccordion
                        sx={{
                          "&:before": {
                            display: "none",
                          },
                        }}
                        defaultExpanded={
                          storeCustomizationId !== undefined &&
                          customizationObj?.id === storeCustomizationId
                        }
                      >
                        <StyledStoreRowBox>
                          <Checkbox
                            testId={`storeactive-${storeObj.id}`}
                            hideLabel
                            label={storeObj.city}
                            onChange={(checked) =>
                              handleStoreConditionCheck(storeObj.id, checked)
                            }
                            value={storeObj.id}
                            checked={!!customizationObj?.isActive}
                          />

                          <StyledAccordionSummary
                            expandIcon={<Icon iconName="ChevronDown" />}
                            aria-controls="panel1a-content"
                            id="panel1a-header"
                            data-testid={`storeactive-accordion-${storeObj.id}`}
                          >
                            <StyledAccordionSummaryRow>
                              <StyledStoreTypography>
                                {storeObj.name}
                              </StyledStoreTypography>

                              {isCustomConditionApplied(
                                storeCustomizations,
                                storeObj.id
                              ) && (
                                <StyledCustomConditionsAppliedBadge variant="smallText">
                                  Custom conditions applied
                                </StyledCustomConditionsAppliedBadge>
                              )}
                            </StyledAccordionSummaryRow>
                          </StyledAccordionSummary>
                        </StyledStoreRowBox>

                        <StyledAccordionDetails>
                          <StyledStoreCustomizationBox
                            data-testid={`styled-store-customization-box-${storeObj.id}`}
                          >
                            <Grid spacing={2} container>
                              <Grid item xs={12}>
                                <Controller
                                  render={() => (
                                    <SingleCustomStoreFieldBox>
                                      <Input
                                        type="number"
                                        startAdornment={
                                          method ===
                                          MANUAL_DISCOUNT_METHODS.PERCENT
                                            ? "Percent"
                                            : "Dollar"
                                        }
                                        label={
                                          method ===
                                          MANUAL_DISCOUNT_METHODS.PERCENT
                                            ? "Percentage %"
                                            : "Amount $"
                                        }
                                        helperText={getAmountErrorMessageIfExists(
                                          storeCustomizations,
                                          storeObj.id
                                        )}
                                        error={Boolean(
                                          getAmountErrorMessageIfExists(
                                            storeCustomizations,
                                            storeObj.id
                                          )
                                        )}
                                        onChange={(evt) =>
                                          onStoresChange(
                                            handleStoreCustomFieldChange(
                                              storeCustomizations,
                                              storeObj.id,
                                              "amount",
                                              evt.target.value
                                            )
                                          )
                                        }
                                        value={discountOrStoreAmount}
                                        required
                                      />
                                      {customizationObj?.amount !== null && (
                                        <Tooltip
                                          placement="bottom-end"
                                          title="Reset custom condition to restore global discount settings."
                                          variant="multiRow"
                                          testId={`store-condition-reset-amount-tooltip-${storeObj.id}`}
                                        >
                                          <IconButton
                                            testId={`store-condition-reset-amount-${storeObj.id}`}
                                            iconName="History"
                                            onClick={() =>
                                              onStoresChange(
                                                handleStoreCustomFieldChange(
                                                  storeCustomizations,
                                                  storeObj.id,
                                                  "amount",
                                                  null
                                                )
                                              )
                                            }
                                            variant="secondary"
                                            size="medium"
                                          />
                                        </Tooltip>
                                      )}
                                    </SingleCustomStoreFieldBox>
                                  )}
                                  rules={{
                                    validate: {
                                      required: (value) => {
                                        if (value === "")
                                          return "Amount is required";

                                        return true;
                                      },
                                      validate: (value) =>
                                        (typeof value === "string" &&
                                          validateAmountInput(method)(value)) ||
                                        true,
                                    },
                                  }}
                                  name={`storeCustomizations.${getStoreCustomizationIndex(
                                    storeCustomizations,
                                    storeObj.id
                                  )}.amount`}
                                  control={control}
                                />
                              </Grid>

                              <DividerBox />

                              <SecuritySettingsGrid spacing={2} container>
                                <Grid item xs={12}>
                                  <Typography variant="mediumTextStrong">
                                    Security Settings
                                  </Typography>
                                </Grid>
                                <Grid item xs={12}>
                                  <Controller
                                    render={() => (
                                      <SingleCustomStoreFieldBox>
                                        <Checkbox
                                          label="Require manager PIN"
                                          onChange={(checked) =>
                                            onStoresChange(
                                              handleStoreCustomFieldChange(
                                                storeCustomizations,
                                                storeObj.id,
                                                "requirePin",
                                                checked
                                              )
                                            )
                                          }
                                          value={storeObj.id}
                                          checked={isRequirePinChecked}
                                          testId={`store-security-require-manager-pin-${storeObj.id}`}
                                        />
                                        {customizationObj?.requirePin !==
                                          null && (
                                          <Tooltip
                                            placement="bottom-end"
                                            title="Reset custom condition to restore global discount settings."
                                            variant="multiRow"
                                            testId={`store-condition-reset-require-pin-tooltip-${storeObj.id}`}
                                          >
                                            <IconButton
                                              testId={`store-condition-reset-require-pin-${storeObj.id}`}
                                              iconName="History"
                                              onClick={() =>
                                                onStoresChange(
                                                  handleStoreCustomFieldChange(
                                                    storeCustomizations,
                                                    storeObj.id,
                                                    "requirePin",
                                                    null
                                                  )
                                                )
                                              }
                                              variant="secondary"
                                              size="medium"
                                            />
                                          </Tooltip>
                                        )}
                                      </SingleCustomStoreFieldBox>
                                    )}
                                    name={`storeCustomizations.${getStoreCustomizationIndex(
                                      storeCustomizations,
                                      storeObj.id
                                    )}.requirePin`}
                                    control={control}
                                  />
                                </Grid>
                                <Grid item xs={12}>
                                  <Controller
                                    render={() => (
                                      <SingleCustomStoreFieldBox>
                                        <Checkbox
                                          label="Require reason"
                                          onChange={(checked) =>
                                            onStoresChange(
                                              handleStoreCustomFieldChange(
                                                storeCustomizations,
                                                storeObj.id,
                                                "requireReason",
                                                checked
                                              )
                                            )
                                          }
                                          value={storeObj.id}
                                          checked={isRequireReasonChecked}
                                          testId={`store-security-require-reason-${storeObj.id}`}
                                        />
                                        {customizationObj?.requireReason !==
                                          null && (
                                          <Tooltip
                                            placement="bottom-end"
                                            title="Reset custom condition to restore global discount settings."
                                            variant="multiRow"
                                            testId={`store-condition-reset-require-reason-tooltip-${storeObj.id}`}
                                          >
                                            <IconButton
                                              testId={`store-condition-reset-require-reason-${storeObj.id}`}
                                              iconName="History"
                                              onClick={() =>
                                                onStoresChange(
                                                  handleStoreCustomFieldChange(
                                                    storeCustomizations,
                                                    storeObj.id,
                                                    "requireReason",
                                                    null
                                                  )
                                                )
                                              }
                                              variant="secondary"
                                              size="medium"
                                            />
                                          </Tooltip>
                                        )}
                                      </SingleCustomStoreFieldBox>
                                    )}
                                    name={`storeCustomizations.${getStoreCustomizationIndex(
                                      storeCustomizations,
                                      storeObj.id
                                    )}.requireReason`}
                                    control={control}
                                  />
                                </Grid>
                              </SecuritySettingsGrid>
                            </Grid>
                          </StyledStoreCustomizationBox>
                          {isCustomConditionApplied(
                            storeCustomizations,
                            storeObj.id
                          ) && (
                            <Tooltip
                              placement="bottom-start"
                              title="Reset all custom conditions for this store to match global discount settings."
                              variant="multiRow"
                              testId={`reset-button-tooltip-${storeObj.id}`}
                            >
                              <Button
                                iconName="History"
                                label="Reset to global"
                                onClick={() =>
                                  onStoresChange(
                                    handleResetToGlobal(
                                      storeCustomizations,
                                      storeObj.id
                                    )
                                  )
                                }
                                testId={`store-condition-global-reset-${storeObj.id}`}
                                variant="text"
                              />
                            </Tooltip>
                          )}
                        </StyledAccordionDetails>
                      </StyledAccordion>
                    </StyledStoreBox>
                  );
                })}

                {errors?.storeCustomizations ? (
                  <StyledErrorMsg>Select store!</StyledErrorMsg>
                ) : (
                  ""
                )}
              </StyledAccordionWrapper>
            </>
          )}
        />
      </SelectStoresWrapper>
    </StyledGrid>
  );
};

SelectStoreStep.defaultProps = {
  storeCustomizationId: undefined,
};

export default SelectStoreStep;
