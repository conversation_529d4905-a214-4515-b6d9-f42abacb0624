import React from "react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import * as ReactDOMClient from "react-dom/client";
import singleSpaReact from "single-spa-react";
import Root from "./root.component";
import ErrorView from "./views/ErrorView";

const lifecycles = singleSpaReact({
  React,
  ReactDOMClient,
  rootComponent: Root,
  errorBoundary: (err, info, props) => (
    <TreezThemeProvider>
      <ErrorView err={err} info={info} props={props} />
    </TreezThemeProvider>
  ),
});

export const { bootstrap } = lifecycles;
export const { mount } = lifecycles;
export const { unmount } = lifecycles;
