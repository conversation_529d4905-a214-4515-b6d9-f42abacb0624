import React from "react";
import { GridRenderCellParams } from "@mui/x-data-grid-pro";
import { StaticChip, Tooltip } from "@treez-inc/component-library";

export const storeCellRenderer = (params: GridRenderCellParams) => {
  if (params.row.storeCustomizations && params.row.storeCustomizations[0]) {
    const entities = params.row.storeCustomizations.map(
      (store: any) => store.entityName
    );

    return entities.length > 1 && !params.row.isChild ? (
      <Tooltip
        variant="context"
        enterDelay={400}
        leaveDelay={0}
        title={entities.join(", ")}
      >
        <StaticChip
          badgeContent={entities.length}
          label="Multiple"
          testId="multiple-store-chip"
        />
      </Tooltip>
    ) : (
      <Tooltip
        variant="context"
        enterDelay={400}
        leaveDelay={0}
        title={params.row.entityName}
      >
        <StaticChip testId="single-store-chip" label={params.row.entityName} />
      </Tooltip>
    );
  }
  return null;
};
