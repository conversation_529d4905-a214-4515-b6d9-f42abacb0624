import { useState, useEffect } from "react";
import {
  GridRowId,
  GridTreeNodeWithRender,
  gridFilteredDescendantCountLookupSelector,
  useGridApiContext,
  useGridSelector,
} from "@mui/x-data-grid-pro";

const useTreeDataGroupingCell = (
  id: GridRowId,
  field: string,
  rowNode: GridTreeNodeWithRender
) => {
  const apiRef = useGridApiContext();
  const [isExpanded, setExpanded] = useState(
    rowNode.type === "group" && rowNode.childrenExpanded
  );

  const filteredDescendantCountLookup = useGridSelector(
    apiRef,
    gridFilteredDescendantCountLookupSelector
  );

  const handleClick = (event: React.MouseEvent) => {
    if (rowNode.type !== "group") {
      return;
    }

    const newExpandedState = !rowNode.childrenExpanded;
    apiRef.current.setRowChildrenExpansion(id, newExpandedState);
    apiRef.current.setCellFocus(id, field);
    setExpanded(newExpandedState);
    event.stopPropagation();
  };

  useEffect(() => {
    if (rowNode.type !== "group") {
      return;
    }

    if (isExpanded !== rowNode.childrenExpanded) {
      apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
      apiRef.current.setCellFocus(id, field);
    }
  }, [apiRef, id, field, isExpanded, rowNode]);

  return {
    hasChildren: filteredDescendantCountLookup[rowNode.id] > 0,
    isExpanded,
    handleClick,
  };
};

export default useTreeDataGroupingCell;
