import React, { useContext, useEffect, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { Box, Grid, styled, Typography } from "@mui/material";
import {
  Checkbox,
  convertPxToRem,
  Panel,
  SearchInput,
} from "@treez-inc/component-library";
import { EntityResponse, Store } from "../../../../../interfaces/entity";
import {
  AutomatedDiscountFormData,
  StoreCustomization,
} from "../../../../../interfaces/discounts";
import { getStores } from "../../../../../utils";
import { EntityContext } from "../../../../../providers/EntityProvider";

const STORE_CUSTOMIZATIONS_FIELD = "storeCustomizations";

const StyledGrid = styled(Grid)(({ theme }) => ({
  width: "100%",
  background: theme.palette.primaryWhite.main,
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  gap: convertPxToRem(24),
}));

const StyledSection = styled(Box)(({ theme }) => ({
  width: "100%",
  height: "auto",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  boxSizing: "border-box",
  background: theme.palette.primaryWhite.main,
}));

const StyledSearchResultsSection = styled(StyledSection)({
  justifyContent: "flex-start",
  whiteSpace: "pre-wrap",
});

const StyledInstructionsBox = styled(Box)({
  display: "flex",
  flexDirection: "column",
  marginBottom: convertPxToRem(12),
  width: convertPxToRem(544),
});

const StyledStoreRow = styled(Box)({
  margin: convertPxToRem(8),
});

const StyledStoreRowBox = styled(Box)({
  height: convertPxToRem(48),
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
});

const StyledStoreTypography = styled(Typography)({
  textTransform: "capitalize",
  marginLeft: convertPxToRem(8),
});

const SelectStoreStep = () => {
  const entityList = useContext(EntityContext);
  const { control, setValue, getValues } =
    useFormContext<AutomatedDiscountFormData>();

  const [searchTerm, setSearchTerm] = useState("");
  const [filteredEntities, setFilteredEntities] = useState<Store[]>([]);

  const handleStoreSearch = (
    event: React.ChangeEvent<HTMLInputElement> | null
  ) => {
    const searchTermValue = event?.target.value || "";
    setSearchTerm(searchTermValue);
  };

  const allFilteredEntitiesSelected = (
    storeCustomizations: StoreCustomization[]
  ) =>
    filteredEntities.every(
      (filteredEntity) =>
        !!storeCustomizations.find(
          (storeCustomization) =>
            storeCustomization.entityId === filteredEntity.id
        )
    );

  const handleSwitch = (storeCustomizations: StoreCustomization[]) => {
    if (allFilteredEntitiesSelected(storeCustomizations)) {
      return storeCustomizations.filter(
        (storeCustomization) =>
          !filteredEntities.find(
            (filteredEntity) =>
              filteredEntity.id === storeCustomization.entityId
          )
      );
    }
    const filteredEntitiesToCheck = filteredEntities.filter(
      (filteredEntity) =>
        !storeCustomizations.find(
          (storeCustomization) =>
            storeCustomization.entityId === filteredEntity.id
        )
    );

    return storeCustomizations.concat(
      filteredEntitiesToCheck.map((filteredEntity) => ({
        entityId: filteredEntity.id,
        entityName: filteredEntity.name,
        isActive: null,
        requireReason: null,
        requirePin: null,
      }))
    );
  };

  const handleStoreConditionCheck = (checkedId: string, checked?: boolean) => {
    const { storeCustomizations } = getValues();

    if (checked) {
      const matchingEntity = entityList.find(
        (entity) => entity.id === checkedId
      );

      if (matchingEntity) {
        const newStoreCustomizations = storeCustomizations.concat([
          {
            entityId: checkedId,
            entityName: matchingEntity.name,
            isActive: null,
            requireReason: null,
            requirePin: null,
          },
        ]);

        setValue(STORE_CUSTOMIZATIONS_FIELD, newStoreCustomizations);
      }
    } else {
      const newStoreCustomizations = storeCustomizations.filter(
        (storeCustomization) => storeCustomization.entityId !== checkedId
      );

      setValue(STORE_CUSTOMIZATIONS_FIELD, newStoreCustomizations);
    }

    return storeCustomizations;
  };

  useEffect(() => {
    let newFilteredEntityList: EntityResponse[] = entityList;

    if (searchTerm) {
      newFilteredEntityList = entityList.filter(
        (entity) =>
          entity.name.toLowerCase().indexOf(searchTerm.toLowerCase()) >= 0
      );
    }

    setFilteredEntities(getStores(newFilteredEntityList));
  }, [searchTerm, entityList]);

  useEffect(() => {
    if (entityList.length === 0) return;

    const { id } = getValues();

    let newStoreCustomizations: StoreCustomization[] | undefined;
    if (!id) {
      newStoreCustomizations = entityList.map((entity) => ({
        entityId: entity.id,
        entityName: entity.name,
        amount: null,
        requirePin: null,
        requireReason: null,
        isActive: null,
      }));

      setValue(STORE_CUSTOMIZATIONS_FIELD, [...newStoreCustomizations]);
    }
  }, []);

  return (
    <StyledGrid data-testid="automated-select-store-step" item sm={9} xs={12}>
      <Panel testId="automated-discount-select-store-panel">
        <StyledSection>
          <Typography variant="h6" color="primaryBlackText">
            Select Stores
          </Typography>
          <div>
            <SearchInput
              value={searchTerm}
              id="store-search-input"
              onChange={handleStoreSearch}
              testId="store-search-input"
            />
          </div>
        </StyledSection>
        <StyledInstructionsBox>
          <Typography variant="mediumText" color="secondaryText">
            Select the stores that will use this discount.
          </Typography>
        </StyledInstructionsBox>
        {searchTerm && (
          <StyledSearchResultsSection>
            <Typography variant="largeTextStrong" color="primaryBlackText">
              {`${Object.values(filteredEntities).length} stores found under `}
            </Typography>
            <Typography variant="mediumText" color="secondaryText">
              {`"${searchTerm}"`}
            </Typography>
          </StyledSearchResultsSection>
        )}
        <Controller
          name="storeCustomizations"
          control={control}
          render={({
            field: { onChange: onStoresChange, value: storeCustomizations },
          }) => (
            <>
              <Checkbox
                label={searchTerm ? "All Search Results" : "All Stores"}
                onChange={() =>
                  onStoresChange(handleSwitch(storeCustomizations))
                }
                value={allFilteredEntitiesSelected(storeCustomizations)}
                checked={allFilteredEntitiesSelected(storeCustomizations)}
                testId="automated-discount-select-all-stores-checkbox"
              />
              {filteredEntities.map((storeObj) => (
                <StyledStoreRow key={`store-condition-${storeObj.id}`}>
                  <Panel testId="automated-discount-select-store-box">
                    <StyledStoreRowBox>
                      <Checkbox
                        testId={`store-active-${storeObj.id}`}
                        hideLabel
                        label={storeObj.city}
                        value={storeObj.id}
                        onChange={(checked) =>
                          handleStoreConditionCheck(storeObj.id, checked)
                        }
                        checked={
                          !!storeCustomizations.find(
                            (storeCustomization) =>
                              storeCustomization.entityId === storeObj.id
                          )
                        }
                      />
                      <StyledStoreTypography>
                        {storeObj.name}
                      </StyledStoreTypography>
                    </StyledStoreRowBox>
                  </Panel>
                </StyledStoreRow>
              ))}
            </>
          )}
        />
      </Panel>
    </StyledGrid>
  );
};

export default SelectStoreStep;
