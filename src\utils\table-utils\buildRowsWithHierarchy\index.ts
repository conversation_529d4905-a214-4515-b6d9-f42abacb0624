import { OrgDiscountResponse } from "../../../interfaces/responseModels";
import { OrgDiscountRow } from "../../../interfaces/table";

const buildRowsWithHierarchy = (
  discounts: OrgDiscountResponse[]
): OrgDiscountRow[] => {
  const discountRows: OrgDiscountRow[] = [];
  discounts?.forEach((disc) => {
    discountRows.push({
      ...disc,
      createdAt: new Date(disc.createdAt),
      updatedAt: new Date(disc.updatedAt),
      isChild: false,
      isStackable: disc.isStackable || false,
      conditions: disc.conditions
        ? {
            ...disc.conditions,
            customerEvents: disc.conditions.customerEvents
              ? disc.conditions.customerEvents[0]
              : null,
          }
        : null,
      hierarchy: [`disc-${disc.id}`],
      entityName:
        disc.storeCustomizations.length === 1
          ? disc.storeCustomizations[0].entityName
          : undefined,
    });

    disc?.storeCustomizations?.forEach((store) => {
      discountRows.push({
        ...disc,
        createdAt: new Date(disc.createdAt),
        updatedAt: new Date(disc.updatedAt),
        id: store.id,
        parentId: disc.id,
        entityId: store.entityId,
        entityName: store.entityName,
        amount: store.amount || disc.amount,
        conditions: disc.conditions
          ? {
              ...disc.conditions,
              customerEvents: disc.conditions.customerEvents
                ? disc.conditions.customerEvents[0]
                : null,
            }
          : null,
        isChild: true,
        hierarchy: [`disc-${disc.id}`, `store-${store.id}`],
      });
    });
  });

  return discountRows;
};

export { buildRowsWithHierarchy };
