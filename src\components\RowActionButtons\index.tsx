import React from "react";
import { GridRowId, GridTreeNodeWithRender } from "@mui/x-data-grid-pro";
import { IconButton } from "@treez-inc/component-library";
import { Box, styled } from "@mui/material/";
import { useTreeDataGroupingCell } from "../../hooks";
import { OrgDiscountRow } from "../../interfaces/table";
import DiscountActionMenu from "../DiscountActionMenu";
import { OrgDiscountResponse } from "../../interfaces/responseModels";

export interface RowActionButtonsProps {
  id: GridRowId;
  field: string;
  row: OrgDiscountRow;
  rowNode: GridTreeNodeWithRender;
  openDiscountModal: (
    type: string,
    {
      discount,
      parentDiscountId,
      storeToUnassignId,
    }: {
      discount?: OrgDiscountResponse;
      parentDiscountId?: string;
      storeToUnassignId?: string;
    }
  ) => void;
  openDiscountLog: (discount: OrgDiscountResponse) => void;
}

const StyledRowActionContainer = styled(Box)(() => ({
  display: "flex",
}));

const RowActionButtons: React.FC<RowActionButtonsProps> = ({
  id,
  field,
  row,
  rowNode,
  openDiscountModal = () => {},
  openDiscountLog = () => {},
}) => {
  const { hasChildren, isExpanded, handleClick } = useTreeDataGroupingCell(
    id,
    field,
    rowNode
  );

  return (
    <StyledRowActionContainer
      key={`row-action-buttons-container-${id}`}
      data-testid={`row-action-buttons-${id}`}
    >
      {rowNode.depth === 0 && (
        <>
          <DiscountActionMenu
            row={row}
            openDiscountModal={openDiscountModal}
            openDiscountLog={openDiscountLog}
          />
          {hasChildren && (
            <IconButton
              iconName={isExpanded ? "ChevronUp" : "ChevronRight"}
              onClick={handleClick}
              size="small"
              testId={`expand-row-button-${id}`}
              variant="secondary"
            />
          )}
        </>
      )}
    </StyledRowActionContainer>
  );
};

export default RowActionButtons;
