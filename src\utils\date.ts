import { ScheduleResponse } from "interfaces/responseModels";
import moment from "moment";

/* eslint-disable radix */
export const getDateLocaleString = (date: Date) =>
  date.toLocaleDateString("sv");

export const getDateFromTime = (time: string) => {
  const [hour, minute, second] = time.split(":");
  const date = new Date();
  date.setHours(parseInt(hour, 10));
  date.setMinutes(parseInt(minute, 10));
  date.setSeconds(parseInt(second, 10));

  return date;
};

export const formatDateToLocaleTime = (date: Date) =>
  date.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });

export const formatDateToLocale = (date: Date) =>
  date.toLocaleDateString("en-US");

export const formatDateToTime = (date: Date) =>
  date.toLocaleTimeString("it-IT");

export const parseDateString = (date: string) => {
  const dateParts = date.split("-");

  const year = parseInt(dateParts[0], 10);
  const month = parseInt(dateParts[1], 10) - 1;
  const day = parseInt(dateParts[2], 10);

  return new Date(year, month, day);
};

export const formatScheduledDateTime = (
  schedule: Partial<ScheduleResponse>
) => {
  if (!schedule) {
    return "";
  }

  const { startDate, startTime } = schedule;
  const formattedStartDate = moment(startDate, "YYYY-MM-DD").format("MM/DD/YY");

  if (startTime) {
    const formattedTime = moment(startTime, "HH:mm:ss").format("hh:mm A");
    return `${formattedStartDate} ${formattedTime}`;
  }

  return formattedStartDate;
};

export const combineDateAndTime = (date: Date, time: Date) => {
  const result = new Date(date);

  result.setHours(time.getHours());
  result.setMinutes(time.getMinutes());

  return result;
};

const numToStr = (num: number) => {
  const numToStrArray = ["first", "second", "third", "fourth", "fifth"];

  return numToStrArray[num - 1];
};

export const getWeekNumberStr = (date: Date) => {
  const monthStart = moment(date).startOf("month");

  const weekDifference = moment(date).week() - moment(monthStart).week();

  if (moment(date).weekday() - moment(monthStart).weekday() < 0) {
    return numToStr(weekDifference);
  }
  return numToStr(weekDifference + 1);
};

export const getWeekDay = (date: Date) => moment(date).format("dddd");

export const getDayOfMonth = (date: Date) => moment(date).format("Do");

export const getDayOfYear = (date: Date) => moment(date).format("MMMM Do");
