import apiInterceptor from "./apiInterceptor";

import retry from "./retry";

retry.retryRequest = jest.fn().mockReturnValue("retry");

const clearTokens = jest.fn();
const api = apiInterceptor(clearTokens);

describe("axios interceptor response for network error and timeout error", () => {
  beforeEach(() => {
    jest.spyOn(console, "error").mockImplementation(jest.fn());
  });

  it("should retry request when network error occur", async () => {
    const config = {
      message: "Network Error",
      code: "1",
      config: {
        headers: {
          retry: "1",
        },
      },
      request: {},
      response: {},
    };

    const expected = { headers: { retry: "2" } };

    // @ts-ignore
    await api.interceptors.response.handlers[0].rejected(config);
    expect(retry.retryRequest).toBeCalledWith(expected);
    expect(retry.retryRequest).toBeCalledTimes(1);
  });

  it("should retry request when timeout error occur", async () => {
    const config = {
      message: "timeout",
      code: "1",
      config: {
        headers: {
          retry: "1",
        },
      },
      request: {},
      response: {},
    };

    const expected = { headers: { retry: "2" } };

    // @ts-ignore
    await api.interceptors.response.handlers[0].rejected(config);
    expect(retry.retryRequest).toBeCalledWith(expected);
    expect(retry.retryRequest).toBeCalledTimes(1);
  });

  it("should throw error if rety is greater than 3", async () => {
    const config = {
      message: "Network Error",
      code: "1",
      config: {
        headers: {
          retry: "4",
        },
      },
      request: {},
      response: {},
    };
    try {
      // @ts-ignore
      await api.interceptors.response.handlers[0].rejected(config);
    } catch (err) {
      expect(err).toEqual(config);
      expect(retry.retryRequest).toBeCalledTimes(0);
    }
  });

  it("should throw error if error is not net work or timeout error", async () => {
    const config = {
      message: "some error",
      code: "1",
      config: {
        headers: {
          retry: "0",
        },
      },
      request: {},
      response: {},
    };
    try {
      // @ts-ignore
      await api.interceptors.response.handlers[0].rejected(config);
    } catch (err) {
      expect(err).toEqual(config);
      expect(retry.retryRequest).toBeCalledTimes(0);
    }
  });

  it("should throw error if there is no retry header", async () => {
    const config = {
      message: "timeout",
      code: "1",
      config: { headers: {} },
      request: {},
      response: {},
    };
    try {
      // @ts-ignore
      await api.interceptors.response.handlers[0].rejected(config);
    } catch (err) {
      expect(err).toEqual(config);
      expect(retry.retryRequest).toBeCalledTimes(0);
    }
  });

  it("should throw error if there is no retry header", async () => {
    const config = {
      message: "some error",
      code: "1",
      config: { headers: {} },
      request: {},
      response: {},
    };
    try {
      // @ts-ignore
      await api.interceptors.response.handlers[0].rejected(config);
    } catch (err) {
      expect(err).toEqual(config);
      expect(retry.retryRequest).toBeCalledTimes(0);
    }
  });

  it("should clear token when error status is 401", async () => {
    const config = {
      message: "Network Error",
      code: "1",
      config: {
        headers: {
          retry: "1",
        },
      },
      request: {},
      response: {
        status: 401,
      },
    };

    try {
      // @ts-ignore
      await api.interceptors.response.handlers[0].rejected(config);
    } catch (err: any) {
      expect(clearTokens).toBeCalledTimes(1);
      expect(err.message).toBe("An error has occurred. Please try again");
    }
  });

  it("should return error when status is 403", async () => {
    const config = {
      message: "Network Error",
      code: "1",
      config: {
        headers: {
          retry: "1",
        },
      },
      request: {},
      response: {
        status: 403,
      },
    };

    try {
      // @ts-ignore
      await api.interceptors.response.handlers[0].rejected(config);
    } catch (err: any) {
      expect(err).toBe(config);
      expect(retry.retryRequest).toBeCalledTimes(0);
    }
  });
});
