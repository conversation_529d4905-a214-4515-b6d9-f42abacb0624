import React, {
  ReactNode,
  createContext,
  useContext,
  useState,
  useMemo,
} from "react";

interface DrawerStateProps {
  open: boolean;
  title: string;
  data: any;
}

interface DrawerContextProps {
  drawerState: DrawerStateProps;
  openDrawer: (state: DrawerStateProps) => void;
  closeDrawer: () => void;
}

interface DrawerProviderProps {
  children: ReactNode;
}

const defaultState: DrawerStateProps = { open: false, title: "", data: null };
const DrawerContext = createContext<DrawerContextProps>({
  drawerState: defaultState,
  openDrawer: () => {},
  closeDrawer: () => {},
});

export const useDrawer = () => useContext(DrawerContext);

const DrawerProvider = ({ children }: DrawerProviderProps) => {
  const [drawerState, setDrawerState] = useState<DrawerStateProps>(defaultState);

  const openDrawer = (state: DrawerStateProps) => {
    setDrawerState({ ...defaultState, ...state, open: true });
  };

  const closeDrawer = () => {
    setDrawerState((prevDrawerState) => ({ ...prevDrawerState, open: false }));
  };

  const value = useMemo(
    () => ({
      drawerState,
      openDrawer,
      closeDrawer,
    }),
    [drawerState]
  );

  return (
    <DrawerContext.Provider value={value}>{children}</DrawerContext.Provider>
  );
};
export default DrawerProvider;
