/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react";
import { Grid, Box, styled } from "@mui/material";
import { LinearStepper } from "@treez-inc/component-library";

interface VerticalStepperProps {
  steps: Array<{
    label: string;
    completed?: boolean;
    disabled?: boolean;
    onClick: (index: number) => unknown;
  }>;
  activeStep: number;
}
const StyledGrid = styled(Grid)({
  display: "flex",
  flexDirection: "column",
});

const VerticalStepper: React.FC<VerticalStepperProps> = (props) => {
  const { steps, activeStep } = props;

  return (
    <StyledGrid
      item
      sm={3}
      sx={{
        display: { xs: "none", sm: "flex" },
        position: "fixed",
      }}
    >
      <Box>
        <LinearStepper
          activeStep={activeStep}
          steps={steps}
          testId="roleaddoredit-stepper"
        />
      </Box>
    </StyledGrid>
  );
};

export default VerticalStepper;
