import React from "react";
import { styled } from "@mui/material/";
import { convertPxToRem } from "@treez-inc/component-library";

const Image = styled("img")(() => ({
  borderRadius: convertPxToRem(16),
  width: "100%",
  height: "100%",
}));

interface ImageViewerProps {
  imageUrl: string;
  onClick?: () => void;
}

const ImageViewer = ({ imageUrl, onClick }: ImageViewerProps) => <Image src={imageUrl} onClick={onClick} data-testId="image-viewer" />;

export default ImageViewer;
