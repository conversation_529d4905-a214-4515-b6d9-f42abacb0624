import React from "react";
import { styled } from "@mui/material";
import {
  Button,
  ButtonProps,
  Tooltip,
  convertPxToRem,
} from "@treez-inc/component-library";
import { PERMISSIONS_MESSAGES } from "../../constants";

// TODO: Move this to the component library
export interface PageHeaderProps {
  /** Props for the primary button, excluding variant and small props. */
  buttonProps: Omit<ButtonProps, "small" | "variant">;
  /** Test id that can be used for targeting elements in tests: data-testid={testId}. */
  testId?: string;
  /** Custom filter options for the page header. */
  filterOptions?: React.ReactNode;
}

const StyledPageHeader = styled("div")(({ theme }) => ({
  backgroundColor: theme.palette.grey02.main,
  padding: `${convertPxToRem(24)} ${convertPxToRem(20)}`,
  [theme.breakpoints.up("sm")]: {
    padding: `${convertPxToRem(24)} ${convertPxToRem(20)}`,
  },
  [theme.breakpoints.up("lg")]: {
    padding: `${convertPxToRem(24)} ${convertPxToRem(52)}`,
  },
  [theme.breakpoints.up("xl")]: {
    padding: `${convertPxToRem(24)} ${convertPxToRem(80)}`,
  },
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
}));

const StyledFlexContainer = styled("div")({
  display: "flex",
  alignItems: "center",
  marginLeft: convertPxToRem(12),
});

const PageHeader = ({
  buttonProps,
  testId,
  filterOptions,
}: PageHeaderProps) => (
  <StyledPageHeader data-testid={testId}>
    <StyledFlexContainer data-testid={testId && `${testId}-filter-options`}>
      {filterOptions}
    </StyledFlexContainer>
    <StyledFlexContainer>
      {buttonProps.disabled ? (
        <Tooltip title={PERMISSIONS_MESSAGES.NO_CREATE} variant="multiRow">
          <Button {...buttonProps} />
        </Tooltip>
      ) : (
        <Button {...buttonProps} />
      )}
    </StyledFlexContainer>
  </StyledPageHeader>
);

export default PageHeader;
