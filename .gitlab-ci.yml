include:
  - project: "treez-inc/engineering/ci-templates/mfe"
    ref: "latest"
    file: "template.yml"

variables:
  # Path to the MFE. Must match the path defined in MSO Core UI
  BUCKET_PATH: discount-management/latest

install_dependencies:
  extends: .install_dependencies

lint:
  extends: .lint

unit_test:
  variables: 
    NODE_OPTIONS: "--max_old_space_size=4096"
  extends: .unit_test

build_dev:
  extends: .build_dev

deploy_dev:
  extends: .deploy_dev

cypress_tests:
  image: registry.gitlab.com/treez-inc/engineering/docker-images/cypress:node-18.16.1-chrome-114.0.5735.133-1-ff-114.0.2-edge-114.0.1823.51-1
  stage: post_deploy_dev
  environment: sandbox
  allow_failure: true
  before_script:
    - echo $REACT_APP_LOGIN_ENV_APP_URL
    - apt-get update
    - apt-get install jq -y
    - apt-get install python3.9 -y
    - apt-get install python3-pip -y
    - pip3 install awscli --upgrade
    - export NPM_TOKEN=${DEFAULT_NPM_TOKEN}
    - npm install -g yarn --force
  script:
    - yarn install --frozen-lockfile --no-progress
    - yarn run cypress:run
  dependencies:
    - deploy_dev

build_build:
  extends: .build_build

deploy_build:
  extends: .deploy_build

build_prod:
  extends: .build_prod

deploy_prod:
  extends: .deploy_prod

tag:
  extends: .tag
