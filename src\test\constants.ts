import { ManualDiscountMethods } from "../constants/discounts";
import { OrgDiscountResponse } from "../interfaces/responseModels";
import { Tokens } from "../interfaces/tokens";

export const tokens: Tokens = {
  accessToken: "accessToken",
  expiresIn: 300,
  refreshToken: "refreshToken",
  idToken: "idToken",
};

export const entities = [
  {
    id: "entity-1",
    name: "Entity",
    address: {
      city: "LA",
      region: "CA",
      country: "USA",
    },
  },
  {
    id: "entity-2",
    name: "Entity 2",
    address: {
      city: "LA",
      region: "CA",
      country: "USA",
    },
  },
];

export const testDiscountsResponse: OrgDiscountResponse[] = [
  {
    id: "disc1",
    organizationId: "orgId-1",
    title: "Disc 1",
    displayTitle: "Disc 1",
    description: "Disc 1",
    amount: "10",
    method: ManualDiscountMethods.DOLLAR,
    isActive: false,
    isManual: true,
    isCart: true,
    isAdjustment: false,
    isStackable: null,
    requireReason: false,
    requirePin: true,
    requireCoupon: false,
    showEcommerce: true,
    showCustomerFacing: true,
    showSellTreez: true,
    displayImageOnly: false,
    createdAt: "2024-01-23T21:43:52.421Z",
    updatedAt: "2024-01-23T21:43:52.421Z",
    customerGroups: [],
    storeCustomizations: [
      {
        id: "disc1-store1",
        entityId: "entity-1",
        entityName: "Entity1",
        orgDiscountId: "orgId-1",
        amount: "15",
        isActive: true,
        isCart: null,
        isAdjustment: null,
        requireCoupon: null,
        requirePin: true,
        requireReason: true,
        createdAt: "2024-01-23T21:43:52.421Z",
        updatedAt: "2024-01-23T21:43:52.421Z",
      },
      {
        id: "disc1-store2",
        entityId: "entity-2",
        entityName: "Entity2",
        orgDiscountId: "orgId-1",
        amount: null,
        isActive: false,
        isCart: null,
        isAdjustment: null,
        requireCoupon: null,
        requirePin: true,
        requireReason: true,
        createdAt: "2024-01-23T21:43:52.421Z",
        updatedAt: "2024-01-23T21:43:52.421Z",
      },
    ],
    collections: [],
    coupons: [],
    schedule: null,
    conditions: null,
    collectionsRequired: [],
  },
  {
    id: "disc2",
    organizationId: "orgId-1",
    title: "Disc 2",
    displayTitle: "Disc 2",
    description: "Disc 2",
    amount: "10",
    method: ManualDiscountMethods.DOLLAR,
    isActive: true,
    isManual: true,
    isCart: true,
    isAdjustment: false,
    isStackable: null,
    requireReason: false,
    requirePin: true,
    requireCoupon: false,
    showEcommerce: true,
    showCustomerFacing: true,
    showSellTreez: true,
    displayImageOnly: false,
    createdAt: "2024-01-23T21:43:52.421Z",
    updatedAt: "2024-01-23T21:43:52.421Z",
    customerGroups: [],
    storeCustomizations: [
      {
        id: "disc2-store2",
        entityId: "entity-2",
        entityName: "Entity2",
        orgDiscountId: "orgId-1",
        amount: null,
        isActive: false,
        isCart: null,
        isAdjustment: null,
        requireCoupon: null,
        requirePin: true,
        requireReason: true,
        createdAt: "2024-01-23T21:43:52.421Z",
        updatedAt: "2024-01-23T21:43:52.421Z",
      },
    ],
    collections: [],
    coupons: [],
    schedule: null,
    conditions: null,
    collectionsRequired: [],
  },
];
