import React from "react";
import {
  Control,
  Controller,
  FieldValues,
  useFormContext,
} from "react-hook-form";
import {
  Autocomplete,
  AutocompleteProps,
  convertPxToRem,
} from "@treez-inc/component-library";
import { Box, styled } from "@mui/material";
import { DiscountConditionFormData } from "../../../../../../interfaces/discounts";

interface AutocompleteInputProps {
  name: string;
  control: Control<FieldValues, DiscountConditionFormData>;
  label: string;
  options: AutocompleteProps["options"];
  isOptionEqualToValue?: AutocompleteProps["isOptionEqualToValue"];
  errorMsg?: string;
  testId?: string;
}

const StyledContainer = styled(Box)({
  maxWidth: convertPxToRem(300),
});

const AutocompleteInput = ({
  name,
  control,
  label,
  options,
  isOptionEqualToValue,
  errorMsg,
  testId,
}: AutocompleteInputProps) => {
  const { getValues } = useFormContext();
  const defaultValue = getValues(name);

  return (
    <Controller
      name={name}
      control={control}
      rules={{
        required: errorMsg || "An option is required",
      }}
      render={({ field: { onChange }, fieldState: { error } }) => (
        <StyledContainer data-testid={testId && `${testId}-autocomplete-input`}>
          <Autocomplete
            defaultValue={defaultValue}
            inputProps={{
              label,
              onChange: () => {},
              error: !!error,
              helperText: error?.message,
            }}
            options={options}
            onChange={(_, selectedItems) => {
              onChange(selectedItems);
            }}
            isOptionEqualToValue={isOptionEqualToValue}
            freeSolo={false}
            multiline
          />
        </StyledContainer>
      )}
    />
  );
};

export default AutocompleteInput;
