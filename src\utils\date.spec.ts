import {
  getWeekNumberStr,
  getWeekDay,
  getDayOfMonth,
  getDayOfYear,
} from "./date";

describe("date.spec.ts", () => {
  it("getWeekNumberSt returns first for when given first tuesday", () => {
    const date = new Date("2024-02-06T09:00:00.000Z");

    expect(getWeekNumberStr(date)).toBe("first");
  });

  it("getWeekNumberSt returns third for when given third friday", () => {
    const date = new Date("2024-06-21T09:00:00.000Z");

    expect(getWeekNumberStr(date)).toBe("third");
  });

  it("getWeekDay returns Friday on a Friday ", () => {
    const date = new Date("2024-07-05T09:00:00.000Z");

    expect(getWeekDay(date)).toBe("Friday");
  });

  it("getWeekDay returns Monday on a Monday ", () => {
    const date = new Date("2024-05-06T09:00:00.000Z");

    expect(getWeekDay(date)).toBe("Monday");
  });

  it("getDayOfMonth returns March 15th on March 15th", () => {
    const date = new Date("2024-03-15T09:00:00.000Z");

    expect(getDayOfMonth(date)).toBe("15th");
  });

  it("getDayOfMonth returns 2nd on June 2nd", () => {
    const date = new Date("2024-06-02T09:00:00.000Z");

    expect(getDayOfMonth(date)).toBe("2nd");
  });

  it("getDayOfYear returns January 12th on January 21th ", () => {
    const date = new Date("2024-01-12T09:00:00.000Z");

    expect(getDayOfYear(date)).toBe("January 12th");
  });
});
