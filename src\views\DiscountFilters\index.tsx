import React from "react";
import { convertPxToRem } from "@treez-inc/component-library";
import { styled, Box } from "@mui/material";
import { DISCOUNT_FILTER_FIELDS } from "../../constants/discountTable";
import DropdownSelect, {
  DropdownSelectOptionProps,
} from "../../components/DropdownSelect";

interface DiscountFiltersProps {
  setFilter: (field: string, value: string | boolean | string[]) => void;
  entitiesOptions: DropdownSelectOptionProps[];
}

const statusOptions: DropdownSelectOptionProps[] = [
  { key: "true", label: "Active", checked: true },
  { key: "false", label: "Inactive" },
];

const DiscountFiltersContainer = styled(Box)(() => ({
  display: "flex",
}));

const DiscountFilter = styled(Box)({
  marginRight: convertPxToRem(12),
});

const DiscountFilters: React.FC<DiscountFiltersProps> = ({
  setFilter,
  entitiesOptions,
}) => (
  <DiscountFiltersContainer>
    <DiscountFilter>
      <DropdownSelect
        testId="filterstores-dropdown"
        label="Store"
        data={entitiesOptions}
        allOption
        onChange={(ids) => {
          setFilter(DISCOUNT_FILTER_FIELDS.STORES, ids);
        }}
      />
    </DiscountFilter>

    <DiscountFilter>
      <DropdownSelect
        testId="filterstatus-dropdown"
        label="Status"
        data={statusOptions}
        allOption
        onChange={(ids) => {
          setFilter(DISCOUNT_FILTER_FIELDS.STATUS, ids);
        }}
      />
    </DiscountFilter>
  </DiscountFiltersContainer>
);

export default DiscountFilters;
