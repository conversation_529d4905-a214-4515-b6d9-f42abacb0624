import React, { ReactNode } from "react";
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom";
import { renderHook } from "@testing-library/react";
import { waitFor } from "@testing-library/dom";
import { HttpResponse, http } from "msw";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  convertDiscountResponseToAutomatedDiscountForm,
  useAutomatedDiscountsForm,
} from ".";
import ApiService from "../../../../../services/api/apiService";
import { tokens } from "../../../../../test/constants";
import { server } from "../../../../../test/mocks/node";
import {
  getOrgTagGroupUrl,
  getProductCollectionsUrl,
  upsertDiscountUrl,
} from "../../../../../services/apiEndPoints";
import { testAutomatedDiscountsResponse } from "../../../../../test/fixtures";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const wrapper = ({ children }: { children: ReactNode }) => (
  <QueryClientProvider client={queryClient}>
    <BrowserRouter>{children}</BrowserRouter>
  </QueryClientProvider>
);

const clearTokens = () => {};
const mockSetIsPageLoading = jest.fn();
const mockApiService = new ApiService(() => tokens, clearTokens);

const mockNavigate = jest.fn();

jest.mock("react-router-dom", () => ({
  ...(jest.requireActual("react-router-dom") as any),
  useNavigate: () => mockNavigate,
}));

const mockOpenSnackbar = jest.fn();
jest.mock("../../../../../providers/SnackbarProvider", () => ({
  useSnackbar: jest.fn(() => ({ openSnackbar: mockOpenSnackbar })),
}));

const initialSteps = [
  {
    label: "Discount Details",
    completed: false,
    disabled: false,
  },
  {
    label: "Select Product Collections",
    completed: false,
    disabled: true,
  },
  {
    label: "Select Stores",
    completed: false,
    disabled: true,
  },
  {
    label: "Schedule Discount",
    completed: false,
    disabled: true,
  },
  {
    label: "Set Conditions",
    completed: false,
    disabled: true,
  },
];

const singleStore = [
  {
    id: "single-store1",
    name: "Single Store 1",
    customerType: "retail",
    phoneNumber: "************",
    address: {},
    license: [{ key: "s-license1", value: "sl1" }],
  },
];

const multiStore = [
  {
    id: "multi-store1",
    name: "Multi Store 1",
    customerType: "retail",
    phoneNumber: "************",
    address: {},
    license: [{ key: "m-license1", value: "ml1" }],
  },
  {
    id: "multi-store2",
    name: "Multi Store 2",
    customerType: "retail",
    phoneNumber: "************",
    address: {},
    license: [{ key: "m-license2", value: "ml2" }],
  },
];

describe("useAutomatedDiscountsForm", () => {
  it("should initialize activeStep as 0 and render initial steps correctly", () => {
    const { result } = renderHook(
      () =>
        useAutomatedDiscountsForm(
          mockApiService,
          mockSetIsPageLoading,
          multiStore
        ),
      { wrapper }
    );

    expect(result.current.activeStep).toBe(0);
    expect(
      result.current.steps.map((step) => ({
        label: step.label,
        completed: step.completed,
        disabled: step.disabled,
      }))
    ).toEqual(initialSteps);
  });

  it("should render all initial steps when store count is more than 1 (multi stores)", () => {
    const { result } = renderHook(
      () =>
        useAutomatedDiscountsForm(
          mockApiService,
          mockSetIsPageLoading,
          multiStore
        ),
      { wrapper }
    );

    expect(result.current.activeStep).toBe(0);
    expect(
      result.current.steps.map((step) => ({
        label: step.label,
        completed: step.completed,
        disabled: step.disabled,
      }))
    ).toEqual(initialSteps);
  });

  it('should remove "Select Stores" step when store count is 1 (single store)', () => {
    const { result } = renderHook(
      () =>
        useAutomatedDiscountsForm(
          mockApiService,
          mockSetIsPageLoading,
          singleStore
        ),
      { wrapper }
    );

    expect(result.current.steps).not.toContainEqual(
      expect.objectContaining({ label: "Select Stores" })
    );
  });

  it('removes the "Select Stores" step when store count is 0 (no stores)', () => {
    const { result } = renderHook(
      () => useAutomatedDiscountsForm(mockApiService, mockSetIsPageLoading, []),
      { wrapper }
    );

    expect(result.current.steps).not.toContainEqual(
      expect.objectContaining({ label: "Select Stores" })
    );
  });

  it("should call useCreateDiscount and open a success snackbar when onSubmit is called", async () => {
    const { result } = renderHook(
      () =>
        useAutomatedDiscountsForm(
          mockApiService,
          mockSetIsPageLoading,
          multiStore
        ),
      { wrapper }
    );

    result.current.onSubmit(
      convertDiscountResponseToAutomatedDiscountForm(
        testAutomatedDiscountsResponse[0]
      )
    );

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith("/automated");
      expect(mockOpenSnackbar).toHaveBeenCalledWith({
        severity: "info",
        iconName: "Success",
        message: '"Test Discount 1" discount has been created',
      });
    });
  });

  it("should open an error snackbar when useCreateDiscount returns an error when onSubmit is called", async () => {
    server.use(
      http.post(
        upsertDiscountUrl,
        () => new HttpResponse(null, { status: 401 }),
        {
          once: true,
        }
      )
    );

    const { result } = renderHook(
      () =>
        useAutomatedDiscountsForm(
          mockApiService,
          mockSetIsPageLoading,
          multiStore
        ),
      { wrapper }
    );

    result.current.onSubmit(
      convertDiscountResponseToAutomatedDiscountForm(
        testAutomatedDiscountsResponse[0]
      )
    );

    await waitFor(() => {
      expect(mockOpenSnackbar).toHaveBeenCalledWith({
        severity: "error",
        message: "Unable to create the discount. Please try again",
      });
    });
  });

  it("should open an error snackbar when useCreateDiscount returns an AxiosError when onSubmit is called", async () => {
    jest.spyOn(console, "error").mockImplementation(jest.fn());

    server.use(
      http.post(upsertDiscountUrl, () => HttpResponse.error(), {
        once: true,
      })
    );

    const { result } = renderHook(
      () =>
        useAutomatedDiscountsForm(
          mockApiService,
          mockSetIsPageLoading,
          multiStore
        ),
      { wrapper }
    );

    result.current.onSubmit(
      convertDiscountResponseToAutomatedDiscountForm(
        testAutomatedDiscountsResponse[0]
      )
    );

    await waitFor(() => {
      expect(mockOpenSnackbar).toHaveBeenCalledWith({
        severity: "error",
        message: "Your request could not be completed due to a network error",
      });
    });
  });

  it.skip("should open an error snackbar when the submitted discount title already exists", async () => {
    server.use(
      // @ts-ignore
      http.post(upsertDiscountUrl, (req, res, ctx) => {
        const { title } = req.body;
        if (title === testAutomatedDiscountsResponse[0].title) {
          return res(
            ctx.status(422),
            ctx.json({ message: "Title Already Taken" })
          );
        }
        return res(ctx.status(200));
      })
    );
    jest.spyOn(console, "error").mockImplementation(jest.fn());

    const { result } = renderHook(
      () =>
        useAutomatedDiscountsForm(
          mockApiService,
          mockSetIsPageLoading,
          multiStore
        ),
      { wrapper }
    );

    result.current.onSubmit(
      convertDiscountResponseToAutomatedDiscountForm(
        testAutomatedDiscountsResponse[0]
      )
    );

    await waitFor(() => {
      expect(mockOpenSnackbar).toHaveBeenCalledWith({
        severity: "error",
        message: "Title Already Taken",
      });
    });
  });

  it("should trigger a snackbar and call useNavigate when product collections are not retrieved", async () => {
    server.use(
      http.get(
        getProductCollectionsUrl,
        () => new HttpResponse(null, { status: 404 }),
        {
          once: true,
        }
      )
    );

    renderHook(
      () =>
        useAutomatedDiscountsForm(
          mockApiService,
          mockSetIsPageLoading,
          multiStore
        ),
      { wrapper }
    );

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalled();
      expect(mockOpenSnackbar).toHaveBeenCalledWith({
        severity: "error",
        message: "Request failed with status code 404",
      });
    });
  });

  it("should trigger a snackbar and call useNavigate when product collections are not retrieved and display custom error message", async () => {
    server.use(
      http.get(
        getProductCollectionsUrl,
        () => new HttpResponse(null, { status: 403 }),
        {
          once: true,
        }
      )
    );

    renderHook(
      () =>
        useAutomatedDiscountsForm(
          mockApiService,
          mockSetIsPageLoading,
          multiStore
        ),
      { wrapper }
    );

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalled();
      expect(mockOpenSnackbar).toHaveBeenCalledWith({
        severity: "error",
        message: "Product Collections: User does not have permission",
      });
    });
  });

  it("should trigger a snackbar and call navigate when customer groups are not retrieved", async () => {
    server.use(
      http.get(
        getOrgTagGroupUrl,
        () => new HttpResponse(null, { status: 500 }),
        {
          once: true,
        }
      )
    );

    renderHook(
      () =>
        useAutomatedDiscountsForm(
          mockApiService,
          mockSetIsPageLoading,
          multiStore
        ),
      { wrapper }
    );

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalled();
      expect(mockOpenSnackbar).toHaveBeenCalledWith({
        severity: "error",
        message: "There was an error retrieving the customer groups",
      });
    });
  });

  describe("Edit Mode", () => {
    it("should initialize the form with prefilled values from the existing discount", async () => {
      const { result } = renderHook(
        () =>
          useAutomatedDiscountsForm(
            mockApiService,
            mockSetIsPageLoading,
            multiStore,
            "discount1"
          ),
        { wrapper }
      );

      const discountFormValues = convertDiscountResponseToAutomatedDiscountForm(
        testAutomatedDiscountsResponse[0]
      );

      await waitFor(() => {
        expect(result.current.existingDiscount).toBeDefined();
        const formFilledData = result.current.methods.getValues();
        expect(formFilledData).toEqual(discountFormValues);
      });
    });

    it("should display a loading spinner, trigger a snackbar and call navigate when a existing discount is not retrieved", async () => {
      renderHook(
        () =>
          useAutomatedDiscountsForm(
            mockApiService,
            mockSetIsPageLoading,
            multiStore,
            "discountIdThatDoesNotExist"
          ),
        { wrapper }
      );

      await waitFor(() => {
        expect(mockSetIsPageLoading).toHaveBeenCalledWith(true);
        expect(mockNavigate).toHaveBeenCalled();
        expect(mockOpenSnackbar).toHaveBeenCalledWith({
          severity: "error",
          message: "There was an error retrieving the discount details",
        });
      });
    });

    it("should call useUpdateDiscount and open a success snackbar when onSubmit is called", async () => {
      const { result } = renderHook(
        () =>
          useAutomatedDiscountsForm(
            mockApiService,
            mockSetIsPageLoading,
            multiStore,
            "discount1"
          ),
        { wrapper }
      );

      result.current.onSubmit(
        convertDiscountResponseToAutomatedDiscountForm(
          testAutomatedDiscountsResponse[0]
        )
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith("/automated");
        expect(mockOpenSnackbar).toHaveBeenCalledWith({
          severity: "info",
          iconName: "Success",
          message: '"Test Discount 1" discount has been updated',
        });
      });
    });

    it("should open an error snackbar when useUpdateDiscount returns an error when onSubmit is called", async () => {
      server.use(
        http.put(
          upsertDiscountUrl,
          () => new HttpResponse(null, { status: 404 }),
          {
            once: true,
          }
        )
      );

      const { result } = renderHook(
        () =>
          useAutomatedDiscountsForm(
            mockApiService,
            mockSetIsPageLoading,
            multiStore,
            "discount1"
          ),
        { wrapper }
      );

      result.current.onSubmit(
        convertDiscountResponseToAutomatedDiscountForm(
          testAutomatedDiscountsResponse[0]
        )
      );

      await waitFor(() => {
        expect(mockOpenSnackbar).toHaveBeenCalledWith({
          severity: "error",
          message: "Your request could not be completed due to a network error",
        });
      });
    });

    it("should set storeCustomizations isActive to null when onSubmit is called", async () => {
      const { result } = renderHook(
        () =>
          useAutomatedDiscountsForm(
            mockApiService,
            mockSetIsPageLoading,
            multiStore,
            "discount1"
          ),
        { wrapper }
      );

      result.current.onSubmit(
        convertDiscountResponseToAutomatedDiscountForm(
          testAutomatedDiscountsResponse[0]
        )
      );

      jest.spyOn(mockApiService, "put");

      await waitFor(() => {
        expect(mockApiService.put).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            storeCustomizations: expect.arrayContaining([
              expect.objectContaining({ isActive: null }),
            ]),
          })
        );
      });
    });
  });
});
