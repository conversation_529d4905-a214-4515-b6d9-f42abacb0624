/* eslint-disable react/no-array-index-key */
import React from "react";
import {
  convertPxToRem,
  Drawer,
  DrawerContent,
  Icon,
  Tooltip,
} from "@treez-inc/component-library";
import { Typography, List, ListItem, styled } from "@mui/material";
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  timelineItemClasses,
  TimelineSeparator,
} from "@mui/lab";
import moment from "moment";
import { DiscountLogDrawerStateProps } from "../../hooks/useDiscounts";
import { LIMITED_HISTORICAL_DATA_FOR_HISTORY_LOG_WARNING_MESSAGE } from "../../constants";

export interface DiscountLogDrawerProps {
  closeDrawer: () => void;
  drawerState: DiscountLogDrawerStateProps;
  testId?: string;
}

const LogMessageTypography = styled(Typography)(() => ({
  marginBottom: convertPxToRem(6),
  marginLeft: convertPxToRem(6),
}));

const DiscountLogDrawer: React.FC<DiscountLogDrawerProps> = ({
  closeDrawer,
  drawerState,
  testId,
}) => (
  <Drawer
    open={drawerState.open}
    onClose={() => closeDrawer()}
    primaryButtonProps={{
      testId: "discount-log-drawer-close-button",
      label: "Close",
      onClick: () => closeDrawer(),
    }}
    title={drawerState.title}
    testId={testId}
  >
    <DrawerContent divider>
      <Timeline
        sx={{
          [`& .${timelineItemClasses.root}:before`]: {
            flex: 0,
            padding: 0,
          },
        }}
      >
        <>
          {drawerState?.data?.discountLogs?.length === 0 &&
            "No logs found for this discount."}
          {drawerState?.data?.discountLogs.map((log, index, discountLogs) => (
            <TimelineItem
              key={`discount-log-${drawerState.data.discount.id}-${log.createdAt}-timeline-item`}
            >
              {index < discountLogs.length - 1 ? (
                <TimelineSeparator>
                  <TimelineDot />
                  <TimelineConnector />
                </TimelineSeparator>
              ) : (
                <TimelineSeparator>
                  <TimelineDot />
                </TimelineSeparator>
              )}

              <TimelineContent
                key={`discount-log-${drawerState.data.discount.id}-${log.createdAt}-timeline-content`}
              >
                <Typography color="textSecondary">
                  {moment(new Date(log.createdAt)).format(
                    "DD MMMM yyyy, hh:mm A"
                  )}{" "}
                  {log.username}
                </Typography>
                <List
                  key={`discount-log-${drawerState.data.discount.id}-${log.createdAt}-timeline-content-list`}
                >
                  {log.updates.map((update, updateIndex) => (
                    <ListItem
                      key={`discount-log-${drawerState.data.discount.id}-${log.createdAt}-timeline-content-list-${updateIndex}`}
                    >
                      {log.limitedData && (
                        <Tooltip
                          variant="multiRow"
                          title={
                            LIMITED_HISTORICAL_DATA_FOR_HISTORY_LOG_WARNING_MESSAGE
                          }
                        >
                          <Icon
                            fontSize="large"
                            iconName="Warning"
                            color="orange"
                          />
                        </Tooltip>
                      )}

                      <LogMessageTypography>{update}</LogMessageTypography>
                    </ListItem>
                  ))}
                </List>
              </TimelineContent>
            </TimelineItem>
          ))}
        </>
      </Timeline>
    </DrawerContent>
  </Drawer>
);

export default DiscountLogDrawer;
