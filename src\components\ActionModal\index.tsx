import React from "react";
import { Typography } from "@mui/material";
import { Modal } from "@treez-inc/component-library";
import { DiscountModalStateProps } from "../../hooks/useDiscounts";

export interface ActionModalProps {
  closeModal: () => void;
  modalState: DiscountModalStateProps;
  primaryLabel: string;
  secondaryLabel: string;
  testId?: string;
}

const ActionModal: React.FC<ActionModalProps> = ({
  closeModal,
  modalState,
  primaryLabel,
  secondaryLabel,
  testId,
}) => (
  <Modal
    testId={`${testId}-action-modal`}
    title={modalState.title || ""}
    content={<Typography variant="largeText">{modalState.content}</Typography>}
    open={modalState.open}
    onClose={() => closeModal()}
    primaryButton={{
      testId: "action-modal-primary-button",
      label: primaryLabel,
      onClick: () => {
        if (modalState.submit) {
          modalState.submit();
          closeModal();
        }
      },
    }}
    secondaryButton={{
      testId: "action-modal-secondary-button",
      label: secondaryLabel,
      onClick: () => closeModal(),
    }}
  />
);

export default ActionModal;
