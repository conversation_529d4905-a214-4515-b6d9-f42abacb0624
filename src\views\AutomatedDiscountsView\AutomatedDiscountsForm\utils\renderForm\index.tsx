import React from "react";
import DiscountDetailsStep from "../../components/DiscountDetailsStep";
import ProductCollectionsStep from "../../components/ProductCollectionsStep";
import SetConditionsStep from "../../components/SetConditionsStep";
import SelectStoreStep from "../../components/SelectStoreStep";
import ScheduleDiscount from "../../components/ScheduleDiscountStep";
import {
  OrgTags,
  ProductCollectionResponse,
} from "../../../../../interfaces/responseModels";
import { AUTOMATED_DISCOUNT_FORM_STEPS } from "../../../../../constants/discountForm";
import ApiService from "../../../../../services/api/apiService";

type StepOptions =
  | ApiService
  | ProductCollectionResponse[]
  | OrgTags[]
  | unknown;
export interface RenderFormProps {
  [key: string]: StepOptions;
}

export const renderForm = (step: string, props?: RenderFormProps) => {
  switch (step) {
    case AUTOMATED_DISCOUNT_FORM_STEPS.DISCOUNT_DETAILS:
      return <DiscountDetailsStep api={props?.api as ApiService} />;
    case AUTOMATED_DISCOUNT_FORM_STEPS.PRODUCT_COLLECTIONS:
      return (
        <ProductCollectionsStep {...(props as RenderFormProps | undefined)} />
      );
    case AUTOMATED_DISCOUNT_FORM_STEPS.STORES:
      return <SelectStoreStep {...(props as RenderFormProps | undefined)} />;
    case AUTOMATED_DISCOUNT_FORM_STEPS.SCHEDULE:
      return <ScheduleDiscount {...(props as RenderFormProps | undefined)} />;
    case AUTOMATED_DISCOUNT_FORM_STEPS.CONDITIONS:
      return <SetConditionsStep {...(props as RenderFormProps | undefined)} />;
    default:
      return <div data-testid="automated-review-step">Review</div>;
  }
};
