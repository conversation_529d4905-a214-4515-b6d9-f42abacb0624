import React, { useEffect, useState } from "react";
import { Control, UseFormGetValues, UseFormSetValue } from "react-hook-form";
import { Box, Typography, styled } from "@mui/material";
import { convertPxToRem } from "@treez-inc/component-library";
import DropdownSelect, {
  DropdownSelectOptionProps,
} from "../../../../../components/DropdownSelect";
import {
  manualDiscountConditionsDropdownSelect,
  purchaseAmountTypes,
} from "../../../../../constants/discountForm";
import { ManualDiscountFormData } from "../../../../../interfaces/discounts";
import PurchaseInput from "./PurchaseInput/PurchaseInput";
import MaxCountInput from "./MaxCountInput/MaxCountInput";
import AccordionPanel from "./AccordionPanel/AccordionPanel";

export interface ConditionValue {
  title: string;
  subtitle: string;
  children: React.ReactNode;
}

interface SetConditionsStepProps {
  control: Control<ManualDiscountFormData>;
  getValues: UseFormGetValues<ManualDiscountFormData>;
  setValue: UseFormSetValue<ManualDiscountFormData>;
}

const conditionsEnabledProps: { [key: string]: string } = {
  "per-customer-condition": "customerCapEnabled",
  "purchase-minimum-condition": "purchaseMinimumEnabled",
  "redemption-item-limit-condition": "itemLimitEnabled",
};

const StyledConditionsStepWrapper = styled(Box)(({ theme }) => ({
  border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
  borderRadius: convertPxToRem(16),
  padding: convertPxToRem(16),
  minWidth: convertPxToRem(280),
  maxWidth: convertPxToRem(620),
}));

const StyledSetConditionsContainer = styled(Box)({
  display: "flex",
});

const StyledColumnContent = styled(Box)({
  margin: convertPxToRem(16),
  marginTop: convertPxToRem(8),
});

const SetConditionsStep: React.FC<SetConditionsStepProps> = ({
  getValues,
  control,
  setValue,
}) => {
  const [selectedConditions, setSelectedConditions] = useState<string[]>([]);
  const [conditionsList, setConditionsList] = useState<
    DropdownSelectOptionProps[]
  >(manualDiscountConditionsDropdownSelect);


  const accordionData: Record<string, ConditionValue> = {
    "per-customer-condition": {
      title: "Per-Customer Limit",
      subtitle:
        "Restricts the number of times a discount can be applied per customer, managed at the store level",
      children: (
        <MaxCountInput
          label="Max Count"
          name="manualConditions.customerCapValue"
          control={control}
        />
      ),
    },
    "purchase-minimum-condition": {
      title: "Purchase Minimum Requirement",
      subtitle: "Purchase minimum is evaluated pre-discount. Subtotal is before tax; grand total includes tax.",
      children: (
        <PurchaseInput
          inputName="manualConditions.purchaseMinimumValue"
          selectName="manualConditions.purchaseMinimumType"
          control={control}
          options={purchaseAmountTypes}
        />
      ),
    },
    "redemption-item-limit-condition": {
      title: "Redemption Item Limit",
      subtitle:
        "Number of times the discount can be applied to a single transaction",
      children: (
        <MaxCountInput
          label="Max Count"
          name="manualConditions.itemLimitValue"
          control={control}
        />
      ),
    },
  };

  const onConditionSelected = (ids: string[]) => {
    setSelectedConditions(ids);

    Object.keys(conditionsEnabledProps).forEach((conditionId) => {
      const propName = conditionsEnabledProps[conditionId] as keyof ManualDiscountFormData["manualConditions"];

      if (propName && propName.endsWith("Enabled")) {
        setValue(`manualConditions.${propName}`, ids.includes(conditionId));
      }
    });
  };

  useEffect(() => {
    const values = getValues();
    const updatedSelectedConditions: string[] = [];

    Object.entries(conditionsEnabledProps).forEach(
      ([conditionId, propName]) => {
        if (
          propName &&
          propName.endsWith("Enabled") &&
          values.manualConditions[propName as keyof typeof values.manualConditions]
        ) {
          updatedSelectedConditions.push(conditionId);
        }
      }
    );

    const updatedConditionsList = conditionsList.map((condition) => ({
      ...condition,
      checked: updatedSelectedConditions.includes(condition.key),
    }));

    setConditionsList(updatedConditionsList);
    setSelectedConditions(updatedSelectedConditions);
  }, [getValues, conditionsEnabledProps]);

  return (
    <StyledConditionsStepWrapper data-testid="manual-set-conditions-step">
      <StyledSetConditionsContainer>
        <StyledColumnContent>
          <Typography variant="h6">Set Conditions</Typography>
          <Typography variant="mediumText" color="secondaryText">
            Apply conditions to determine when this discount becomes eligible
            for checkout at your organization stores. Tailor its availability
            based on specific criteria.
          </Typography>
        </StyledColumnContent>
        <StyledColumnContent>
          <DropdownSelect
            testId="conditions-list"
            label="Set conditions"
            data={conditionsList}
            onChange={onConditionSelected}
          />
        </StyledColumnContent>
      </StyledSetConditionsContainer>
      {selectedConditions.map((conditionId) => (
        <AccordionPanel
          testId={conditionId}
          key={conditionId}
          title={accordionData[conditionId].title}
          subtitle={accordionData[conditionId].subtitle}
          expanded
        >
          {accordionData[conditionId].children}
        </AccordionPanel>
      ))}
    </StyledConditionsStepWrapper>
  );
};

export default SetConditionsStep;
