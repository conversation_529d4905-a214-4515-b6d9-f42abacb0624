import React, { useEffect, useState } from "react";
import { Box, styled } from "@mui/material";
import { Outlet, useNavigate } from "react-router-dom";
import { convertPxToRem } from "@treez-inc/component-library";
import { useQueryClient } from "@tanstack/react-query";
import ManualDiscountTable from "./components/ManualDiscountTable";
import ActionModal from "../../components/ActionModal";
import LoadingSpinner from "../../components/LoadingSpinner";
import { DropdownSelectOptionProps } from "../../components/DropdownSelect";
import ApiService from "../../services/api/apiService";
import { useDiscounts, useDiscountsQuery, useGetEntities } from "../../hooks";
import { entityListUrl } from "../../services/apiEndPoints";
import { parseJwt } from "../../utils";
import PageHeader from "../../components/PageHeader";
import DiscountFilters from "../DiscountFilters";
import { KeyValueFilter } from "../../interfaces/table";
import DiscountLogDrawer from "../../components/DiscountLogDrawer";
import { DiscountLogDrawerStateProps } from "../../hooks/useDiscounts";
import { DISCOUNT_FILTER_FIELDS } from "../../constants/discountTable";

interface ManualDiscountsViewProps {
  api: ApiService;
  permissions: { read: boolean; write: boolean };
}

const ManualDiscountsWrapper = styled(Box)({
  overflow: "hidden",
  height: "100%",
  display: "flex",
  flexDirection: "column",
});

const TableWrapper = styled("div")(({ theme }) => ({
  height: "calc(100% - 9rem)",
  padding: `${convertPxToRem(40)} 0`,
  [theme.breakpoints.up("sm")]: {
    padding: `${convertPxToRem(40)} ${convertPxToRem(20)}`,
  },
  [theme.breakpoints.up("lg")]: {
    padding: `${convertPxToRem(40)} ${convertPxToRem(52)}`,
  },
  [theme.breakpoints.up("xl")]: {
    padding: `${convertPxToRem(40)} ${convertPxToRem(80)}`,
  },
}));

const ManualDiscountsView: React.FC<ManualDiscountsViewProps> = ({
  api,
  permissions,
}) => {
  const [filters, setFilters] = useState<KeyValueFilter>({
    [DISCOUNT_FILTER_FIELDS.STATUS]: ["true"],
  });

  const queryClient = useQueryClient();
  const { data: orgDiscounts, isLoading } = useDiscountsQuery(api, {
    isManual: true,
    filters,
  });

  const {
    closeModal,
    modalState,
    openDiscountModal,
    openDiscountLog,
    drawerState,
    closeDrawer,
  } = useDiscounts(api);

  const [entitiesOptions, setEntityOptions] = useState<DropdownSelectOptionProps[]>([]);
  const navigate = useNavigate();
  const decodedToken = parseJwt(api.getTokens().accessToken);

  const [entityListState] = useGetEntities(
    api,
    entityListUrl(decodedToken?.orgId)
  );

  const updateFilter = (field: string, value: string | boolean | string[]) => {
    setFilters((prevFilters) => ({ ...prevFilters, [field]: value }));
  };

  const handleAddButtonClick = () => {
    navigate(`add`);
  };

  useEffect(() => {
    if (entityListState.data) {
      setEntityOptions(
        entityListState.data.map((entity) => ({
          key: entity.id,
          label: entity.name,
        }))
      );
    }
  }, [entityListState]);

  return (
    <ManualDiscountsWrapper data-testid="manual-discount-table-wrapper">
      {(entityListState.loading ||
        isLoading ||
        queryClient.isMutating() > 0) && <LoadingSpinner />}
      <PageHeader
        testId="manual-discounts-page-header"
        buttonProps={{
          testId: "add-discount-button",
          label: "Add Discount",
          onClick: handleAddButtonClick,
          disabled: !permissions.write,
        }}
        filterOptions={
          <DiscountFilters
            entitiesOptions={entitiesOptions}
            setFilter={updateFilter}
          />
        }
      />
      <TableWrapper>
        <ManualDiscountTable
          discounts={orgDiscounts || []}
          openDiscountModal={openDiscountModal}
          openDiscountLog={openDiscountLog}
        />
      </TableWrapper>
      <Outlet />
      <ActionModal
        testId="manual-discounts"
        closeModal={closeModal}
        modalState={modalState}
        primaryLabel="Confirm"
        secondaryLabel="Cancel"
      />

      <DiscountLogDrawer
        testId="discounts-log-drawer"
        closeDrawer={closeDrawer}
        drawerState={drawerState as DiscountLogDrawerStateProps}
      />
    </ManualDiscountsWrapper>
  );
};

export default ManualDiscountsView;
