import { AutomatedDiscountMethods, ManualDiscountMethods } from "../constants/discounts";
import {
  OrgDiscountResponse,
  ProductCollectionResponse,
} from "../interfaces/responseModels";
import { OrgDiscountRow } from "../interfaces/table";

const testAutomatedDiscountsResponse: OrgDiscountResponse[] = [
  {
    id: "discount1",
    title: "Test Discount 1",
    displayTitle: "Test Discount 1",
    description: "Test Discount 1",
    method: AutomatedDiscountMethods.DOLLAR,
    amount: "100",
    isActive: true,
    isManual: false,
    isCart: false,
    isAdjustment: false,
    isStackable: false,
    customerGroups: [],
    conditions: {
      customerCapEnabled: false,
      customerCapValue: null,
      customerEventEnabled: false,
      customerEvents: null,
      customerGroupsEnabled: false,
      customerLicenseTypeEnabled: false,
      customerLicenseType: null,
      fulfillmentTypesEnabled: false,
      fulfillmentTypes: null,
      itemLimitEnabled: false,
      itemLimitValue: null,
      purchaseMinimumEnabled: false,
      purchaseMinimumType: null,
      purchaseMinimumValue: null,
      bogoConditions: null,
      bundleConditions: null,
      packageAgeEnabled: false,
      id: "conditionsofdiscount1",
      createdAt: "2020-01-01T00:00:00.000Z",
      updatedAt: "2020-01-01T00:00:00.000Z",
    },
    collections: Array.from({ length: 15 }, (_, i) => ({
      id: (i + 1).toString(),
      orgDiscountId: `test-org-${i + 1}`,
      productCollectionId: (i + 1).toString(),
      productCollectionName:
        i + 1 < 10 ? `Collection ${i + 1}` : `New Collection ${i + 1}`,
      createdAt: "2020-01-01T00:00:00.000Z",
      updatedAt: "2020-01-01T00:00:00.000Z",
      deletedAt: null,
    })),
    collectionsRequired: [],
    requireReason: false,
    requirePin: false,
    requireCoupon: false,
    showEcommerce: true,
    showCustomerFacing: true,
    showSellTreez: true,
    organizationId: "test-org-1",
    displayImageOnly: false,
    storeCustomizations: [
      {
        id: "disc1-store1",
        orgDiscountId: "test-org-1",
        entityId: "store1",
        entityName: "Store 1",
        amount: "100",
        isActive: true,
        isCart: false,
        isAdjustment: false,
        requireReason: false,
        requirePin: false,
        requireCoupon: false,
        createdAt: "2020-01-01T00:00:00.000Z",
        updatedAt: "2020-01-01T00:00:00.000Z",
      },
      {
        id: "disc1-store2",
        orgDiscountId: "test-org-1",
        entityId: "store2",
        entityName: "Store 2",
        amount: "100",
        isActive: true,
        isCart: false,
        isAdjustment: false,
        requireReason: false,
        requirePin: false,
        requireCoupon: false,
        createdAt: "2020-01-01T00:00:00.000Z",
        updatedAt: "2020-01-01T00:00:00.000Z",
      },
    ],
    coupons: [],
    schedule: null,
    createdAt: "2020-01-01T00:00:00.000Z",
    updatedAt: "2020-01-01T00:00:00.000Z",
  },
  {
    id: "discount2",
    title: "Test Discount 2",
    displayTitle: "Test Discount 2",
    description: "Test Discount 2",
    method: AutomatedDiscountMethods.PERCENT,
    amount: "200",
    isActive: false,
    isManual: false,
    isCart: false,
    isAdjustment: false,
    isStackable: false,
    customerGroups: [],
    conditions: {
      customerCapEnabled: false,
      customerCapValue: null,
      customerEventEnabled: false,
      customerEvents: null,
      customerLicenseTypeEnabled: false,
      customerGroupsEnabled: false,
      customerLicenseType: null,
      fulfillmentTypesEnabled: false,
      fulfillmentTypes: null,
      itemLimitEnabled: false,
      itemLimitValue: null,
      purchaseMinimumEnabled: false,
      purchaseMinimumType: null,
      purchaseMinimumValue: null,
      bogoConditions: null,
      bundleConditions: null,
      packageAgeEnabled: false,
      id: "conditionsofdiscount2",
      createdAt: "2020-01-01T00:00:00.000Z",
      updatedAt: "2020-01-01T00:00:00.000Z",
    },
    collections: [],
    collectionsRequired: [],
    requireReason: false,
    requirePin: false,
    requireCoupon: false,
    showEcommerce: true,
    showCustomerFacing: true,
    showSellTreez: true,
    organizationId: "test-org-2",
    displayImageOnly: false,
    storeCustomizations: [
      {
        id: "disc2-store1",
        orgDiscountId: "test-org-2",
        entityId: "store1",
        entityName: "Store 1",
        amount: "200",
        isActive: false,
        isCart: false,
        isAdjustment: false,
        requireReason: false,
        requirePin: false,
        requireCoupon: false,
        createdAt: "2020-01-01T00:00:00.000Z",
        updatedAt: "2020-01-01T00:00:00.000Z",
      },
    ],
    coupons: [],
    schedule: null,
    createdAt: "2020-01-01T00:00:00.000Z",
    updatedAt: "2020-01-01T00:00:00.000Z",
  },
];

const testAutomatedDiscountGetByIdResponse: OrgDiscountResponse = {
  id: "discount1",
  title: "Test Discount 1",
  displayTitle: "Test Discount 1",
  description: "Test Discount 1",
  method: AutomatedDiscountMethods.DOLLAR,
  amount: "100",
  isActive: true,
  isManual: false,
  isCart: false,
  isAdjustment: false,
  isStackable: false,
  requireReason: false,
  requirePin: false,
  requireCoupon: false,
  showEcommerce: true,
  showCustomerFacing: true,
  showSellTreez: true,
  organizationId: "test-org-1",
  displayImageOnly: false,
  storeCustomizations: [
    {
      id: "disc1-store1",
      orgDiscountId: "test-org-1",
      entityId: "store1",
      entityName: "Store 1",
      amount: "100",
      isActive: true,
      isCart: false,
      isAdjustment: false,
      requireReason: false,
      requirePin: false,
      requireCoupon: false,
      createdAt: "2020-01-01T00:00:00.000Z",
      updatedAt: "2020-01-01T00:00:00.000Z",
    },
    {
      id: "disc1-store2",
      orgDiscountId: "test-org-1",
      entityId: "store2",
      entityName: "Store 2",
      amount: "100",
      isActive: true,
      isCart: false,
      isAdjustment: false,
      requireReason: false,
      requirePin: false,
      requireCoupon: false,
      createdAt: "2020-01-01T00:00:00.000Z",
      updatedAt: "2020-01-01T00:00:00.000Z",
    },
  ],
  customerGroups: [],
  conditions: {
    customerCapEnabled: false,
    customerCapValue: null,
    customerEventEnabled: false,
    customerEvents: null,
    customerGroupsEnabled: false,
    customerLicenseTypeEnabled: false,
    customerLicenseType: null,
    fulfillmentTypesEnabled: false,
    fulfillmentTypes: null,
    itemLimitEnabled: false,
    itemLimitValue: null,
    purchaseMinimumEnabled: false,
    purchaseMinimumType: null,
    purchaseMinimumValue: null,
    bogoConditions: null,
    bundleConditions: null,
    packageAgeEnabled: false,
    id: "conditionsofdiscount2",
    createdAt: "2020-01-01T00:00:00.000Z",
    updatedAt: "2020-01-01T00:00:00.000Z",
  },
  collections: [],
  collectionsRequired: [],
  coupons: [],
  schedule: null,
  createdAt: "2020-01-01T00:00:00.000Z",
  updatedAt: "2020-01-01T00:00:00.000Z",
};

const testActiveAutomatedDiscountRow: OrgDiscountRow = {
  id: "1",
  parentId: undefined,
  entityId: undefined,
  entityName: undefined,
  title: "Test Active Automated Discount Row",
  displayTitle: "Test ActiveAutomated Discount Row",
  description: "Test ActiveAutomated Discount Row",
  amount: "100",
  method: AutomatedDiscountMethods.DOLLAR,
  isActive: true,
  isChild: false,
  isManual: false,
  isStackable: false,
  isCart: false,
  isAdjustment: false,
  requireReason: false,
  requirePin: false,
  requireCoupon: false,
  showEcommerce: true,
  showCustomerFacing: true,
  showSellTreez: true,
  displayImageOnly: false,
  customerGroups: [],
  storeCustomizations: [],
  coupons: [],
  schedule: null,
  organizationId: "orgId-1",
  createdAt: new Date(),
  updatedAt: new Date(),
  hierarchy: [""],
};

const testInactiveAutomatedDiscountRow: OrgDiscountRow = {
  id: "1",
  parentId: undefined,
  entityId: undefined,
  entityName: undefined,
  title: "Test Inactive Automated Discount Row",
  displayTitle: "Test Inactive Automated Discount Row",
  description: "Test Inactive Automated Discount Row",
  amount: "100",
  method: AutomatedDiscountMethods.DOLLAR,
  isActive: false,
  isChild: false,
  isManual: false,
  isStackable: false,
  isCart: false,
  isAdjustment: false,
  requireReason: false,
  requirePin: false,
  requireCoupon: false,
  showEcommerce: true,
  showCustomerFacing: true,
  showSellTreez: true,
  displayImageOnly: false,
  organizationId: "orgId-1",
  customerGroups: [],
  storeCustomizations: [],
  coupons: [],
  schedule: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  hierarchy: [""],
};

const testAutomatedDiscountChildRow: OrgDiscountRow = {
  id: "1",
  parentId: "230",
  entityId: undefined,
  entityName: undefined,
  title: "Test Automated Discount Child Row",
  displayTitle: "Test Automated Discount Child Row",
  description: "Test Automated Discount Child Row",
  amount: "100",
  method: AutomatedDiscountMethods.DOLLAR,
  isActive: true,
  isChild: false,
  isManual: false,
  isStackable: false,
  isCart: false,
  isAdjustment: false,
  requireReason: false,
  requirePin: false,
  requireCoupon: false,
  showEcommerce: true,
  showCustomerFacing: true,
  showSellTreez: true,
  displayImageOnly: false,
  customerGroups: [],
  storeCustomizations: [],
  coupons: [],
  schedule: null,
  organizationId: "orgId-1",
  createdAt: new Date(),
  updatedAt: new Date(),
  hierarchy: [""],
};

const testProductCollections: ProductCollectionResponse[] = [
  ...new Array(2),
].map((value, index) => ({
  id: `${index}`,
  name: `Collection ${index}`,
  createdAt: "2020-01-01T00:00:00.000Z",
  updatedAt: "2020-01-01T00:00:00.000Z",
  deletedAt: null,
  sync: false,
}));

export {
  testAutomatedDiscountsResponse,
  testAutomatedDiscountGetByIdResponse,
  testActiveAutomatedDiscountRow,
  testInactiveAutomatedDiscountRow,
  testAutomatedDiscountChildRow,
  testProductCollections,
};

const testManualDiscountsResponse: OrgDiscountResponse[] = [
  {
    id: "10",
    title: "Active Manual Discount",
    displayTitle: "Active Manual Discount",
    description: "Active Manual Discount",
    amount: "100",
    method: ManualDiscountMethods.DOLLAR,
    isActive: true,
    isStackable: false,
    isManual: true,
    isCart: true,
    isAdjustment: true,
    requireReason: true,
    requirePin: true,
    requireCoupon: true,
    showEcommerce: false,
    showCustomerFacing: false,
    showSellTreez: false,
    displayImageOnly: false,
    organizationId: "org123",
    conditions: null,
    customerGroups: [],
    collections: [],
    collectionsRequired: [],
    storeCustomizations: [],
    coupons: [],
    schedule: null,
    createdAt: "2024-01-23T21:43:52.421Z",
    updatedAt: "2024-01-23T21:43:52.421Z",
  },
  {
    id: "20",
    title: "Active Manual Discount",
    displayTitle: "Active Manual Discount",
    description: "Active Manual Discount",
    amount: "100",
    method: ManualDiscountMethods.DOLLAR,
    isActive: true,
    isStackable: false,
    isManual: true,
    isCart: true,
    isAdjustment: true,
    requireReason: true,
    requirePin: true,
    requireCoupon: true,
    showEcommerce: false,
    showCustomerFacing: false,
    showSellTreez: false,
    displayImageOnly: false,
    organizationId: "org456",
    conditions: null,
    customerGroups: [],
    collections: [],
    collectionsRequired: [],
    storeCustomizations: [],
    coupons: [],
    schedule: null,
    createdAt: "2024-01-23T21:43:52.421Z",
    updatedAt: "2024-01-23T21:43:52.421Z",
  },
];

const activeManualDiscount: OrgDiscountRow = {
  id: "100",
  isActive: true,
  isChild: false,
  title: "Some Title",
  displayTitle: "Some Display Title",
  description: "Some Display Title",
  amount: "100",
  method: ManualDiscountMethods.DOLLAR,
  isStackable: null,
  isManual: true,
  isCart: true,
  isAdjustment: true,
  requireReason: true,
  requirePin: true,
  requireCoupon: true,
  showEcommerce: false,
  showCustomerFacing: false,
  showSellTreez: false,
  displayImageOnly: false,
  organizationId: "org123",
  createdAt: new Date(),
  updatedAt: new Date(),
  customerGroups: [],
  hierarchy: ["someHierarchy"],
  coupons: [],
  schedule: null,
  storeCustomizations: [],
};

const activeManualDiscountChild: OrgDiscountRow = {
  id: "123",
  parentId: "100",
  isActive: true,
  isChild: true,
  title: "Some Title",
  displayTitle: "Some Display Title",
  description: "Some Display Title",
  amount: "100",
  method: ManualDiscountMethods.DOLLAR,
  customerGroups: [],
  isStackable: null,
  isManual: true,
  isCart: true,
  isAdjustment: true,
  requireReason: true,
  requirePin: true,
  requireCoupon: true,
  showEcommerce: false,
  showCustomerFacing: false,
  showSellTreez: false,
  displayImageOnly: false,
  organizationId: "org123",
  createdAt: new Date(),
  updatedAt: new Date(),
  hierarchy: ["someHierarchy"],
  coupons: [],
  schedule: null,
  storeCustomizations: [],
};

const inactiveManualDiscount: OrgDiscountRow = {
  id: "20",
  isActive: false,
  isChild: false,
  title: "Inactive Manual Discount",
  displayTitle: "Inactive Manual Discount",
  description: "Inactive Manual Discount",
  amount: "100",
  method: ManualDiscountMethods.DOLLAR,
  customerGroups: [],
  isStackable: null,
  isManual: true,
  isCart: true,
  isAdjustment: true,
  requireReason: true,
  requirePin: true,
  requireCoupon: true,
  showEcommerce: false,
  showCustomerFacing: false,
  showSellTreez: false,
  displayImageOnly: false,
  organizationId: "org456",
  createdAt: new Date(),
  updatedAt: new Date(),
  hierarchy: ["someHierarchy"],
  coupons: [],
  schedule: null,
  storeCustomizations: [],
};

const inactiveManualDiscountChild: OrgDiscountRow = {
  id: "21",
  isActive: false,
  isChild: true,
  title: "Inactive Manual Discount Child",
  displayTitle: "Inactive Manual Discount Child",
  description: "Inactive Manual Discount Child",
  amount: "100",
  method: ManualDiscountMethods.DOLLAR,
  isStackable: null,
  isManual: true,
  isCart: true,
  isAdjustment: true,
  requireReason: true,
  requirePin: true,
  requireCoupon: true,
  showEcommerce: false,
  showCustomerFacing: false,
  showSellTreez: false,
  displayImageOnly: false,
  organizationId: "org456",
  customerGroups: [],
  createdAt: new Date(),
  updatedAt: new Date(),
  hierarchy: ["someHierarchy"],
  coupons: [],
  schedule: null,
  storeCustomizations: [],
};

export {
  testManualDiscountsResponse,
  activeManualDiscount,
  activeManualDiscountChild,
  inactiveManualDiscount,
  inactiveManualDiscountChild,
};
