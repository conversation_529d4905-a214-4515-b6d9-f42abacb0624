import React from "react";
import { fireEvent, render, renderHook, screen } from "@testing-library/react";
import { FieldValues, FormProvider, useForm } from "react-hook-form";
import {
  AutocompleteProps,
  TreezThemeProvider,
} from "@treez-inc/component-library";
import AutocompleteInput from ".";

describe("AutocompleteInput", () => {
  const options = [
    { label: "Option 1", value: "option1" },
    { label: "Option 2", value: "option2" },
    { label: "Option 3", value: "option3" },
  ];

  const renderAutocompleteInput = (
    invalid = false,
    testId = "test-options",
    props?: Partial<AutocompleteProps>
  ) => {
    const { result } = renderHook(() =>
      useForm({ defaultValues: { testAutocompleteInput: [] } as FieldValues })
    );

    const FormProviderWrapper: React.FC<{ children: React.ReactNode }> = ({
      children,
    }) => <FormProvider {...result.current}>{children}</FormProvider>;

    if (invalid) {
      result.current.setError("testAutocompleteInput", {
        type: "required",
        message: "An option is required",
      });
    }

    render(
      <TreezThemeProvider>
        <FormProviderWrapper>
          <AutocompleteInput
            testId={testId}
            name="testAutocompleteInput"
            control={result.current.control}
            label="Test Autocomplete Input"
            options={options}
            {...props}
          />
        </FormProviderWrapper>
      </TreezThemeProvider>
    );

    const autocompleteInput = screen.getByTestId(
      `${testId}-autocomplete-input`
    );
    return { autocompleteInput, result };
  };

  it("should render", () => {
    const { autocompleteInput } = renderAutocompleteInput();
    expect(autocompleteInput).toBeInTheDocument();
  });

  it("should render testId when testId prop is passed", () => {
    const { autocompleteInput } = renderAutocompleteInput(
      false,
      "custom-test-id"
    );
    expect(autocompleteInput).toHaveAttribute(
      "data-testid",
      "custom-test-id-autocomplete-input"
    );
  });

  it("should render the correct option when a selection is made from the dropdown", () => {
    const { autocompleteInput, result } = renderAutocompleteInput();

    fireEvent.focus(autocompleteInput);

    const triggerButton = autocompleteInput.getElementsByTagName("button")[0];
    fireEvent.click(triggerButton);

    const option = screen.getByText("Option 1");
    fireEvent.click(option);

    const selectedValues = result.current.getValues("testAutocompleteInput");
    expect(selectedValues).toContainEqual({
      label: "Option 1",
      value: "option1",
    });
  });

  it("should be able to type a valid option and then select it from the dropdown", () => {
    const { autocompleteInput, result } = renderAutocompleteInput();

    const inputElement = autocompleteInput.querySelector("input");

    if (inputElement) {
      fireEvent.change(inputElement, {
        target: { value: "Option 1" },
      });

      const option = screen.getByText("Option 1");
      fireEvent.click(option);

      const selectedValues = result.current.getValues("testAutocompleteInput");
      expect(selectedValues).toContainEqual({
        label: "Option 1",
        value: "option1",
      });
    }
  });

  it("should display an error message when input is invalid", () => {
    renderAutocompleteInput(true);
    expect(screen.getByText("An option is required")).toBeInTheDocument();
  });

  it("should be able to compare using custom `isOptionEqualToValue` function", () => {
    const { autocompleteInput, result } = renderAutocompleteInput(
      false,
      "custom-test-id",
      { isOptionEqualToValue: (option, value) => option.value === value }
    );

    fireEvent.focus(autocompleteInput);

    const triggerButton = autocompleteInput.getElementsByTagName("button")[0];
    fireEvent.click(triggerButton);

    const option = screen.getByText("Option 1");
    fireEvent.click(option);

    const selectedValues = result.current.getValues("testAutocompleteInput");
    expect(selectedValues).toContainEqual({
      label: "Option 1",
      value: "option1",
    });
  });
});
