import { FetchStatus } from "@tanstack/react-query";

export const IDLE_FETCH_STATUS: FetchStatus = "idle";

export const PERMISSIONS_MESSAGES = {
  NO_EDIT: "You do not have the permissions to save changes to discounts",
  NO_CREATE: "You do not have the permissions to create new discounts",
};

export enum Permissions {
  READ_DISCOUNTS = "discountmanagement::readOrgDiscount",
  WRITE_DISCOUNTS = "discountmanagement::writeOrgDiscount",
}

// eslint-disable-next-line prefer-destructuring
export const STAGE = process.env.STAGE;

export const IMAGE_CDN_URL = `https://cdn.${
  STAGE === "prod" ? "mso" : STAGE
}.treez.io`;
export const LIMITED_HISTORICAL_DATA_FOR_HISTORY_LOG_WARNING_MESSAGE = "Limited historical data is available for this discount. The creation and initial edit will appear as general actions, but all subsequent changes will log full details."
