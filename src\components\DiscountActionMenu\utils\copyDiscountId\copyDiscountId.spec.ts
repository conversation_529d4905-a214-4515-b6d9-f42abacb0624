import copyDiscountId from ".";

const mockWriteText = jest.fn();

Object.defineProperty(global.navigator, "clipboard", {
  value: { writeText: mockWriteText },
});

const mockOpenSnackbar = jest.fn();

describe("copyDiscountId function", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should copy the discount ID to the clipboard and show a success snackbar", async () => {
    const mockEvent = { preventDefault: jest.fn() };

    await copyDiscountId("test-id-123", mockOpenSnackbar)(mockEvent as any);

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith("test-id-123");
    expect(mockOpenSnackbar).toHaveBeenCalledWith({
      message: "Discount ID was copied to clipboard",
    });
  });

  it("should show an error snackbar if unable to copy", async () => {
    // @ts-ignore
    global.navigator.clipboard.writeText.mockImplementationOnce(() => {
      throw new Error("Copy Failed");
    });

    const mockEvent = { preventDefault: jest.fn() };

    await copyDiscountId("test-id-123", mockOpenSnackbar)(mockEvent as any);

    expect(mockOpenSnackbar).toHaveBeenCalledWith({
      message: "Unable to copy Discount ID at the moment",
      severity: "error",
    });
  });
});
