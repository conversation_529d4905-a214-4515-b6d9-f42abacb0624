import { BogoDiscountMethods } from "../../constants/discounts";
import {
  AUTOMATED_DISCOUNT_METHODS,
  MANUAL_DISCOUNT_METHODS,
} from "../../constants/discountForm";
import { Coupon } from "../../interfaces/coupon";
import { FulfillmentTypesMap, ManualDiscountFormData } from "../../interfaces/discounts";

export const validateDiscountAmountInput =
  (amountType: string) => (value: string) => {
    const percent =
      amountType === MANUAL_DISCOUNT_METHODS.PERCENT ||
      amountType === AUTOMATED_DISCOUNT_METHODS.PERCENT ||
      amountType === BogoDiscountMethods.PERCENT;

    const numericValue = parseFloat(value);

    if (Number.isNaN(numericValue) || numericValue <= 0) {
      return "Please enter a number greater than 0";
    }

    if (percent && numericValue > 100) {
      return "Please enter a number smaller than or equal to 100%";
    }

    const decimalPlaces = 2;

    const decimalRegex = new RegExp(
      `^(\\d+\\.?|\\.)(\\d{1,${decimalPlaces}})?$`
    );

    if (!decimalRegex.test(value as string)) {
      return `Please enter a maximum of ${decimalPlaces} decimal places`;
    }

    return true;
  };

export const validateCouponCode =
  (coupons: Coupon[], isEditing: boolean = false) =>
  (value: string | undefined) => {
    if (!value) return true;

    if (/\s/.test(value)) {
      return "Spaces are not allowed";
    }

    if (value.length < 3 || value.length > 255) {
      return "Length must be between 3 and 255 characters";
    }

    if (!/^[A-Za-z0-9-._~:/\\/]+$/.test(value)) {
      return "Invalid characters";
    }

    const currentCoupons =
      (coupons && coupons.filter((coupon) => coupon.code === value)) || [];

    if (
      (isEditing && currentCoupons.length > 1) ||
      (!isEditing && currentCoupons.length > 0)
    ) {
      return "Coupons should not be duplicated";
    }

    return true;
  };

export const validateRequireReasonDiscountType = (
  checked: boolean,
  { requireCoupon, couponFormModel: { coupons } }: ManualDiscountFormData
) => {
  if ((coupons.length > 0 && checked) || (requireCoupon && checked)) {
    return "Require Reason cannot be used with coupons";
  }

  return true;
};

export const validateFulfillmentTypes = (fulfillmentTypes: FulfillmentTypesMap) => {  
  const fulfillmentValues = Object.values(fulfillmentTypes || {});

  if (fulfillmentValues.find(checked => checked === true)) {
    return true;
  } 
  
  return "Select at least one fulfillment type"
}