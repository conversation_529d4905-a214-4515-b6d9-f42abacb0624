import React from "react";
import { render, screen } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderForm, RenderFormProps } from ".";
import {
  AUTOMATED_DISCOUNT_FORM_STEPS,
  defaultAutomatedDiscountFormValues as defaultValues,
} from "../../../../../constants/discountForm";

describe("renderForm()", () => {
  const FormComponent = ({
    step,
    props,
  }: {
    step: string;
    props?: RenderFormProps;
  }) => {
    const methods = useForm({ defaultValues });
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    return (
      <TreezThemeProvider>
        <QueryClientProvider client={queryClient}>
          <FormProvider {...methods}>{renderForm(step, props)}</FormProvider>
        </QueryClientProvider>
      </TreezThemeProvider>
    );
  };

  it("should render the DiscountDetailsStep for the first step", () => {
    render(
      <FormComponent
        step={AUTOMATED_DISCOUNT_FORM_STEPS.DISCOUNT_DETAILS}
        props={{ api: { getTokens: jest.fn() } }}
      />
    );
    expect(
      screen.getByTestId("automated-discounts-details-step")
    ).toBeInTheDocument();
  });

  it("should render the Select Product Collections Step for the second step", () => {
    render(
      <FormComponent step={AUTOMATED_DISCOUNT_FORM_STEPS.PRODUCT_COLLECTIONS} />
    );
    expect(
      screen.getByTestId("automated-product-collections-step")
    ).toBeInTheDocument();
  });

  it("should render the Select Stores Step for the third step", () => {
    render(<FormComponent step={AUTOMATED_DISCOUNT_FORM_STEPS.STORES} />);
    expect(
      screen.getByTestId("automated-select-store-step")
    ).toBeInTheDocument();
  });

  it("should render the Schedule Discount Step for the fourth step", () => {
    render(<FormComponent step={AUTOMATED_DISCOUNT_FORM_STEPS.SCHEDULE} />);
    expect(
      screen.getByTestId("automated-schedule-discount-step")
    ).toBeInTheDocument();
  });

  it("should render the Set Conditions Step for the fifth step", () => {
    render(<FormComponent step={AUTOMATED_DISCOUNT_FORM_STEPS.CONDITIONS} />);
    expect(
      screen.getByTestId("automated-set-conditions-step")
    ).toBeInTheDocument();
  });

  it("should render the Review Step as the default step", () => {
    render(<FormComponent step="" />);
    expect(screen.getByTestId("automated-review-step")).toBeInTheDocument();
  });
});
