import React from "react";
import { fireEvent, render, renderHook, screen } from "@testing-library/react";
import { FieldValues, FormProvider, useForm } from "react-hook-form";
import { TreezThemeProvider } from "@treez-inc/component-library";
import MaxCountInput from ".";

describe("MaxCountInput", () => {
  const label = "Max Count";

  const renderMaxCountInput = (
    invalid = false,
    testId = "test-value",
    customLabel = label
  ) => {
    const { result } = renderHook(() =>
      useForm({ defaultValues: { testMaxCountInput: "" } as FieldValues })
    );

    const FormProviderWrapper: React.FC<{ children: React.ReactNode }> = ({
      children,
    }) => <FormProvider {...result.current}>{children}</FormProvider>;

    if (invalid) {
      result.current.setError("testMaxCountInput", {
        type: "required",
        message: "Max count is required",
      });
    }

    render(
      <TreezThemeProvider>
        <FormProviderWrapper>
          <MaxCountInput
            name="testMaxCountInput"
            control={result.current.control}
            testId={testId}
            label={customLabel}
          />
        </FormProviderWrapper>
      </TreezThemeProvider>
    );

    const { getByTestId } = screen;

    const maxCountInput = getByTestId(`${testId}-max-count-input`);
    return { maxCountInput };
  };

  it("should render", () => {
    const { maxCountInput } = renderMaxCountInput();
    expect(maxCountInput).toBeInTheDocument();
  });

  it("should display input as required", async () => {
    const { maxCountInput } = renderMaxCountInput();
    expect(maxCountInput.getElementsByTagName("input")[0]).toBeRequired();
  });

  it("should display an error message when input is invalid", () => {
    renderMaxCountInput(true);
    expect(screen.getByText("Max count is required")).toBeInTheDocument();
  });

  it("should not display an error message when the input is empty", () => {
    const { maxCountInput } = renderMaxCountInput();
    const input = maxCountInput.querySelector("input");
    if (input) {
      fireEvent.blur(input);
    }
    expect(screen.queryByText("Max count is required")).not.toBeInTheDocument();
  });

  it("should render with the correct label", () => {
    const { maxCountInput } = renderMaxCountInput(false, "custom-id", label);
    const inputLabel = screen.getByText(label);
    expect(maxCountInput).toBeInTheDocument();
    expect(inputLabel).toBeInTheDocument();
  });

  it("should render testId when testId prop is passed", () => {
    const customTestId = "custom-id";
    const { maxCountInput } = renderMaxCountInput(false, customTestId, label);
    expect(maxCountInput).toBeInTheDocument();
    expect(maxCountInput).toHaveAttribute(
      "data-testid",
      `${customTestId}-max-count-input`
    );
  });
});
