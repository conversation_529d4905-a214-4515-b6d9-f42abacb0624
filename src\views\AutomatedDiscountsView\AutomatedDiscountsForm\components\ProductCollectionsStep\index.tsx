import React, { useEffect } from "react";
import { Box, Grid, Typography, styled } from "@mui/material";
import {
  Input,
  Modal,
  Panel,
  Select,
  convertPxToRem,
  Checkbox,
  Tooltip,
} from "@treez-inc/component-library";
import { Controller, useFormContext } from "react-hook-form";
import {
  AUTOMATED_DISCOUNT_METHODS,
  BOGO_DISCOUNT_METHODS,
  BUNDLE_DISCOUNT_METHODS,
  BUNDLE_PURCHASE_REQUIREMENTS,
} from "../../../../../constants/discountForm";
import { validateDiscountAmountInput } from "../../../../../utils/validations";
import ProductCollectionsSelectionCard from "./ProductCollectionsSelectionCard";
import { BundleDiscountMethods } from "../../../../../constants/discounts";

const StyledProductCollectionsStep = styled(Box)(() => ({
  minWidth: convertPxToRem(280),
  maxWidth: convertPxToRem(620),
  height: "100%",
  display: "flex",
  flexDirection: "column",
  gap: convertPxToRem(24),
  overflowY: "auto",
}));

const ProductCollectionsStep = () => {
  const {
    control,
    getValues,
    watch,
    trigger,
    clearErrors,
    setValue,
    formState: { errors },
  } = useFormContext();

  const isBogo = getValues("method") === AUTOMATED_DISCOUNT_METHODS.BOGO;
  const isBundle = getValues("method") === AUTOMATED_DISCOUNT_METHODS.BUNDLE;

  const discountUnit =
    (isBogo && watch("conditions.bogoConditions.discountUnit")) ||
    (isBundle && watch("conditions.bundleConditions.discountUnit"));

  const getSelectProductsSubtitle = () => {
    if (isBogo)
      return "Choose the product collections on which the BOGO discount will be applied. These can be the same as the products that activate the BOGO deal.";
    if (isBundle)
      return "Choose the product collections that trigger the bundle discount when the purchase requirement is met.";
    return "Choose which product collections will be affected by the discount.";
  };

  useEffect(() => {
    if ((isBogo || isBundle) && getValues().amount) {
      trigger("amount");
    }

    if (isBundle && discountUnit !== BundleDiscountMethods.PERCENT) {
      setValue("conditions.bundleConditions.threshold", false);
    }
  }, [discountUnit]);

  return (
    <StyledProductCollectionsStep data-testid="automated-product-collections-step">
      {isBogo && (
        <>
          <Panel
            testId="bogo-buy-count-panel"
            title="Set Customer Buy Count"
            subtitle="Specify how many items the customer must purchase to qualify for
        the discount."
          >
            <Controller
              name="conditions.bogoConditions.buyCount"
              control={control}
              rules={{
                required: { value: true, message: "Buy count is required" },
                min: {
                  value: 1,
                  message: "Please enter a number greater than 0",
                },
                pattern: {
                  value: /^[0-9]*$/,
                  message: "Please enter a number greater than 0",
                },
              }}
              render={({
                field: { ref, value, ...rest },
                fieldState: { error },
              }) => (
                <Input
                  {...rest}
                  value={value || ""}
                  label="Customer Buy Count"
                  testId="bogo-buycount-input"
                  helperText={error?.message || ""}
                  error={!!error}
                  required
                  type="number"
                />
              )}
            />
          </Panel>
          <Panel>
            <ProductCollectionsSelectionCard
              testId="automated-product-collections-bogo-card"
              title="Select Products to Activate the Discount"
              subtitle="Choose the product collections that trigger the BOGO discount when purchased."
              errorMessage="No products selected. Add a Product Collection to select which products trigger the BOGO discount."
              formName="collectionsRequired"
            />
          </Panel>
          <Panel
            testId="bogo-method-amount-count-panel"
            title="Discount Method and Items to Get"
            subtitle="Choose the discount method (percentage or flat amount) and select the items customers will receive as part of the BOGO deal."
          >
            <Grid container spacing={convertPxToRem(12)}>
              <Grid item xs={6}>
                <Controller
                  name="conditions.bogoConditions.discountUnit"
                  control={control}
                  rules={{ required: "Discount method is required" }}
                  render={({ field, fieldState: { error } }) => {
                    //  Treez Select component doesn't accept a ref prop
                    const { ref, value, ...rest } = field;
                    return (
                      <Select
                        {...rest}
                        value={value || ""}
                        testId="discount-method-select"
                        label="Discount method"
                        menuItems={Object.values(BOGO_DISCOUNT_METHODS)}
                        helperText={error?.message ? error.message : ""}
                        error={!!error}
                        required
                      />
                    );
                  }}
                />
              </Grid>
              <Grid item xs={6}>
                <Controller
                  name="amount"
                  control={control}
                  rules={{
                    required: { value: true, message: "Amount is required" },
                    validate: validateDiscountAmountInput(discountUnit),
                  }}
                  render={({
                    field: { ref, value, ...rest },
                    fieldState: { error },
                  }) => (
                    <Input
                      {...rest}
                      value={value || ""}
                      label="Amount"
                      testId="bogo-amount-input"
                      helperText={error?.message || ""}
                      error={!!error}
                      required
                      type="number"
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  name="conditions.bogoConditions.getCount"
                  control={control}
                  rules={{
                    required: { value: true, message: "Count is required" },
                    min: {
                      value: 1,
                      message: "Please enter a number greater than 0",
                    },
                    pattern: {
                      value: /^[0-9]*$/,
                      message: "Please enter a number greater than 0",
                    },
                  }}
                  render={({
                    field: { ref, value, ...rest },
                    fieldState: { error },
                  }) => (
                    <Input
                      {...rest}
                      value={value || ""}
                      label="Discounted Items Count"
                      testId="bogo-amount-input"
                      helperText={
                        error?.message ||
                        "Number of products that will be affected by the discount"
                      }
                      error={!!error}
                      required
                      type="number"
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Panel>
        </>
      )}
      {isBundle && (
        <Panel
          testId="bundle-method-amount-count-panel"
          title="Discount Method and Purchase Requirement"
          subtitle="Choose the discount method and the purchase requirement for customers to receive the discount. The purchase requirement can be based on minimum unit or total retail value and only applies to the selected product collections.
          "
        >
          <Grid container spacing={convertPxToRem(12)}>
            <Grid item xs={6}>
              <Controller
                name="conditions.bundleConditions.discountUnit"
                control={control}
                rules={{ required: "Discount method is required" }}
                render={({ field, fieldState: { error } }) => {
                  //  Treez Select component doesn't accept a ref prop
                  const { ref, value, ...rest } = field;
                  return (
                    <Select
                      {...rest}
                      value={value || ""}
                      testId="discount-method-select"
                      label="Discount method"
                      menuItems={Object.values(BUNDLE_DISCOUNT_METHODS)}
                      helperText={error?.message ? error.message : ""}
                      error={!!error}
                      required
                    />
                  );
                }}
              />
            </Grid>
            <Grid item xs={6}>
              <Controller
                name="amount"
                control={control}
                rules={{
                  required: { value: true, message: "Amount is required" },
                  validate: validateDiscountAmountInput(discountUnit),
                }}
                render={({
                  field: { ref, value, ...rest },
                  fieldState: { error },
                }) => (
                  <Input
                    {...rest}
                    value={value || ""}
                    label="Amount"
                    testId="bundle-amount-input"
                    helperText={error?.message || ""}
                    error={!!error}
                    required
                    type="number"
                  />
                )}
              />
            </Grid>

            <Grid item xs={6}>
              <Controller
                name="conditions.bundleConditions.purchaseRequirement"
                control={control}
                rules={{ required: "Purchase requirement is required" }}
                render={({ field, fieldState: { error } }) => {
                  //  Treez Select component doesn't accept a ref prop
                  const { ref, value, ...rest } = field;
                  return (
                    <Select
                      {...rest}
                      value={value || ""}
                      testId="purchase-requirement-select"
                      label="Purchase requirement"
                      menuItems={Object.values(BUNDLE_PURCHASE_REQUIREMENTS)}
                      helperText={error?.message ? error.message : ""}
                      error={!!error}
                      required
                    />
                  );
                }}
              />
            </Grid>
            <Grid item xs={6}>
              <Controller
                name="conditions.bundleConditions.buyCount"
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: "Purchase amount",
                  },
                  validate: validateDiscountAmountInput(discountUnit),
                }}
                render={({
                  field: { ref, value, ...rest },
                  fieldState: { error },
                }) => (
                  <Input
                    {...rest}
                    value={value || ""}
                    label="Purchase amount"
                    testId="bundle-buy-count-input"
                    helperText={error?.message || ""}
                    error={!!error}
                    required
                    type="number"
                  />
                )}
              />
            </Grid>

            {discountUnit === BundleDiscountMethods.PERCENT && (
              <Grid item xs={6}>
                <Tooltip
                  title="With Threshold Enabled: The discount applies to the entire qualifying purchase once it exceeds the specified minimum purchase requirement. Without Threshold: The discount only applies to the minimum purchase amount. For additional items to receive the discount, each must meet the minimum purchase requirement separately."
                  variant="multiRow"
                  cursor="default"
                >
                  <Controller
                    name="conditions.bundleConditions.threshold"
                    control={control}
                    render={({ field, fieldState: { error } }) => {
                      // Treez Checkbox component doesn't accept a ref prop
                      const { ref, value, ...rest } = field;
                      return (
                        <Checkbox
                          {...rest}
                          testId="bundle-threshold-checkbox"
                          label="Enable Threshold"
                          value={value}
                          checked={value}
                          error={!!error}
                        />
                      );
                    }}
                  />
                </Tooltip>
              </Grid>
            )}
          </Grid>
        </Panel>
      )}
      <Panel>
        <Grid container spacing={convertPxToRem(12)}>
          <Grid item xs={12}>
            <ProductCollectionsSelectionCard
              testId="automated-product-collections-card"
              title="Select Products to Apply the Discount"
              subtitle={getSelectProductsSubtitle()}
              errorMessage="No products selected. Add a Product Collection to select which products are eligible for the discount."
              formName="collections"
            />
          </Grid>
        </Grid>
      </Panel>
      <Modal
        testId="automated-product-collections-error-modal"
        title="No Products Selected"
        content={
          <>
            {errors?.collections && (
              <Typography variant="largeText">
                Add a Product Collection to select which products are eligible
                for the discount.
              </Typography>
            )}
            {errors?.collectionsRequired && (
              <Typography variant="largeText">
                Add a Product Collection to select which products are eligible
                for the discount.
              </Typography>
            )}
          </>
        }
        open={
          !!errors.collections?.message || !!errors.collectionsRequired?.message
        }
        onClose={() => clearErrors(["collections", "collectionsRequired"])}
        primaryButton={{
          label: "OK",
          onClick: () => clearErrors(["collections", "collectionsRequired"]),
        }}
      />
    </StyledProductCollectionsStep>
  );
};

export default ProductCollectionsStep;
