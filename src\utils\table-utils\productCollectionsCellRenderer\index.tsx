import React from "react";
import { GridRenderCellParams } from "@mui/x-data-grid-pro";
import { StaticChip, Tooltip } from "@treez-inc/component-library";
import { ProductCollectionDiscountResponse } from "../../../interfaces/responseModels";

export const productCollectionsCellRenderer = (
  params: GridRenderCellParams
) => {
  if (params.row.collections && params.row.collections[0]) {
    const collectionNames = params.row.collections.map(
      (collection: ProductCollectionDiscountResponse) =>
        collection.productCollectionName
          ? collection.productCollectionName
          : collection.productCollectionId
    );

    return collectionNames.length > 1 ? (
      <Tooltip
        variant="context"
        enterDelay={400}
        leaveDelay={0}
        title={collectionNames.join(", ")}
      >
        <StaticChip
          badgeContent={collectionNames.length}
          label="Multiple"
          testId="multiple-collection-chip"
        />
      </Tooltip>
    ) : (
      <Tooltip
        variant="context"
        enterDelay={400}
        leaveDelay={0}
        title={collectionNames[0] || ""}
      >
        <StaticChip testId="collection-chip" label={collectionNames[0]} />
      </Tooltip>
    );
  }
  return null;
};
