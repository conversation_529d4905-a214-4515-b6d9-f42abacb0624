import React from "react";
import { render, screen } from "@testing-library/react";
import {
  blackAndWhiteColors,
  darkGreenColors,
  TreezThemeProvider,
} from "@treez-inc/component-library";
import LoadingSpinner from ".";

describe("<LoadingSpinner />", () => {
  const renderLoadingSpinner = () => {
    render(
      <TreezThemeProvider>
        <LoadingSpinner />
      </TreezThemeProvider>
    );
    const { getByTestId } = screen;

    const loadingSpinner = getByTestId("loading-spinner");

    return { loadingSpinner };
  };

  it("should render the Treez Logo", () => {
    const { loadingSpinner } = renderLoadingSpinner();

    expect(loadingSpinner).toBeInTheDocument();
    expect(loadingSpinner).toHaveStyle(
      `color: ${darkGreenColors.green06.main}`
    );
  });

  it("should render a white background overlay", () => {
    const { loadingSpinner } = renderLoadingSpinner();
    const loadingContainer = loadingSpinner.parentElement;
    const background = loadingContainer?.parentElement;

    expect(background).toHaveStyle(
      `backgroundColor: ${blackAndWhiteColors.primaryWhite.main}`
    );
    expect(background).toHaveStyle(`opacity: 0.7`);
  });
});
