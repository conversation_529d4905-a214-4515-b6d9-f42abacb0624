import { formatSchedule } from ".";
import {
  CustomEndType,
  CustomRepeatEvery,
  RepeatType,
  Schedule,
} from "../../../../../interfaces/discounts";
import { formatDateToTime, getDateLocaleString } from "../../../../../utils";

describe("formatSchedule function", () => {
  it("should correctly format startDate", () => {
    const date = new Date();

    const schedule: Schedule = {
      startDate: date,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.startDate).toBe(getDateLocaleString(date));
  });

  it("should correctly format endDate", () => {
    const date = new Date();

    const schedule: Schedule = {
      endDate: date,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.endDate).toBe(getDateLocaleString(date));
  });

  it("should correctly format startTime", () => {
    const date = new Date();

    const schedule: Schedule = {
      startTime: date,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.startTime).toBe(formatDateToTime(date));
  });

  it("should correctly format endTime", () => {
    const date = new Date();

    const schedule: Schedule = {
      endTime: date,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.endTime).toBe(formatDateToTime(date));
  });

  it("should correctly format allDay", () => {
    const schedule: Schedule = {
      allDay: true,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.allDay).toBe(true);
  });

  it("should correctly format spansMultipleDays", () => {
    const schedule: Schedule = {
      spansMultipleDays: true,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.spansMultipleDays).toBe(true);
  });

  it("should correctly format repeatType", () => {
    const schedule: Schedule = {
      repeatType: RepeatType.ANNUAL,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.repeatType).toBe(RepeatType.ANNUAL);
  });

  it("should correctly format customRepeatIntervalCount", () => {
    const schedule: Schedule = {
      customRepeatIntervalCount: 1,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.customRepeatIntervalCount).toBe(1);
  });

  it("should correctly format customRepeatEvery", () => {
    const schedule: Schedule = {
      customRepeatEvery: CustomRepeatEvery.DAY,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.customRepeatEvery).toBe(CustomRepeatEvery.DAY);
  });

  it("should correctly format customRepeatDaysOfWeek", () => {
    const schedule: Schedule = {
      customRepeatDaysOfWeek: {
        MON: true,
        TUE: true,
        WED: true,
        THU: true,
        FRI: true,
        SAT: true,
        SUN: true,
      },
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.customRepeatDaysOfWeek?.MON).toBe(true);
    expect(formattedSchedule.customRepeatDaysOfWeek?.TUE).toBe(true);
    expect(formattedSchedule.customRepeatDaysOfWeek?.WED).toBe(true);
    expect(formattedSchedule.customRepeatDaysOfWeek?.THU).toBe(true);
    expect(formattedSchedule.customRepeatDaysOfWeek?.FRI).toBe(true);
    expect(formattedSchedule.customRepeatDaysOfWeek?.SAT).toBe(true);
    expect(formattedSchedule.customRepeatDaysOfWeek?.SUN).toBe(true);
  });

  it("should correctly format customEndType", () => {
    const schedule: Schedule = {
      customEndType: CustomEndType.DATE,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.customEndType).toBe(CustomEndType.DATE);
  });

  it("should correctly format customEndDate", () => {
    const date = new Date();

    const schedule: Schedule = {
      customEndDate: date,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.customEndDate).toBe(getDateLocaleString(date));
  });

  it("should correctly format customEndRepeatCount", () => {
    const schedule: Schedule = {
      customEndRepeatCount: 1,
    } as Schedule;
    const formattedSchedule = formatSchedule(schedule);
    expect(formattedSchedule.customEndRepeatCount).toBe(1);
  });
});
