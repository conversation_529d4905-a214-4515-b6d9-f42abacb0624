import React from "react";
import { <PERSON><PERSON>erRouter as Router } from "react-router-dom";
import { render, screen } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import PageHeader from ".";

const testButtonProps = {
  label: "Add Discount",
  onClick: jest.fn(),
  testId: "test-page-header-button",
};

describe("<PageHeader />", () => {
  const renderPageHeader = () => {
    render(
      <Router>
        <TreezThemeProvider>
          <PageHeader
            buttonProps={testButtonProps}
            testId="test-page-header"
            filterOptions={<div>Filter Options</div>}
          />
        </TreezThemeProvider>
      </Router>
    );

    const { getByTestId } = screen;

    const header = getByTestId("test-page-header");
    const button = getByTestId("test-page-header-button");
    const filterOptions = getByTestId("test-page-header-filter-options");

    return {
      header,
      button,
      filterOptions,
    };
  };

  it("should render the <PageHeader /> component", () => {
    const { header } = renderPageHeader();
    expect(header).toBeInTheDocument();
  });

  it("should render the button", () => {
    const { button } = renderPageHeader();
    expect(button).toBeInTheDocument();
  });

  describe("button", () => {
    it("should render the button with the correct label", () => {
      const { button } = renderPageHeader();
      expect(button).toHaveTextContent(testButtonProps.label);
    });

    it("should call the onClick function when clicked", () => {
      const { button } = renderPageHeader();
      button.click();
      expect(testButtonProps.onClick).toHaveBeenCalled();
    });

    it("should render with the correct test id", () => {
      const { button } = renderPageHeader();
      expect(button).toHaveAttribute("data-testid", "test-page-header-button");
    });
  });

  describe("filterOptions", () => {
    it("should render the filter options", () => {
      const { filterOptions } = renderPageHeader();
      expect(filterOptions).toBeInTheDocument();
    });

    it("should render with the correct test id", () => {
      const { filterOptions } = renderPageHeader();
      expect(filterOptions).toHaveAttribute(
        "data-testid",
        "test-page-header-filter-options"
      );
    });
  });
});
