import React, { useEffect, useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { styled, Box } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import ActionModal from "../../components/ActionModal";
import LoadingSpinner from "../../components/LoadingSpinner";
import PageHeader from "../../components/PageHeader";
import AutomatedDiscountsTable from "./components/AutomatedDiscountsTable";
import { useDiscounts, useDiscountsQuery, useGetEntities } from "../../hooks";
import ApiService from "../../services/api/apiService";
import DiscountLogDrawer from "../../components/DiscountLogDrawer";
import { DiscountLogDrawerStateProps } from "../../hooks/useDiscounts";
import ManualDiscountFilters from "../DiscountFilters";
import { KeyValueFilter } from "../../interfaces/table";
import { DropdownSelectOptionProps } from "../../components/DropdownSelect";
import { parseJwt } from "../../utils";
import { entityListUrl } from "../../services/apiEndPoints";
import { DISCOUNT_FILTER_FIELDS } from "../../constants/discountTable";

interface AutomatedDiscountsViewProps {
  api: ApiService;
  permissions: { read: boolean; write: boolean };
}

const AutomatedDiscountsWrapper = styled(Box)({
  overflow: "hidden",
  height: "100%",
  display: "flex",
  flexDirection: "column",
});

const AutomatedDiscountsView: React.FC<AutomatedDiscountsViewProps> = ({
  api,
  permissions,
}) => {
  const [filters, setFilters] = useState<KeyValueFilter>({
    [DISCOUNT_FILTER_FIELDS.STATUS]: ["true"],
  });

  const queryClient = useQueryClient();
  const useOrgDiscountsQuery = useDiscountsQuery(api, {
    isManual: false,
    filters,
  });

  const { data } = useOrgDiscountsQuery;

  const {
    closeModal,
    modalState,
    openDiscountModal,
    openDiscountLog,
    closeDrawer,
    drawerState,
  } = useDiscounts(api);

  const [entitiesOptions, setEntityOptions] = useState<DropdownSelectOptionProps[]>([]);
  const navigate = useNavigate();
  const decodedToken = parseJwt(api.getTokens().accessToken);

  const [entityListState] = useGetEntities(api, entityListUrl(decodedToken?.orgId));

  const updateFilter = (field: string, value: string | boolean | string[]) => {
    setFilters((prevFilters) => ({ ...prevFilters, [field]: value }));
  };

  const handleAddButtonClick = () => {
    navigate(`add`);
  };

  useEffect(() => {
    if (entityListState.data) {
      setEntityOptions(
        entityListState.data.map((entity) => ({
          key: entity.id,
          label: entity.name,
        }))
      );
    }
  }, [entityListState]);

  return (
    <AutomatedDiscountsWrapper>
      {queryClient.isMutating() > 0 && <LoadingSpinner />}
      <PageHeader
        testId="automated-discounts-page-header"
        buttonProps={{
          testId: "add-discount-button",
          label: "Add Discount",
          onClick: handleAddButtonClick,
          disabled: !permissions.write,
        }}
        filterOptions={
          <ManualDiscountFilters
            entitiesOptions={entitiesOptions}
            setFilter={updateFilter}
          />
        }
      />
      <AutomatedDiscountsTable
        discounts={data}
        openDiscountModal={openDiscountModal}
        openDiscountLog={openDiscountLog}
      />
      <Outlet />
      <ActionModal
        testId="automated-discounts"
        closeModal={closeModal}
        modalState={modalState}
        primaryLabel="Confirm"
        secondaryLabel="Cancel"
      />

      <DiscountLogDrawer
        testId="discounts-log-drawer"
        closeDrawer={closeDrawer}
        drawerState={drawerState as DiscountLogDrawerStateProps}
      />
    </AutomatedDiscountsWrapper>
  );
};
export default AutomatedDiscountsView;
