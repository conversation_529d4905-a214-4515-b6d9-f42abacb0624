import React from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { styled, Box } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import ActionModal from "../../components/ActionModal";
import LoadingSpinner from "../../components/LoadingSpinner";
import PageHeader from "../../components/PageHeader";
import AutomatedDiscountsTable from "./components/AutomatedDiscountsTable";
import { useDiscounts, useDiscountsQuery } from "../../hooks";
import ApiService from "../../services/api/apiService";
import DiscountLogDrawer from "../../components/DiscountLogDrawer";
import { DiscountLogDrawerStateProps } from "../../hooks/useDiscounts";

interface AutomatedDiscountsViewProps {
  api: ApiService;
  permissions: { read: boolean; write: boolean };
}

const AutomatedDiscountsWrapper = styled(Box)({
  overflow: "hidden",
  height: "100%",
  display: "flex",
  flexDirection: "column",
});

const AutomatedDiscountsView: React.FC<AutomatedDiscountsViewProps> = ({
  api,
  permissions,
}) => {
  const queryClient = useQueryClient();
  const useOrgDiscountsQuery = useDiscountsQuery(api, { isManual: false });

  const { data } = useOrgDiscountsQuery;

  const {
    closeModal,
    modalState,
    openDiscountModal,
    openDiscountLog,
    closeDrawer,
    drawerState,
  } = useDiscounts(api);

  const navigate = useNavigate();

  const handleAddButtonClick = () => {
    navigate(`add`);
  };

  return (
    <AutomatedDiscountsWrapper>
      {queryClient.isMutating() > 0 && <LoadingSpinner />}
      <PageHeader
        testId="automated-discounts-page-header"
        buttonProps={{
          testId: "add-discount-button",
          label: "Add Discount",
          onClick: handleAddButtonClick,
          disabled: !permissions.write,
        }}
      />
      <AutomatedDiscountsTable
        discounts={data}
        openDiscountModal={openDiscountModal}
        openDiscountLog={openDiscountLog}
      />
      <Outlet />
      <ActionModal
        testId="automated-discounts"
        closeModal={closeModal}
        modalState={modalState}
        primaryLabel="Confirm"
        secondaryLabel="Cancel"
      />

      <DiscountLogDrawer
        testId="discounts-log-drawer"
        closeDrawer={closeDrawer}
        drawerState={drawerState as DiscountLogDrawerStateProps}
      />
    </AutomatedDiscountsWrapper>
  );
};
export default AutomatedDiscountsView;
