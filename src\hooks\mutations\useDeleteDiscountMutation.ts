import { QueryClient, useMutation } from "@tanstack/react-query";
import ApiService from "../../services/api/apiService";
import { deleteDiscountUrl } from "../../services/apiEndPoints";
import { OrgDiscountResponse } from "../../interfaces/responseModels";

const useDeleteDiscountMutation = ({
  api,
  queryClient,
}: {
  api: ApiService;
  queryClient: QueryClient;
}) =>
  useMutation({
    mutationFn: async (id: string) => {
      const result = await api.delete(deleteDiscountUrl(id));
      return { ...result.data, id };
    },
    onSuccess: (data: OrgDiscountResponse) => {
      queryClient.setQueryData(
        ["discounts"],
        (oldDiscountsList: OrgDiscountResponse[]): OrgDiscountResponse[] => {
          if (oldDiscountsList) {
            const deletedDiscountIndex = oldDiscountsList.findIndex(
              (discount) => discount.id === data.id
            );
            if (deletedDiscountIndex > -1) {
              const newDiscountsList = [...oldDiscountsList];
              newDiscountsList.splice(deletedDiscountIndex, 1);
              return newDiscountsList;
            }
          }
          return oldDiscountsList;
        }
      );
    },
  });

export default useDeleteDiscountMutation;
