import React from "react";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import AccordionPanel from ".";

const defaultAccordionPanelProps = {
  title: "title",
  subtitle: "subtitle",
  testId: "testId",
  expanded: false,
};

const renderAccordionPanel = (propsOverride = {}) => {
  const defaultProps = {
    ...defaultAccordionPanelProps,
    ...propsOverride,
  };

  const { title, testId } = defaultProps;

  const { container } = render(
    <TreezThemeProvider>
      <AccordionPanel {...defaultProps}>
        <div>Content</div>
      </AccordionPanel>
    </TreezThemeProvider>
  );

  const accordionPanel = screen.getByTestId(`${testId}-accordion-panel`);
  const accordionButtonBase = accordionPanel.querySelector(
    ".MuiButtonBase-root"
  );

  const toggleAccordion = () => {
    fireEvent.click(screen.getByText(title));
  };

  return { container, accordionPanel, accordionButtonBase, toggleAccordion };
};

describe("AccordionPanel", () => {
  it("should render", () => {
    const { accordionPanel } = renderAccordionPanel();
    expect(accordionPanel).toBeInTheDocument();
  });

  it("should render testId when testId prop is passed", () => {
    const { accordionPanel } = renderAccordionPanel({ testId: "test-id" });
    expect(accordionPanel).toHaveAttribute(
      "data-testid",
      "test-id-accordion-panel"
    );
  });

  it("should expand and collapse when clicked", async () => {
    const { accordionButtonBase, toggleAccordion } = renderAccordionPanel();

    expect(accordionButtonBase).toHaveAttribute("aria-expanded", "false");

    await waitFor(() => {
      toggleAccordion();
    });

    expect(accordionButtonBase).toHaveAttribute("aria-expanded", "true");

    await waitFor(() => {
      toggleAccordion();
    });

    expect(accordionButtonBase).toHaveAttribute("aria-expanded", "false");
  });

  it("should display accordion button as expanded if the expanded property is provided", async () => {
    const { accordionButtonBase } = renderAccordionPanel({ expanded: true });

    expect(accordionButtonBase).toHaveAttribute("aria-expanded", "true");
  });
});

export default renderAccordionPanel;
