import React, { useEffect, useState } from "react";
import {
  Control,
  Controller,
  ControllerRenderProps,
  FieldErrors,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
  UseFormWatch,
} from "react-hook-form";
import { Box, Grid, Typography, styled } from "@mui/material";
import {
  Alert,
  Button,
  Checkbox,
  Icon,
  Input,
  Modal,
  RadioButton,
  Select,
  Tooltip,
  convertPxToRem,
} from "@treez-inc/component-library";
import { ManualDiscountFormData } from "../../../../../interfaces/discounts";
import {
  MANUAL_DISCOUNT_METHODS,
  DISCOUNT_METHOD_OPTIONS,
} from "../../../../../constants/discountForm";
import {
  validateAmountInput,
  validateRequireReasonDiscountType,
} from "../../../../../utils";
import { useSnackbar } from "../../../../../providers/SnackbarProvider";
import CouponForm from "../CouponForm";
import { ManualDiscountMethods } from "../../../../../constants/discounts";

export interface DiscountInfoStepProps {
  trigger: UseFormTrigger<ManualDiscountFormData>;
  errors: FieldErrors<ManualDiscountFormData>;
  control: Control<ManualDiscountFormData>;
  getValues: UseFormGetValues<ManualDiscountFormData>;
  setValue: UseFormSetValue<ManualDiscountFormData>;
  watch: UseFormWatch<ManualDiscountFormData>;
  isEditMode: boolean;
  isEditConfirmModal: boolean;
  closeEditConfirmModal: () => void;
  handleEditConfirmModal: () => void;
  couponFeatureFlag: boolean;
  priceAtFeatureFlag: boolean;
}

const StyledDiscountInfoStep = styled(Box)({
  "> div": {
    marginBottom: convertPxToRem(24),
  },
});

const StyledGrid = styled(Grid)(({ theme }) => ({
  width: "100%",
  boxSizing: "border-box",
  background: theme.palette.primaryWhite.main,
  border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
  borderRadius: convertPxToRem(16),
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  padding: `${convertPxToRem(24)}`,
  marginBottom: convertPxToRem(24),
}));

export const StyledTitle = styled(Box)({
  height: convertPxToRem(20),
  marginBottom: convertPxToRem(16),
  display: "flex",
  width: "100%",
  justifyContent: "space-between",
  alignItems: "center",
});

export const StyledInputTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.grey08.main,
  fontWeight: 700,
  width: "100%",
}));

export const StyledInputSubtitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.grey08.main,
  fontWeight: 400,
  marginTop: convertPxToRem(12),
  marginBottom: convertPxToRem(12),
}));

const StyledInputItem = styled(Grid)({
  marginTop: convertPxToRem(6),
});

const StyledInputContainer = styled(Grid)({
  display: "flex",
  justifyContent: "space-between",
  minHeight: convertPxToRem(76),
});

const StyledFieldBox = styled(Box)({
  width: "100%",
  display: "flex",
  marginTop: convertPxToRem(30),
  flexDirection: "column",
});

const DiscountRequirementBox = styled(Box)({
  marginTop: convertPxToRem(20),
});

const AlertContainer = styled(Box)({
  marginTop: convertPxToRem(24),
});

const StyledRequiredDiscountFieldError = styled(Typography)({
  margin: `${convertPxToRem(8)} 0`,
});

const DiscountTypeRequirementInputBox = styled(Box)(() => ({
  display: "flex",
  alignItems: "center",
}));

const DiscountTypeInfoIcon = styled(Box)(() => ({
  display: "flex",
  marginLeft: convertPxToRem(8),
}));

const DiscountInfoStep: React.FC<DiscountInfoStepProps> = ({
  trigger,
  errors,
  control,
  setValue,
  getValues,
  isEditMode,
  watch,
  isEditConfirmModal,
  closeEditConfirmModal,
  handleEditConfirmModal,
  couponFeatureFlag,
  priceAtFeatureFlag,
}) => {
  const discountPlaceholderMethods = {
    [MANUAL_DISCOUNT_METHODS.DOLLAR]: "Amount",
    [MANUAL_DISCOUNT_METHODS.PERCENT]: "Percentage",
    [MANUAL_DISCOUNT_METHODS.PRICE_AT]: "Amount",
  };

  const discountMethods = priceAtFeatureFlag ? 
    DISCOUNT_METHOD_OPTIONS :
    DISCOUNT_METHOD_OPTIONS.filter(m => m.displayValue !== ManualDiscountMethods.PRICE_AT);

  const method = getValues("method");

  const [discountMethod, setDiscountMethod] = useState({
    method,
    label: discountPlaceholderMethods[method],
  });

  const [isItem, setIsItem] = useState(false);
  const isItemFormField = watch("isItem");

  const onDiscountTypeChange = (value: string) => {
    const newType = value === "true";
    setValue("isItem", newType);
    setValue("couponFormModel", { coupons: [], couponToAdd: "" });
    setIsItem(newType);
  };

  useEffect(() => {
    setIsItem(isItemFormField);
  }, [isItemFormField]);

  const { openSnackbar } = useSnackbar();

  const formErrors: any = errors;

  const onDiscountMethodChange = (event: any) => {
    setValue("method", event.target.value);

    setDiscountMethod({
      method: event.target.value,
      label: discountPlaceholderMethods[event.target.value] || "Amount",
    });

    if (event.target.value === MANUAL_DISCOUNT_METHODS.PRICE_AT) {
      onDiscountTypeChange("true");
    }
  };

  useEffect(() => {
    if (getValues().amount) {
      trigger("amount");
    }
  }, [discountMethod]);

  const onDiscountRequirementToggle =
    (onChange: ControllerRenderProps["onChange"]) => (checked?: boolean) => {
      onChange(checked);
      // Triggers revalidation of `requireReason` discount type
      trigger("requireReason");
    };

  return (
    <StyledDiscountInfoStep>
      <StyledGrid
        item
        sm={9}
        xs={12}
        data-testid="manual-discount-form-container"
      >
        <StyledTitle>
          <StyledInputTitle
            data-testid="manual-discount-info-title"
            variant="h6"
            color="primaryBlack"
          >
            Manual Discount Information
          </StyledInputTitle>
          {isEditMode && (
            <Box minWidth="fit-content">
              <Button
                label="Copy Discount ID"
                onClick={async () => {
                  try {
                    const discountId = getValues("id");
                    await navigator.clipboard.writeText(discountId!);
                    openSnackbar({
                      message: "Discount ID was copied to clipboard",
                    });
                  } catch (err) {
                    openSnackbar({
                      message: "Unable to copy Discount ID at the moment",
                      severity: "error",
                    });
                  }
                }}
                testId="copy-discount-id-button"
                variant="text"
                iconName="Copy"
              />
            </Box>
          )}
        </StyledTitle>
        <StyledInputContainer container>
          <StyledInputItem container>
            <Controller
              control={control}
              name="title"
              rules={{
                required: {
                  value: true,
                  message: "Discount title is required",
                },
              }}
              render={({ field: { onChange, value } }) => (
                <Input
                  testId="discount-title-input"
                  label="Discount Title"
                  value={value}
                  helperText={
                    errors?.title?.message
                      ? errors.title.message.toString()
                      : "*Field Required"
                  }
                  error={!!errors?.title?.message}
                  onChange={onChange}
                  required
                />
              )}
            />
          </StyledInputItem>
        </StyledInputContainer>

        <StyledFieldBox>
          <StyledInputTitle variant="largeTextStrong" color="grey08">
            Global discount conditions
          </StyledInputTitle>

          <StyledInputContainer container spacing={2}>
            <StyledInputItem item xs={12} lg={6} md={6} sm={12}>
              <Controller
                control={control}
                name="method"
                rules={{
                  required: { value: true, message: "Method is required" },
                }}
                render={({ field: { value } }) => (
                  <Select
                    label=""
                    value={value}
                    menuItems={discountMethods}
                    testId="select-option"
                    helperText={
                      errors?.method?.message
                        ? errors.method.message.toString()
                        : "*Field Required"
                    }
                    error={!!formErrors?.method?.message}
                    onChange={onDiscountMethodChange}
                    required
                  />
                )}
              />
            </StyledInputItem>

            <StyledInputItem item xs={12} lg={6} md={6} sm={12}>
              <Controller
                control={control}
                name="amount"
                rules={{
                  required: { value: true, message: "Amount is required" },
                  validate: validateAmountInput(discountMethod.method),
                }}
                render={({ field: { onChange, value } }) => (
                  <Input
                    testId="discount-amount-input"
                    label={discountMethod.label}
                    value={value}
                    type="number"
                    helperText={
                      errors?.amount?.message
                        ? errors.amount.message.toString()
                        : ""
                    }
                    error={!!errors?.amount?.message}
                    onChange={onChange}
                    required
                  />
                )}
              />
            </StyledInputItem>
          </StyledInputContainer>
        </StyledFieldBox>

        <StyledFieldBox>
          <StyledInputContainer container>
            <StyledInputTitle variant="largeTextStrong">
              Discount Type
            </StyledInputTitle>
            <StyledInputSubtitle variant="mediumText">
              Select your discount type: Line item discount for individual items
              or cart discount for the whole cart value. Price At must be line item 
              discounts. Coupons can only be applied to cart discounts.
            </StyledInputSubtitle>
            <StyledInputItem container>
              <Box>
                <Controller
                  name="isItem"
                  control={control}
                  render={({ field }) => {
                    const { ref, ...rest } = field;
                    return (
                      <RadioButton
                        {...rest}
                        label="Discount Type"
                        row
                        hideGroupLabel
                        testId="discount-types-radio-group"
                        onChange={(event, value) => onDiscountTypeChange(value)}
                        radioButtons={[
                          {
                            label: "This is a cart discount",
                            value: "false",
                            disabled: method === MANUAL_DISCOUNT_METHODS.PRICE_AT,
                          },
                          {
                            label: "This is a line item discount",
                            value: "true",
                          },
                        ]}
                      />
                    );
                  }}
                />

                <DiscountRequirementBox data-testid="discount-requirement-box">
                  <StyledInputContainer container>
                    <StyledInputTitle variant="largeTextStrong">
                      Discount Requirements
                    </StyledInputTitle>

                    <StyledInputSubtitle variant="mediumText">
                      Select the discount requirements to be applied in the POS.
                    </StyledInputSubtitle>

                    {!isItem && couponFeatureFlag && (
                      <StyledInputItem container>
                        <Controller
                          control={control}
                          name="requireCoupon"
                          render={({ field: { onChange, value } }) => (
                            <DiscountTypeRequirementInputBox>
                              <Checkbox
                                label="Hide Discount Button in POS"
                                testId="discount-require-coupon-checkbox"
                                onChange={onDiscountRequirementToggle(onChange)}
                                value={value}
                                checked={!!value}
                              />
                              <DiscountTypeInfoIcon>
                                <Tooltip
                                  placement="right"
                                  themeColor="light"
                                  title="Recommended if a coupon code or AIQ redemption is required."
                                  variant="multiRow"
                                >
                                  <Icon iconName="InfoOutlined" />
                                </Tooltip>
                              </DiscountTypeInfoIcon>
                            </DiscountTypeRequirementInputBox>
                          )}
                        />
                      </StyledInputItem>
                    )}

                    <StyledInputItem container>
                      <Controller
                        control={control}
                        name="requirePin"
                        render={({ field: { onChange, value } }) => (
                          <Checkbox
                            label="Require manager PIN"
                            onChange={onChange}
                            value={value}
                            checked={!!value}
                            testId="discount-info-require-manager-pin-checkbox"
                          />
                        )}
                      />
                    </StyledInputItem>

                    <StyledInputItem container>
                      <Controller
                        control={control}
                        name="requireReason"
                        rules={{
                          validate: validateRequireReasonDiscountType,
                        }}
                        render={({ field: { onChange, value } }) => (
                          <Checkbox
                            label="Require reason"
                            onChange={onDiscountRequirementToggle(onChange)}
                            value={value}
                            checked={!!value}
                            testId="discount-require-reason-checkbox"
                          />
                        )}
                      />
                    </StyledInputItem>

                    {formErrors.requireReason && (
                      <StyledRequiredDiscountFieldError
                        data-testid="require-reason-error"
                        variant="mediumText"
                        color="error"
                      >
                        {formErrors.requireReason.message}
                      </StyledRequiredDiscountFieldError>
                    )}
                  </StyledInputContainer>
                </DiscountRequirementBox>

                {isEditMode && (
                  <AlertContainer>
                    <Alert
                      severity="warning"
                      title="Custom conditions will not be affected by global condition changes"
                      message="Some stores may have unique conditions distinct from the upcoming global condition changes you are about to make. If these store-specific conditions are present, they will not be affected by modifications to global conditions."
                      testId="edit-manual-discount-warning-alert"
                    />
                  </AlertContainer>
                )}
              </Box>
            </StyledInputItem>
          </StyledInputContainer>
        </StyledFieldBox>

        <Modal
          testId="manual-discount-edit-modal"
          title="Confirm change"
          content={
            <Typography variant="largeText">
              Are you sure you want to change the global discount information?
              This will affect all the stores carrying this discount. Individual
              store settings can be updated in the next step.
            </Typography>
          }
          open={isEditConfirmModal}
          onClose={closeEditConfirmModal}
          primaryButton={{ label: "Confirm", onClick: handleEditConfirmModal }}
          secondaryButton={{ label: "Cancel", onClick: closeEditConfirmModal }}
        />
      </StyledGrid>

      {!isItem && couponFeatureFlag && (
        <CouponForm trigger={trigger} control={control} errors={errors} />
      )}
    </StyledDiscountInfoStep>
  );
};

export default DiscountInfoStep;
