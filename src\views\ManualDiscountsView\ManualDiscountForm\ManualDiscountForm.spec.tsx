import React from "react";
import { screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { HttpResponse, http } from "msw";
import { AxiosError } from "axios";
import ManualDiscountForm from ".";
import renderWithProviders from "../../../test/renderWithProviders";
import {
  entities,
  testDiscountsResponse,
  tokens,
} from "../../../test/constants";
import ApiService from "../../../services/api/apiService";
import { server } from "../../../test/mocks/node";
import { upsertDiscountUrl } from "../../../services/apiEndPoints";
import { CouponResponse } from "../../../interfaces/responseModels";
import { MANUAL_DISCOUNT_METHODS } from "../../../constants/discountForm";

const mockNavigate = jest.fn();
const mockedUseParams = jest.fn();

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => mockNavigate,
  useParams: () => mockedUseParams(),
}));

jest.mock("../../../hooks/queries/useGetEntities");
const mockUseGetEntities =
  require("../../../hooks/queries/useGetEntities").default;

jest.mock("../../../hooks/queries/useGetFeatureFlag");
const mockUseGetFeatureFlag =
  require("../../../hooks/queries/useGetFeatureFlag").default;

const mockSetIsPageLoading = jest.fn().mockReturnValue({
  isPageLoading: false,
  setIsPageLoading: jest.fn(),
});
jest.mock("../../../hooks/usePageLoading", () => ({
  __esModule: true,
  default: () => mockSetIsPageLoading(),
}));

const mockScrollIntoView = jest.fn();
window.HTMLElement.prototype.scrollIntoView = mockScrollIntoView;
const writeText = jest.fn();

Object.assign(navigator, {
  clipboard: {
    writeText,
  },
});

beforeEach(() => {
  mockUseGetEntities.mockReturnValue([
    {
      error: null,
      loading: false,
      data: entities,
    },
  ]);
  mockedUseParams.mockReturnValue({
    orgDiscountId: testDiscountsResponse[0].id,
  });
  mockUseGetFeatureFlag.mockReturnValue([
    { error: null, loading: false, data: { status: false } },
  ]);
});

const mockApiService = new ApiService(
  () => tokens,
  () => { }
);

describe("ManualDiscountForm", () => {
  const renderManualDiscountForm = (editMode = false, api = mockApiService) => {
    const { getByTestId, getAllByTestId, queryByTestId } = screen;

    renderWithProviders(
      <ManualDiscountForm
        isEditMode={editMode}
        permissions={{ write: true, read: true }}
        api={api}
      />,
      {
        route: "/",
      }
    );

    const container = getByTestId("manual-discount-add-modal");

    const getModal = () => container;
    const getDiscountInfoTitle = () =>
      getByTestId("manual-discount-info-title");
    const getNextStepButton = () => getByTestId("next-step-button");
    const getDoneEditingButton = () => queryByTestId("done-editing-button");
    const getTitleInput = () => getByTestId("discount-title-input");
    const getAmountInput = () => getByTestId("discount-amount-input");
    const getDiscountMethodSelect = () => queryByTestId("select-option");
    const getSelectStoreStep = () => getByTestId("select-store-grid");
    const getSetConditionsStep = () => getByTestId("manual-set-conditions-step");
    const getEntitiesBox = () => getAllByTestId("entities-store-box");
    const getReviewStep = () => getByTestId("review-discount-grid");
    const getReviewCondition = () => getAllByTestId("review-condition-box");
    const getAddCouponNameInput = () => getByTestId("add-coupon-name-input");
    const getAddCouponButton = () => getByTestId("add-coupon-button");
    const getEditDiscountWarningAlert = () =>
      getByTestId("edit-manual-discount-warning-alert");
    const getCopyDiscountIDButton = () =>
      getByTestId("copy-discount-id-button");
    const getDiscountRequireManagerPinCheckbox = () =>
      getByTestId("discount-info-require-manager-pin-checkbox");
    const getDiscountRequireReasonCheckbox = () =>
      getByTestId("discount-require-reason-checkbox");
    const getDiscountRequireCouponCheckbox = () =>
      getByTestId("discount-require-coupon-checkbox");
    const getStoreSearchInput = () => getByTestId("store-search-input");
    const getRequireReasonError = () => getByTestId("require-reason-error");
    const getConfirmChangeModal = () =>
      getByTestId("manual-discount-edit-modal");
    const loadingSpinner = () => queryByTestId("loading-spinner");

    return {
      getModal,
      getDiscountInfoTitle,
      getNextStepButton,
      getDiscountMethodSelect,
      getDoneEditingButton,
      getTitleInput,
      getAmountInput,
      getSelectStoreStep,
      getSetConditionsStep,
      getEntitiesBox,
      getReviewStep,
      getReviewCondition,
      getEditDiscountWarningAlert,
      getCopyDiscountIDButton,
      getAddCouponNameInput,
      getAddCouponButton,
      getDiscountRequireManagerPinCheckbox,
      getDiscountRequireReasonCheckbox,
      getDiscountRequireCouponCheckbox,
      getStoreSearchInput,
      getRequireReasonError,
      getConfirmChangeModal,
      loadingSpinner,
    };
  };

  it("should display a top banner when editing discount", async () => {
    renderManualDiscountForm(true);

    await waitFor(() => {
      expect(
        screen.getByTestId("wizard-form-modal-top-info-bar")
      ).toBeInTheDocument();
    });
  });

  it("should display a loading spinner, trigger a snackbar and call navigate when a existing discount is not retrieved", async () => {
    jest.spyOn(console, "error").mockImplementation(jest.fn());

    server.use(
      http.get(
        `${upsertDiscountUrl}/:id`,
        () => new HttpResponse(null, { status: 500 }),
        {
          once: true,
        }
      )
    );

    renderManualDiscountForm(true);

    await screen.findByText(
      "There was an error retrieving the discount details"
    );
  });

  describe("Step 1: Discount Information", () => {
    it("should render the first section of discount", async () => {
      const { getModal, getDiscountInfoTitle } = renderManualDiscountForm();

      expect(getModal()).toBeInTheDocument();
      expect(getModal()).toBeDefined();
      expect(getDiscountInfoTitle().textContent).toEqual(
        "Manual Discount Information"
      );
    });

    it("discount type should be Line when selecting line item option", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });

      const discountTypesRadios = screen
        .getByTestId("discount-types-radio-group")
        .querySelectorAll(".MuiRadio-root");

      await userEvent.click(discountTypesRadios[1]);

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(
          screen.getByTestId("globalconditiondiscounttype-text")
        ).toHaveTextContent("Item");
      });
    });

    it("triggers validation on amount input when editing method", async () => {
      const { getAmountInput, getDiscountMethodSelect } =
        renderManualDiscountForm();

      fireEvent.change(getAmountInput()!.querySelector("input")!, {
        target: { value: "120" },
      });

      fireEvent.change(getDiscountMethodSelect()!.querySelector("input")!, {
        target: { value: MANUAL_DISCOUNT_METHODS.PERCENT },
      });

      await waitFor(() => {
        expect(
          getAmountInput().querySelector(".MuiFormHelperText-root ")
            ?.textContent
        ).toContain("Please enter a number smaller than or equal to 100%");
      });
    });

    it("should not go to the 2nd step without filling required info", async () => {
      const { getNextStepButton, getTitleInput, getAmountInput } =
        renderManualDiscountForm();

      await userEvent.click(getNextStepButton());

      const titleHelperText = getTitleInput().querySelector("p");
      const amountHelperText = getAmountInput().querySelector("p");

      expect(titleHelperText).toHaveTextContent(/Discount title is required/);
      expect(amountHelperText).toHaveTextContent(/Amount is required/);
    });

    it("should display the 2nd step after filling all required info", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });
      await userEvent.click(getNextStepButton());

      expect(getSelectStoreStep()).toBeInTheDocument();
    });

    it("should copy the discount ID when the copy discount ID button is clicked", async () => {
      const { getCopyDiscountIDButton, loadingSpinner } =
        renderManualDiscountForm(true);

      await waitFor(() => {
        expect(loadingSpinner()).toEqual(null);
      });
      const discountActionCopyIDButton = getCopyDiscountIDButton();
      expect(discountActionCopyIDButton).toBeInTheDocument();

      await userEvent.click(discountActionCopyIDButton);

      expect(navigator.clipboard.writeText).toHaveBeenCalledWith("disc1");
    });

    it("should display corresponding discount requirements as checked/unchecked", async () => {
      const {
        getDiscountRequireManagerPinCheckbox,
        getDiscountRequireReasonCheckbox,
      } = renderManualDiscountForm(true);

      const requirePin = getDiscountRequireManagerPinCheckbox();
      const requireReason = getDiscountRequireReasonCheckbox();

      await waitFor(() => {
        expect(requirePin.querySelector("input")).toBeChecked();
        expect(requireReason.querySelector("input")).not.toBeChecked();
      });
    });

    it("should display an error message when requireReason and requireCoupon are both checked", async () => {
      mockUseGetFeatureFlag.mockReturnValue([
        { error: null, loading: false, data: { status: true } },
      ]);

      const {
        getDiscountRequireReasonCheckbox,
        getDiscountRequireCouponCheckbox,
        getRequireReasonError,
      } = renderManualDiscountForm(true);

      const requireReason = getDiscountRequireReasonCheckbox();
      const requireCoupon = getDiscountRequireCouponCheckbox();

      await userEvent.click(requireCoupon);
      await userEvent.click(requireReason);

      const requireReasonError = getRequireReasonError();

      expect(requireReasonError).toHaveTextContent(
        "Require Reason cannot be used with coupons"
      );
    });

    it("should display error message if requireReason is checked and coupons are present", async () => {
      mockUseGetFeatureFlag.mockReturnValue([
        { error: null, loading: false, data: { status: true } },
      ]);

      server.use(
        http.get(
          `${upsertDiscountUrl}/:id`,
          () => {
            const coupon: CouponResponse = {
              id: "coupon-1",
              title: "Coupon",
              code: "COUPON-1",
              startDate: "2023-12-19",
              description: null,
              orgDiscountId: testDiscountsResponse[0].id,
              organizationId: testDiscountsResponse[0].organizationId,
              endDate: null,
              startTime: null,
              endTime: null,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              deletedAt: null,
            };
            return HttpResponse.json({
              ...testDiscountsResponse[0],
              coupons: [coupon],
            });
          },
          {
            once: true,
          }
        )
      );

      const {
        getDiscountRequireReasonCheckbox,
        getRequireReasonError,
        loadingSpinner,
        getTitleInput,
      } = renderManualDiscountForm(true);

      await waitFor(() => {
        expect(loadingSpinner()).not.toBeInTheDocument();
      });
      const requireReason = getDiscountRequireReasonCheckbox();

      await waitFor(() => {
        expect(getTitleInput().querySelector("input")!.value).toEqual("Disc 1");
        expect(screen.getByTestId("coupon-panel")).toBeInTheDocument();
      });
      await userEvent.click(requireReason);

      await waitFor(() => {
        const requireReasonError = getRequireReasonError();

        expect(requireReasonError).toHaveTextContent(
          "Require Reason cannot be used with coupons"
        );
      });
    });

    describe("edit mode", () => {
      it("should display a warning alert when editing an org discount", async () => {
        const { getEditDiscountWarningAlert } = renderManualDiscountForm(true);

        await waitFor(() => {
          expect(getEditDiscountWarningAlert()).toBeInTheDocument();
          expect(getEditDiscountWarningAlert()).toHaveTextContent(
            "Custom conditions will not be affected by global condition changes"
          );
          expect(getEditDiscountWarningAlert()).toHaveTextContent(
            "Some stores may have unique conditions distinct from the upcoming global condition changes you are about to make. If these store-specific conditions are present, they will not be affected by modifications to global conditions."
          );
        });
      });

      it("should render a 'Done Editing' button when editing a discount", async () => {
        const { getDoneEditingButton } = renderManualDiscountForm(true);
        expect(getDoneEditingButton()).toBeInTheDocument();
      });
    });

    describe("<CouponForm />", () => {
      it("should add a new <CouponPanel /> when user types a coupon code and clicks 'Add Coupon'", async () => {
        mockUseGetFeatureFlag.mockReturnValue([
          { error: null, loading: false, data: { status: true } },
        ]);

        const newCoupon = "COUPON1";

        const { getAddCouponNameInput, getAddCouponButton } =
          renderManualDiscountForm();
        const input = getAddCouponNameInput().querySelector("input");

        fireEvent.change(input!, { target: { value: newCoupon } });

        await userEvent.click(getAddCouponButton());

        await waitFor(() => {
          expect(screen.queryAllByTestId("coupon-panel")).toHaveLength(1);
        });

        const couponRows = screen.getAllByTestId("coupon-panel");
        const couponCode = couponRows[0].querySelector(
          'input[id="Coupon Name"]'
        );

        expect(couponRows).toHaveLength(1);
        expect(couponCode).toHaveValue(newCoupon);
      });

      it("should remove the <CouponPanel /> when user clicks on Delete", async () => {
        mockUseGetFeatureFlag.mockReturnValue([
          { error: null, loading: false, data: { status: true } },
        ]);

        const newCoupon = "COUPON1";

        const { getAddCouponNameInput, getAddCouponButton } =
          renderManualDiscountForm();
        const input = getAddCouponNameInput().querySelector("input");

        fireEvent.change(input!, { target: { value: newCoupon } });

        await userEvent.click(getAddCouponButton());

        await waitFor(() => {
          expect(screen.queryAllByTestId("coupon-panel")).toHaveLength(1);
        });

        await userEvent.click(screen.getByTestId("coupon-delete-button"));

        const couponRows = screen.queryAllByTestId("coupon-panel");

        expect(couponRows).toHaveLength(0);
      });

      it("should show the coupon space validation when user types an invalid code name", async () => {
        mockUseGetFeatureFlag.mockReturnValue([
          { error: null, loading: false, data: { status: true } },
        ]);

        const newCoupon = "COUPON 1";

        const { getAddCouponNameInput, getAddCouponButton } =
          renderManualDiscountForm();
        const couponNameInput = getAddCouponNameInput();
        const input = couponNameInput.querySelector("input");

        fireEvent.change(input!, { target: { value: newCoupon } });

        await userEvent.click(getAddCouponButton());

        await waitFor(() => {
          expect(
            couponNameInput.querySelector("p[id='Coupon Name-helper-text']")
          ).toHaveTextContent("Spaces are not allowed");
        });
      });

      it("should show the invalid length validation when user types an invalid code name", async () => {
        mockUseGetFeatureFlag.mockReturnValue([
          { error: null, loading: false, data: { status: true } },
        ]);

        const newCoupon = "CO";

        const { getAddCouponNameInput, getAddCouponButton } =
          renderManualDiscountForm();
        const couponNameInput = getAddCouponNameInput();
        const input = couponNameInput.querySelector("input");

        fireEvent.change(input!, { target: { value: newCoupon } });

        await userEvent.click(getAddCouponButton());

        await waitFor(() => {
          expect(
            couponNameInput.querySelector("p[id='Coupon Name-helper-text']")
          ).toHaveTextContent("Length must be between 3 and 255 characters");
        });
      });

      it("should show the invalid character validation when user types an invalid code name", async () => {
        mockUseGetFeatureFlag.mockReturnValue([
          { error: null, loading: false, data: { status: true } },
        ]);

        const newCoupon = "COUPON1@";

        const { getAddCouponNameInput, getAddCouponButton } =
          renderManualDiscountForm();
        const couponNameInput = getAddCouponNameInput();
        const input = couponNameInput.querySelector("input");

        fireEvent.change(input!, { target: { value: newCoupon } });

        await userEvent.click(getAddCouponButton());

        await waitFor(() => {
          expect(
            couponNameInput.querySelector("p[id='Coupon Name-helper-text']")
          ).toHaveTextContent("Invalid characters");
        });
      });

      it("should show the coupon space validation when editing a coupon", async () => {
        mockUseGetFeatureFlag.mockReturnValue([
          { error: null, loading: false, data: { status: true } },
        ]);

        const newCoupon = "COUPON1";

        const { getAddCouponNameInput, getAddCouponButton, getNextStepButton } =
          renderManualDiscountForm();
        const input = getAddCouponNameInput().querySelector("input");

        fireEvent.change(input!, { target: { value: newCoupon } });

        await userEvent.click(getAddCouponButton());

        await waitFor(() => {
          expect(screen.getAllByTestId("coupon-panel")).toBeDefined();
        });

        const couponRows = screen.getAllByTestId("coupon-panel");

        const couponCode = couponRows[0].querySelector(
          'input[id="Coupon Name"]'
        );

        fireEvent.change(couponCode!, {
          target: { value: "COUPON WITH SPACE" },
        });

        await userEvent.click(getNextStepButton());

        await waitFor(() => {
          expect(
            couponRows[0].querySelector("p[id='Coupon Name-helper-text']")
          ).toHaveTextContent("Spaces are not allowed");
        });
      });

      it("should show the coupon duplicated validation when trying to add same coupon", async () => {
        mockUseGetFeatureFlag.mockReturnValue([
          { error: null, loading: false, data: { status: true } },
        ]);

        const newCoupon = "COUPON1";

        const { getAddCouponNameInput, getAddCouponButton } =
          renderManualDiscountForm();
        const input = getAddCouponNameInput().querySelector("input");

        fireEvent.change(input!, { target: { value: newCoupon } });

        await userEvent.click(getAddCouponButton());

        await waitFor(() => {
          expect(screen.getAllByTestId("coupon-panel")).toBeDefined();
        });

        fireEvent.change(input!, { target: { value: newCoupon } });

        await userEvent.click(getAddCouponButton());

        await waitFor(() => {
          expect(
            getAddCouponNameInput().querySelector(
              "p[id='Coupon Name-helper-text']"
            )
          ).toHaveTextContent("Coupons should not be duplicated");
        });
      });
    });
  });

  describe("Step 2: Select Stores", () => {
    it("should display the 2nd step with entities", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getEntitiesBox,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
        expect(getEntitiesBox()).toHaveLength(2);
      });
    });

    it("should be able to go back to the 1st step via stepper", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getDiscountMethodSelect,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(
        getAmountInput().querySelector("input") as HTMLInputElement,
        { target: { value: "20" } }
      );

      const { getByTestId } = screen;

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });

      const firstStepPickerItem = getByTestId(
        "roleaddoredit-stepper-label-step-0"
      );

      await userEvent.click(firstStepPickerItem);

      await waitFor(() => {
        expect(getDiscountMethodSelect()).toBeInTheDocument();
      });
    });

    it("store condition amount field should be equal to the global discount condition when globally reset", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getEntitiesBox,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
        expect(getEntitiesBox()).toHaveLength(2);
      });

      const entityBox = getEntitiesBox()[0];
      await userEvent.click(
        entityBox.querySelector(
          ".MuiAccordionSummary-expandIconWrapper"
        ) as HTMLButtonElement
      );

      const customAmountInput = entityBox.querySelector(
        "input[type=text]"
      ) as HTMLInputElement;
      await waitFor(() => {
        expect(customAmountInput).toBeInTheDocument();
        fireEvent.change(customAmountInput, {
          target: { value: "25" },
        });
      });

      await userEvent.click(
        screen.getByTestId(`store-condition-reset-amount-${entities[0].id}`)
      );

      await waitFor(() => {
        expect(customAmountInput.value).toEqual("20");
      });
    });

    it("should render a success snackbar when a discount is successfully created", async () => {
      const {
        getTitleInput,
        getAmountInput,
        getNextStepButton,
        getReviewStep,
        getReviewCondition,
      } = renderManualDiscountForm(false);

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });

      await userEvent.click(getNextStepButton());
      await userEvent.click(getNextStepButton());
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getReviewStep()).toBeInTheDocument();
        expect(getReviewCondition()).toHaveLength(2);
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(screen.getByTestId("snackbar")).toBeInTheDocument();
        expect(screen.getByTestId("snackbar")).toHaveTextContent(
          `"20% Test Discount" discount has been created`
        );
        expect(mockNavigate).toHaveBeenCalledWith("/manual");
      });
    });

    it("should display stores customizations security settings as checked/unchecked", async () => {
      mockedUseParams.mockReturnValue({
        orgDiscountId: testDiscountsResponse[0].id,
        storeCustomizationId: "disc1-store1",
      });

      renderManualDiscountForm(true);

      const requirePin = screen.getByTestId(
        `store-security-require-manager-pin-${entities[0].id}`
      );
      const requireReason = screen.getByTestId(
        `store-security-require-reason-${entities[0].id}`
      );

      await waitFor(() => {
        expect(requirePin.querySelector("input")).toBeChecked();
        expect(requireReason.querySelector("input")).toBeChecked();
      });
    });

    it("should render the store search input", async () => {
      mockedUseParams.mockReturnValue({
        orgDiscountId: testDiscountsResponse[0].id,
        storeCustomizationId: "disc1-store1",
      });

      const { getSelectStoreStep, getStoreSearchInput } =
        renderManualDiscountForm(true);

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });

      expect(getStoreSearchInput()).toBeInTheDocument();
    });

    describe("edit mode", () => {
      it("should display the 2nd step when editing an store condition", async () => {
        mockedUseParams.mockReturnValue({
          orgDiscountId: testDiscountsResponse[0].id,
          storeCustomizationId: "disc1-store1",
        });

        const { getSelectStoreStep, loadingSpinner } =
          renderManualDiscountForm(true);

        await waitFor(() => {
          expect(loadingSpinner()).not.toBeInTheDocument();
          expect(getSelectStoreStep()).toBeInTheDocument();
          expect(
            screen.getByTestId(`styled-store-customization-box-entity-1`)
          ).toBeInTheDocument();
        });
      });

      it("should render the 'Done Editing' button", async () => {
        mockedUseParams.mockReturnValue({
          orgDiscountId: testDiscountsResponse[0].id,
          storeCustomizationId: "disc1-store1",
        });

        const { getSelectStoreStep, getDoneEditingButton } =
          renderManualDiscountForm(true);

        await waitFor(() => {
          expect(getSelectStoreStep()).toBeInTheDocument();
          expect(getDoneEditingButton()).toBeInTheDocument();
        });
      });

      it("should display the line item option checked when editing a discount with line item type", async () => {
        mockedUseParams.mockReturnValue({
          orgDiscountId: testDiscountsResponse[0].id,
        });

        server.use(
          http.get(`${upsertDiscountUrl}/:id`, () =>
            HttpResponse.json({
              ...testDiscountsResponse[0],
              isCart: false,
            })
          )
        );

        const { loadingSpinner } = renderManualDiscountForm(true);
        const discountTypesRadios = screen
          .getByTestId("discount-types-radio-group")
          .querySelectorAll(".PrivateSwitchBase-input");

        await waitFor(() => {
          expect(loadingSpinner()).not.toBeInTheDocument();
          expect(discountTypesRadios[1].getAttribute("value")).toEqual("true");
        });
      });

      it("should render a success snackbar when a discount is successfully edited", async () => {
        mockedUseParams.mockReturnValue({
          orgDiscountId: testDiscountsResponse[0].id,
          storeCustomizationId: "disc1-store1",
        });

        const {
          getSelectStoreStep,
          getNextStepButton,
          getReviewStep,
          loadingSpinner,
        } = renderManualDiscountForm(true);

        await waitFor(() => {
          expect(loadingSpinner()).not.toBeInTheDocument();
          expect(getSelectStoreStep()).toBeInTheDocument();
        });

        await userEvent.click(getNextStepButton());
        await userEvent.click(getNextStepButton());

        await waitFor(() => {
          expect(getReviewStep()).toBeInTheDocument();
        });

        await userEvent.click(getNextStepButton());

        await waitFor(() => {
          expect(screen.getByTestId("snackbar")).toBeInTheDocument();
          expect(screen.getByTestId("snackbar")).toHaveTextContent(
            `"Disc 1" discount has been updated`
          );
          expect(mockNavigate).toHaveBeenCalledWith("/manual");
        });
      });

      it("should render an error snackbar when an unexpected error occurs during discount edit", async () => {
        server.use(
          http.post(
            upsertDiscountUrl,
            () => new HttpResponse(null, { status: 401 }),
            {
              once: true,
            }
          )
        );

        const {
          getNextStepButton,
          getTitleInput,
          getAmountInput,
          getReviewStep,
          getReviewCondition,
        } = renderManualDiscountForm(false);

        fireEvent.change(getTitleInput().querySelector("input")!, {
          target: { value: "20% Test Discount" },
        });
        fireEvent.change(getAmountInput().querySelector("input")!, {
          target: { value: "20" },
        });

        await userEvent.click(getNextStepButton());
        await userEvent.click(getNextStepButton());
        await userEvent.click(getNextStepButton());

        await waitFor(() => {
          expect(getReviewStep()).toBeInTheDocument();
          expect(getReviewCondition()).toHaveLength(2);
        });

        await userEvent.click(getNextStepButton());

        await waitFor(() => {
          expect(screen.getByTestId("snackbar")).toBeInTheDocument();
          expect(screen.getByTestId("snackbar")).toHaveTextContent(
            "Unable to create the discount. Please try again"
          );
        });
      });

      it("should render an error snackbar on API failure during discount edit", async () => {
        server.use(
          http.post(
            upsertDiscountUrl,
            () => {
              throw new AxiosError(
                "User does not have permissions to make this request",
                "403",
                {},
                {},
                {
                  data: {
                    errorType: 6,
                    errorMsgs: [
                      "User does not have permissions to make this request",
                    ],
                  },
                  status: 403,
                  statusText: "",
                  headers: {},
                  request: {},
                  config: {},
                }
              );
            },
            {
              once: true,
            }
          )
        );

        const {
          getNextStepButton,
          getTitleInput,
          getAmountInput,
          getReviewStep,
          getReviewCondition,
        } = renderManualDiscountForm(false);

        fireEvent.change(getTitleInput().querySelector("input")!, {
          target: { value: "20% Test Discount" },
        });
        fireEvent.change(getAmountInput().querySelector("input")!, {
          target: { value: "20" },
        });

        await userEvent.click(getNextStepButton());
        await userEvent.click(getNextStepButton());
        await userEvent.click(getNextStepButton());

        await waitFor(() => {
          expect(getReviewStep()).toBeInTheDocument();
          expect(getReviewCondition()).toHaveLength(2);
        });

        await userEvent.click(getNextStepButton());

        await waitFor(() => {
          expect(screen.getByTestId("snackbar")).toBeInTheDocument();
          expect(screen.getByTestId("snackbar")).toHaveTextContent(
            "Your request could not be completed due to a network error"
          );
        });
      });
    });

    describe("tooltips", () => {
      it("should display the reset amount tooltip", async () => {
        mockedUseParams.mockReturnValue({
          orgDiscountId: testDiscountsResponse[0].id,
          storeCustomizationId: "disc1-store1",
        });

        const { getSelectStoreStep, loadingSpinner } =
          renderManualDiscountForm(true);

        await waitFor(() => {
          expect(loadingSpinner()).not.toBeInTheDocument();
        });

        await waitFor(() => {
          expect(getSelectStoreStep()).toBeInTheDocument();
          expect(screen.getByText("$10")).toBeInTheDocument();
        });

        const resetAmountIconButton = screen.getByTestId(
          `store-condition-reset-amount-${entities[0].id}`
        );
        const resetAmountTooltip = screen.getByTestId(
          `store-condition-reset-amount-tooltip-${entities[0].id}`
        );

        fireEvent.mouseOver(resetAmountIconButton);
        expect(resetAmountTooltip).toBeInTheDocument();
      });

      it("should display the reset require pin tooltip", async () => {
        mockedUseParams.mockReturnValue({
          orgDiscountId: testDiscountsResponse[0].id,
          storeCustomizationId: "disc1-store1",
        });

        const { getSelectStoreStep, loadingSpinner } =
          renderManualDiscountForm(true);

        await waitFor(() => {
          expect(loadingSpinner()).not.toBeInTheDocument();
        });

        await waitFor(() => {
          expect(getSelectStoreStep()).toBeInTheDocument();
          expect(screen.getByText("$10")).toBeInTheDocument();
        });

        const resetRequirePinIconButton = screen.getByTestId(
          `store-condition-reset-require-pin-${entities[0].id}`
        );
        const resetRequirePinTooltip = screen.getByTestId(
          `store-condition-reset-require-pin-tooltip-${entities[0].id}`
        );

        fireEvent.mouseOver(resetRequirePinIconButton);
        expect(resetRequirePinTooltip).toBeInTheDocument();
      });

      it("should display the require reason tooltip", async () => {
        mockedUseParams.mockReturnValue({
          orgDiscountId: testDiscountsResponse[0].id,
          storeCustomizationId: "disc1-store1",
        });

        const { getSelectStoreStep, loadingSpinner } =
          renderManualDiscountForm(true);

        await waitFor(() => {
          expect(loadingSpinner()).not.toBeInTheDocument();
        });

        await waitFor(() => {
          expect(getSelectStoreStep()).toBeInTheDocument();
          expect(screen.getByText("$10")).toBeInTheDocument();
        });

        const resetRequireReasonIconButton = screen.getByTestId(
          `store-condition-reset-require-reason-${entities[0].id}`
        );
        const resetRequireReasonTooltip = screen.getByTestId(
          `store-condition-reset-require-reason-tooltip-${entities[0].id}`
        );

        await waitFor(() => {
          expect(getSelectStoreStep()).toBeInTheDocument();
        });

        fireEvent.mouseOver(resetRequireReasonIconButton);
        expect(resetRequireReasonTooltip).toBeInTheDocument();
      });

      it("should display the reset button tooltip", async () => {
        mockedUseParams.mockReturnValue({
          orgDiscountId: testDiscountsResponse[0].id,
          storeCustomizationId: "disc1-store1",
        });

        const { getSelectStoreStep, loadingSpinner } =
          renderManualDiscountForm(true);

        await waitFor(() => {
          expect(loadingSpinner()).not.toBeInTheDocument();
        });

        await waitFor(() => {
          expect(getSelectStoreStep()).toBeInTheDocument();
          expect(screen.getByText("$10")).toBeInTheDocument();
        });

        const resetButton = screen.getByTestId(
          `store-condition-global-reset-${entities[0].id}`
        );
        const resetButtonTooltip = screen.getByTestId(
          `reset-button-tooltip-${entities[0].id}`
        );

        await waitFor(() => {
          expect(getSelectStoreStep()).toBeInTheDocument();
        });

        fireEvent.mouseOver(resetButton);
        expect(resetButtonTooltip).toBeInTheDocument();
      });
    });
  });

  describe("Step 3: Set Conditions", () => {
    it("should display the 3rd step with entities", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getSetConditionsStep,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSetConditionsStep()).toBeInTheDocument();
      });
    });

    it("should be able to go back to the previous steps via stepper", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getDiscountMethodSelect,
        getSetConditionsStep,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(
        getAmountInput().querySelector("input") as HTMLInputElement,
        { target: { value: "20" } }
      );

      const { getByTestId } = screen;

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSetConditionsStep()).toBeInTheDocument();
      });

      const secondStepPickerItem = getByTestId(
        "roleaddoredit-stepper-label-step-1"
      );

      await userEvent.click(secondStepPickerItem);

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });

      const firstStepPickerItem = getByTestId(
        "roleaddoredit-stepper-label-step-0"
      );

      await userEvent.click(firstStepPickerItem);

      await waitFor(() => {
        expect(getDiscountMethodSelect()).toBeInTheDocument();
      });
    });

    it("should render a success snackbar when a discount is successfully created", async () => {
      const {
        getTitleInput,
        getAmountInput,
        getNextStepButton,
        getReviewStep,
        getReviewCondition,
      } = renderManualDiscountForm(false);

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });

      await userEvent.click(getNextStepButton());
      await userEvent.click(getNextStepButton());
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getReviewStep()).toBeInTheDocument();
        expect(getReviewCondition()).toHaveLength(2);
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(screen.getByTestId("snackbar")).toBeInTheDocument();
        expect(screen.getByTestId("snackbar")).toHaveTextContent(
          `"20% Test Discount" discount has been created`
        );
        expect(mockNavigate).toHaveBeenCalledWith("/manual");
      });
    });

    it("should render the conditions dropdown with options", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getSetConditionsStep,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSetConditionsStep()).toBeInTheDocument();
      });

      const dropdownChip = screen.getByTestId("conditions-list");
      expect(dropdownChip).toBeInTheDocument();

      await userEvent.click(dropdownChip);

      await waitFor(() => {
        const dropdownOptions = screen.getAllByTestId("menuitemfilter-option");
        const optionLabels = dropdownOptions.map((option) => option.textContent);

        expect(optionLabels).toContain("Per-Customer Limit");
        expect(optionLabels).toContain("Purchase Minimum Requirement");
        expect(optionLabels).toContain("Redemption Limit");
      });
    });

    it("should render accordion panels when conditions are selected", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getSetConditionsStep,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSetConditionsStep()).toBeInTheDocument();
      });

      const dropdownChip = screen.getByTestId("conditions-list");
      expect(dropdownChip).toBeInTheDocument();

      await userEvent.click(dropdownChip);

      const option = screen.getByText("Per-Customer Limit");
      expect(option).toBeInTheDocument();

      await userEvent.click(option);

      await waitFor(() => {
        const accordion = screen.getByTestId("per-customer-condition-accordion-panel");
        expect(accordion).toBeInTheDocument();
        expect(accordion).toHaveTextContent("Per-Customer Limit");
      });
    });
  });

  describe("Step 4: Review", () => {
    it("should display the 4th step", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getSetConditionsStep,
        getReviewStep,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(
        getAmountInput().querySelector("input") as HTMLInputElement,
        { target: { value: "20" } }
      );
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSetConditionsStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getReviewStep()).toBeInTheDocument();
      });
    });

    it("should display the 4th step with store customization", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getEntitiesBox,
        getReviewStep,
        getReviewCondition,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
        expect(getEntitiesBox()).toHaveLength(2);
      });

      const entityBox = getEntitiesBox()[0];

      await userEvent.click(
        entityBox.querySelector(
          ".MuiAccordionSummary-expandIconWrapper"
        ) as HTMLElement
      );

      await waitFor(() => {
        const customAmountInput = entityBox.querySelector("input[type=text]");
        expect(customAmountInput).toBeInTheDocument();
        fireEvent.change(customAmountInput as HTMLInputElement, {
          target: { value: "25" },
        });
      });

      await userEvent.click(getNextStepButton());
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getReviewStep()).toBeInTheDocument();
        expect(getReviewCondition()).toHaveLength(2);
        expect(screen.getAllByTestId("review-condition-row")).toHaveLength(2);
      });
    });

    it("should display the 4th step with coupons", async () => {
      mockUseGetFeatureFlag.mockReturnValue([
        { error: null, loading: false, data: { status: true } },
      ]);

      const newCoupon = "COUPONEXAMPLE1";

      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getReviewStep,
        getReviewCondition,
        getAddCouponNameInput,
        getAddCouponButton,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });

      const input = getAddCouponNameInput().querySelector("input");

      fireEvent.change(input!, { target: { value: newCoupon } });

      await userEvent.click(getAddCouponButton());

      await waitFor(() => {
        expect(screen.queryAllByTestId("coupon-panel")).toHaveLength(1);
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getReviewStep()).toBeInTheDocument();
        expect(getReviewCondition()).toHaveLength(2);

        const couponPanelCode = screen.getByTestId(
          `review-coupon-panel-${newCoupon}-code`
        );

        expect(couponPanelCode).toHaveTextContent(newCoupon);
      });
    });

    it("should display the 4th step with set conditions", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getSetConditionsStep,
        getReviewStep,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSetConditionsStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());
      await waitFor(() => {
        expect(getReviewStep()).toBeInTheDocument();
      });

      const reviewGrid = screen.queryByTestId("review-discount-grid");
      expect(reviewGrid).toBeInTheDocument();
    });

    it("should display one record with store customizations in the review section", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getSetConditionsStep,
        getEntitiesBox,
        getReviewStep,
        getReviewCondition,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
        expect(getEntitiesBox()).toHaveLength(2);
      });

      const entityBox = getEntitiesBox()[0];
      await userEvent.click(
        entityBox.querySelector(
          ".MuiAccordionSummary-expandIconWrapper"
        ) as HTMLButtonElement
      );

      await waitFor(() => {
        const customAmountInput = entityBox.querySelector("input[type=text]");
        expect(customAmountInput).toBeInTheDocument();
        fireEvent.change(customAmountInput as HTMLInputElement, {
          target: { value: "25" },
        });
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSetConditionsStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getReviewStep()).toBeInTheDocument();
        expect(getReviewCondition()).toHaveLength(2);
        const storeCustomizationAmounts = screen.getAllByTestId(
          "review-condition-amount-col"
        );

        expect(
          screen.getByTestId("custom-store-review-discount-title")
        ).toHaveTextContent("1 store(s) using different conditions");
        expect(storeCustomizationAmounts[0]).toHaveTextContent("$ 25");
      });
    });

    it("should display store conditions as not required in table view", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getSetConditionsStep,
        getReviewStep,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(
        getAmountInput().querySelector("input") as HTMLInputElement,
        { target: { value: "20" } }
      );
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSetConditionsStep()).toBeInTheDocument();
      });

      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        const requireReasons = screen.getAllByTestId(
          "review-condition-require-reason-col"
        );

        const requirePins = screen.getAllByTestId(
          "review-condition-require-pin-col"
        );

        expect(getReviewStep()).toBeInTheDocument();
        expect(requireReasons[0]).toHaveTextContent("Not required");
        expect(requirePins[0]).toHaveTextContent("Not required");
      });
    });

    it.skip("should submit the form with consistent properties", async () => {
      const {
        getNextStepButton,
        getTitleInput,
        getAmountInput,
        getSelectStoreStep,
        getSetConditionsStep,
        getReviewStep,
      } = renderManualDiscountForm();

      fireEvent.change(getTitleInput().querySelector("input")!, {
        target: { value: "20% Test Discount" },
      });
      fireEvent.change(getAmountInput().querySelector("input")!, {
        target: { value: "20" },
      });
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSelectStoreStep()).toBeInTheDocument();
      });
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getSetConditionsStep()).toBeInTheDocument();
      });
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(getReviewStep()).toBeInTheDocument();
      });
      await userEvent.click(getNextStepButton());

      await waitFor(() => {
        expect(mockApiService.post).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            displayTitle: null,
            isActive: true,
            isManual: true,
            isAdjustment: false,
          })
        );
      });
    });
  });

  describe("feature flag", () => {
    it("should show require coupon when feature flag is enabled and is cart option selected", () => {
      mockUseGetFeatureFlag.mockReturnValue([
        { error: null, loading: false, data: { status: true } },
      ]);
      const { getDiscountRequireCouponCheckbox } = renderManualDiscountForm();

      const discountRequireCouponCheckbox = getDiscountRequireCouponCheckbox();

      expect(discountRequireCouponCheckbox).toBeInTheDocument();
    });

    it("should hide require coupon when feature flag is enabled and is cart option unselected", async () => {
      mockUseGetFeatureFlag.mockReturnValue([
        { error: null, loading: false, data: { status: true } },
      ]);
      const { getDiscountRequireCouponCheckbox } = renderManualDiscountForm();

      const discountTypesRadios = screen
        .getByTestId("discount-types-radio-group")
        .querySelectorAll(".MuiRadio-root");

      await userEvent.click(discountTypesRadios[1]);

      expect(getDiscountRequireCouponCheckbox).toHaveLength(0);
    });

    it("shows require coupon when FFL is enabled and is cart option selected", () => {
      mockUseGetFeatureFlag.mockReturnValue([
        { error: null, loading: false, data: { status: true } },
      ]);
      const { getDiscountRequireCouponCheckbox } = renderManualDiscountForm();

      const discountRequireCouponCheckbox = getDiscountRequireCouponCheckbox();

      expect(discountRequireCouponCheckbox).toBeInTheDocument();
    });

    it("hide require coupon when FFL is enabled and is cart option unselected", async () => {
      mockUseGetFeatureFlag.mockReturnValue([
        { error: null, loading: false, data: { status: true } },
      ]);
      renderManualDiscountForm();

      const discountTypesRadios = screen
        .getByTestId("discount-types-radio-group")
        .querySelectorAll(".MuiRadio-root");

      await userEvent.click(discountTypesRadios[1]);

      expect(
        screen.queryAllByTestId("discount-require-coupon-checkbox")
      ).toHaveLength(0);
    });
  });

  describe("loading spinner", () => {
    it("should render the loading spinner when page is loading", async () => {
      mockSetIsPageLoading.mockReturnValue({
        isPageLoading: true,
        setIsPageLoading: jest.fn(),
      });
      const { loadingSpinner } = renderManualDiscountForm();
      await waitFor(() => {
        expect(loadingSpinner()).toBeInTheDocument();
      });
    });

    it("should not render the loading spinner when page is NOT loading", async () => {
      mockSetIsPageLoading.mockReturnValue({
        isPageLoading: false,
        setIsPageLoading: jest.fn(),
      });
      const { loadingSpinner } = renderManualDiscountForm();
      await waitFor(() => {
        expect(loadingSpinner()).not.toBeInTheDocument();
      });
    });
  });
});
