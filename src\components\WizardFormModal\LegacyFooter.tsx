import React, { ReactNode } from "react";
import { Box, styled } from "@mui/material";
import { convertPxToRem } from "@treez-inc/component-library";

// TODO: Refactor ManualDiscounts to use the new Footer component then remove this OldFooter
interface LegacyFooterProps {
  children: ReactNode;
}

const FooterWrapper = styled(Box)(({ theme }) => ({
  flex: `0 0 ${convertPxToRem(84)}`,
  borderTop: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
}));

const ButtonsWrapper = styled(Box)(() => ({
  maxWidth: convertPxToRem(900),
  height: "100%",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
}));

const LegacyFooter: React.FC<LegacyFooterProps> = ({ children }) => (
  <FooterWrapper>
    <ButtonsWrapper>{children}</ButtonsWrapper>
  </FooterWrapper>
);

export default LegacyFooter;
