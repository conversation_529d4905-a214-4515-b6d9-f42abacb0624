import React from "react";
import { render, screen } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import AutomatedDiscountsForm from "../../index";
import ApiService from "../../../../../services/api/apiService";
import { defaultAutomatedDiscountFormValues as defaultValues } from "../../../../../constants/discountForm";
import { Permissions } from "../../../../../constants";

// Mock dependencies
jest.mock("react-router-dom", () => ({
  useParams: () => ({ orgDiscountId: undefined }),
  useNavigate: () => jest.fn(),
}));

jest.mock("../../../../../hooks", () => ({
  usePageLoading: () => [false, jest.fn()],
  useGetEntities: () => ({ data: [] }),
  useSnackbar: () => ({ openSnackbar: jest.fn() }),
}));

jest.mock("../../../../../providers/EntityProvider", () => ({
  EntityContext: {
    Provider: ({ children }: { children: React.ReactNode }) => children,
  },
}));

// Mock API Service
const mockApiService = {
  getTokens: () => ({
    accessToken: "mock-token",
    refreshToken: "mock-refresh-token",
    expiresIn: 3600,
    idToken: "mock-id-token"
  }),
} as ApiService;

// Mock parseJwt
jest.mock("../../../../../utils", () => ({
  parseJwt: () => ({
    orgId: "test-org-id",
    email: "<EMAIL>",
    name: "Test User"
  }),
  getBannerTitle: jest.fn(),
  getNextStepButtonLabel: jest.fn(),
}));

// Mock the useAutomatedDiscountsForm hook
jest.mock("../../utils/useAutomatedDiscountsForm", () => ({
  useAutomatedDiscountsForm: () => ({
    methods: {
      handleSubmit: jest.fn(),
      trigger: jest.fn(),
    },
    activeStep: 2, // Set to conditions step
    existingDiscount: null,
    customerGroups: [
      {
        id: "group-1",
        name: "VIP Customers",
        tagGroupId: "tag-group-1",
        organizationId: "org-1",
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01"
      }
    ],
    steps: [
      { label: "Discount Details" },
      { label: "Product Collections" },
      { label: "Set Conditions" },
      { label: "Review" }
    ],
    handleNext: jest.fn(),
    handleBack: jest.fn(),
    onSubmit: jest.fn(),
  }),
}));

// Mock renderForm to return SetConditionsStep for conditions step
jest.mock("../../utils/renderForm", () => ({
  renderForm: (step: string, props: any) => {
    if (step === "Set Conditions") {
      const SetConditionsStep = require("./index").default;
      return <SetConditionsStep {...props} />;
    }
    return <div data-testid={`mock-${step.toLowerCase().replace(/\s+/g, '-')}-step`} />;
  },
}));

const createMockQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });
};

describe("AutomatedDiscountsForm - Permission Integration", () => {
  let queryClient: QueryClient;

  const renderAutomatedDiscountsForm = (permissions: {
    read: boolean;
    write: boolean;
    manageTags: boolean;
  }) => {
    const FormWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
      const methods = useForm({ defaultValues });
      return <FormProvider {...methods}>{children}</FormProvider>;
    };

    return render(
      <TreezThemeProvider>
        <QueryClientProvider client={queryClient}>
          <FormWrapper>
            <AutomatedDiscountsForm
              api={mockApiService}
              permissions={permissions}
              isEditMode={false}
            />
          </FormWrapper>
        </QueryClientProvider>
      </TreezThemeProvider>
    );
  };

  beforeEach(() => {
    queryClient = createMockQueryClient();
  });

  describe("Permission-based Customer Group Management", () => {
    it("should show manage customer groups section when user has manageTags permission", () => {
      renderAutomatedDiscountsForm({
        read: true,
        write: true,
        manageTags: true
      });

      // Should show refresh button
      expect(screen.getByLabelText("Refresh Customer Groups")).toBeInTheDocument();
      
      // Should show manage customer groups link
      expect(screen.getByText("Manage Customer Groups")).toBeInTheDocument();
    });

    it("should hide manage customer groups section when user lacks manageTags permission", () => {
      renderAutomatedDiscountsForm({
        read: true,
        write: true,
        manageTags: false
      });

      // Should not show refresh button
      expect(screen.queryByLabelText("Refresh Customer Groups")).not.toBeInTheDocument();
      
      // Should not show manage customer groups link
      expect(screen.queryByText("Manage Customer Groups")).not.toBeInTheDocument();
    });

    it("should still show customer group autocomplete regardless of manageTags permission", () => {
      // Test with manageTags: false
      const { rerender } = renderAutomatedDiscountsForm({
        read: true,
        write: true,
        manageTags: false
      });

      expect(screen.getByLabelText("Customer Group")).toBeInTheDocument();

      // Test with manageTags: true
      const FormWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
        const methods = useForm({ defaultValues });
        return <FormProvider {...methods}>{children}</FormProvider>;
      };

      rerender(
        <TreezThemeProvider>
          <QueryClientProvider client={queryClient}>
            <FormWrapper>
              <AutomatedDiscountsForm
                api={mockApiService}
                permissions={{
                  read: true,
                  write: true,
                  manageTags: true
                }}
                isEditMode={false}
              />
            </FormWrapper>
          </QueryClientProvider>
        </TreezThemeProvider>
      );

      expect(screen.getByLabelText("Customer Group")).toBeInTheDocument();
    });
  });

  describe("Permission Prop Passing", () => {
    it("should correctly pass canManageTags prop to SetConditionsStep", () => {
      // This test verifies that the permission flows correctly from 
      // AutomatedDiscountsForm -> renderForm -> SetConditionsStep
      
      renderAutomatedDiscountsForm({
        read: true,
        write: true,
        manageTags: true
      });

      // Verify the management section is rendered (indicating canManageTags=true was passed)
      expect(screen.getByText("Manage Customer Groups")).toBeInTheDocument();
    });

    it("should handle undefined manageTags permission gracefully", () => {
      // Test edge case where manageTags might be undefined
      const permissionsWithoutManageTags = {
        read: true,
        write: true
      } as any; // Type assertion to simulate missing property

      const FormWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
        const methods = useForm({ defaultValues });
        return <FormProvider {...methods}>{children}</FormProvider>;
      };

      render(
        <TreezThemeProvider>
          <QueryClientProvider client={queryClient}>
            <FormWrapper>
              <AutomatedDiscountsForm
                api={mockApiService}
                permissions={permissionsWithoutManageTags}
                isEditMode={false}
              />
            </FormWrapper>
          </QueryClientProvider>
        </TreezThemeProvider>
      );

      // Should not show management section when manageTags is undefined
      expect(screen.queryByText("Manage Customer Groups")).not.toBeInTheDocument();
    });
  });

  describe("Edit Mode Behavior", () => {
    it("should maintain permission behavior in edit mode", () => {
      const FormWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
        const methods = useForm({ defaultValues });
        return <FormProvider {...methods}>{children}</FormProvider>;
      };

      render(
        <TreezThemeProvider>
          <QueryClientProvider client={queryClient}>
            <FormWrapper>
              <AutomatedDiscountsForm
                api={mockApiService}
                permissions={{
                  read: true,
                  write: true,
                  manageTags: true
                }}
                isEditMode={true}
              />
            </FormWrapper>
          </QueryClientProvider>
        </TreezThemeProvider>
      );

      // Should still show management section in edit mode
      expect(screen.getByText("Manage Customer Groups")).toBeInTheDocument();
    });
  });
});
