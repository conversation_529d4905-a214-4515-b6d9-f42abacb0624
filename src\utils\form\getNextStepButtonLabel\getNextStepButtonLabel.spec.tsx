import { getNextStepButtonLabel } from ".";

describe("getNextStepButtonLabel", () => {
  it("should return 'Finish' when currentStep matches the length of the total steps", () => {
    const label = getNextStepButtonLabel(2, 3, false);

    expect(label).toEqual("Finish");
  });

  it("should return 'Next' when currentStep is less than the length of the total steps", () => {
    const label = getNextStepButtonLabel(1, 3, false);

    expect(label).toEqual("Next");
  });

  describe("EditMode", () => {
    it("should return 'Done Editing' when currentStep matches the length of the total steps", () => {
      const label = getNextStepButtonLabel(2, 3, true);

      expect(label).toEqual("Done Editing");
    });

    it("should return 'Next' when currentStep is less than the length of the total steps", () => {
      const label = getNextStepButtonLabel(1, 3, true);

      expect(label).toEqual("Next");
    });
  });
});
