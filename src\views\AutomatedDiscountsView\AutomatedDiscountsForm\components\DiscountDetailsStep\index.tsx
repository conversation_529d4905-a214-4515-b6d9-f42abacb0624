import React, { useEffect } from "react";
import { ObjectType } from "@treez-inc/file-management";
import { Controller, useFormContext } from "react-hook-form";
import { styled, Box, Typography, Grid } from "@mui/material";
import { v4 as uuidv4 } from "uuid";
import {
  convertPxToRem,
  Checkbox,
  Input,
  Panel,
  Select,
  Tooltip,
  Icon,
  Switch,
} from "@treez-inc/component-library";
import { AUTOMATED_DISCOUNT_METHODS } from "../../../../../constants/discountForm";
import { validateDiscountAmountInput } from "../../../../../utils/validations";
import { AutomatedDiscountMethods } from "../../../../../constants/discounts";
import Images from "../../../../../components/Images";
import ApiService from "../../../../../services/api/apiService";
import buildImageUrl from "../../../../../utils/images";
import { DisplayChannels } from "../../../../../interfaces/discounts";

interface DiscountDetailsStepProps {
  api: ApiService;
}

const StyledDiscountDetailsStep = styled(Box)(() => ({
  minWidth: convertPxToRem(280),
  maxWidth: convertPxToRem(620),
  // panel
  "> div": {
    marginBottom: convertPxToRem(24),
  },
}));

const StyledDetailsContainer = styled(Box)(() => ({
  display: "flex",
  flexDirection: "column",
  gap: convertPxToRem(16),
}));

const StyledStackableContainer = styled(Box)(() => ({
  display: "flex",
  flexDirection: "column",
}));

const StyledInnerSectionContainer = styled(Box)(() => ({
  display: "flex",
  flexDirection: "column",
}));

const StyledDisplayChannelContainer = styled(Box)(() => ({
  display: "flex",
  gap: convertPxToRem(18),
}));

const DiscountDetailsStep = ({ api }: DiscountDetailsStepProps) => {
  const { control, watch, trigger, getValues, setValue } = useFormContext();
  const discountMethod = watch("method");
  const imageUrl = watch("imageUrl");
  const { discountId, organizationId } = getValues();

  const discountLabels = Object.values(AUTOMATED_DISCOUNT_METHODS);

  const methodsMenuItems = discountLabels.map((label) => ({
    displayValue: label,
    displayName: label,
  }));

  useEffect(() => {
    if (getValues().amount) {
      trigger("amount");
    }
  }, [discountMethod]);

  const METHOD_TO_DESCRIPTION = {
    [AUTOMATED_DISCOUNT_METHODS[AutomatedDiscountMethods.DOLLAR]]:
      "Automated dollar discounts will not be applied to bulk products. Manual discounts can still be applied",
    [AUTOMATED_DISCOUNT_METHODS[AutomatedDiscountMethods.COST_PLUS]]:
      "The discounted price is the wholesale cost plus a percentage.",
    [AUTOMATED_DISCOUNT_METHODS[AutomatedDiscountMethods.BOGO]]:
      "The BOGO discount terms ($ and % off or Priced At) are defined in the next step.",
    [AUTOMATED_DISCOUNT_METHODS[AutomatedDiscountMethods.BUNDLE]]:
      "A bundle discount applies to a group of products if the purchase requirement is met for those same products. The discount terms ($ and % off or Price At) are defined in the next step.",
  };

  const shouldDisplayAmountField = () =>
    discountMethod !== AUTOMATED_DISCOUNT_METHODS.BOGO &&
    discountMethod !== AUTOMATED_DISCOUNT_METHODS.BUNDLE;

  const handleDeleteImageAction = () => {
    setValue("imageUrl", null);
  };

  const handleNewImageAction = (objectId: string) => {
    setValue("imageUrl", buildImageUrl(organizationId, objectId));
  };

  const handleDisplayChannelsChange = (
    currentValue: DisplayChannels,
    fieldName: string,
    checked: boolean
  ) => ({
    ...currentValue,
    [fieldName]: checked,
  });

  return (
    <StyledDiscountDetailsStep data-testid="automated-discounts-details-step">
      <Panel
        testId="automated-discount-details-panel"
        title="Automated Discount Details"
      >
        <StyledDetailsContainer>
          <Controller
            name="title"
            control={control}
            rules={{ value: true, required: "Discount title is required" }}
            render={({ field, fieldState: { error } }) => {
              // Treez Input component doesn't accept a ref prop
              const { ref, ...rest } = field;
              return (
                <Input
                  {...rest}
                  testId="discount-title-input"
                  label="Discount Title"
                  helperText={error?.message ? error.message : ""}
                  error={!!error}
                  required
                />
              );
            }}
          />

          <Controller
            name="displayTitle"
            control={control}
            render={({ field, fieldState: { error } }) => {
              // Treez Input component doesn't accept a ref prop
              const { ref, ...rest } = field;
              return (
                <Input
                  {...rest}
                  testId="discount-display-title-input"
                  label="Display Title"
                  helperText={error?.message ? error.message : ""}
                  error={!!error}
                />
              );
            }}
          />

          <Controller
            name="description"
            control={control}
            render={({ field, fieldState: { error } }) => {
              // Treez Input component doesn't accept a ref prop
              const { ref, ...rest } = field;
              return (
                <Input
                  {...rest}
                  testId="discount-description-input"
                  label="Description"
                  helperText={error?.message ? error.message : ""}
                  error={!!error}
                  multiline
                />
              );
            }}
          />

          <StyledInnerSectionContainer>
            <Typography variant="largeTextStrong" color="secondaryText">
              Image
            </Typography>

            <Typography
              variant="mediumText"
              color="secondaryText"
              style={{ marginBottom: convertPxToRem(14) }}
            >
              Upload an image for the discount.
            </Typography>

            <Images
              api={api}
              objectId={discountId || uuidv4()}
              objectType={ObjectType.DISCOUNT_IMAGE}
              imageUrl={imageUrl}
              onDeleteImage={handleDeleteImageAction}
              onNewImageCreated={handleNewImageAction}
            />
          </StyledInnerSectionContainer>

          <StyledInnerSectionContainer>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box display="flex" alignItems="center">
                  <Typography variant="largeTextStrong" color="secondaryText">
                    Display Image Only
                  </Typography>
                  <Box ml={1}>
                    <Tooltip
                      placement="right"
                      themeColor="light"
                      title="When disabled, the discount title and a description will be displayed on top of the uploaded image by default."
                      variant="multiRow"
                    >
                      <Icon iconName="InfoOutlined" />
                    </Tooltip>
                  </Box>
                </Box>

                <Controller
                  name="displayImageOnly"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <Switch
                      value="On"
                      onChange={(checked) => onChange(checked)}
                      checked={value}
                      testId="display-image-only-switch"
                    />
                  )}
                />
              </Box>
          </StyledInnerSectionContainer>

          <StyledInnerSectionContainer>
            <Typography variant="largeTextStrong" color="secondaryText">
              Display Channels
            </Typography>

            <Typography
              variant="mediumText"
              color="secondaryText"
              style={{ marginBottom: convertPxToRem(14) }}
            >
              This setting controls visibility only and does not affect how the
              discount is applied.
            </Typography>
            <Controller
              name="displayChannels"
              control={control}
              render={({ field, fieldState: { error } }) => {
                // Treez Checkbox component doesn't accept a ref prop
                const { ref, value, onChange, ...rest } = field;
                return (
                  <StyledDisplayChannelContainer>
                    <Checkbox
                      {...rest}
                      onChange={(checked) =>
                        onChange(
                          handleDisplayChannelsChange(
                            value,
                            "ecommerce",
                            checked!
                          )
                        )
                      }
                      label="Treez eCommerce"
                      value={value.ecommerce}
                      checked={value.ecommerce}
                      error={!!error}
                    />
                    <Checkbox
                      {...rest}
                      onChange={(checked) =>
                        onChange(
                          handleDisplayChannelsChange(
                            value,
                            "sellTreez",
                            checked!
                          )
                        )
                      }
                      label="SellTreez POS"
                      value={value.sellTreez}
                      checked={value.sellTreez}
                      error={!!error}
                    />
                    <Checkbox
                      {...rest}
                      onChange={(checked) =>
                        onChange(
                          handleDisplayChannelsChange(
                            value,
                            "customerFacing",
                            checked!
                          )
                        )
                      }
                      label="Customer Facing Display"
                      value={value.customerFacing}
                      checked={value.customerFacing}
                      error={!!error}
                    />
                  </StyledDisplayChannelContainer>
                );
              }}
            />
          </StyledInnerSectionContainer>
        </StyledDetailsContainer>
      </Panel>

      <Panel
        testId="discount-method-panel"
        title="Discount Method"
        subtitle="Select how the discount will work, such as a percentage off, a fixed amount, or a buy-one-get-one (BOGO) deal. This defines how the discount affects product prices."
      >
        <Grid container spacing={convertPxToRem(12)}>
          <Grid item xs={12}>
            <StyledStackableContainer>
              <Controller
                name="isStackable"
                control={control}
                render={({ field, fieldState: { error } }) => {
                  // Treez Checkbox component doesn't accept a ref prop
                  const { ref, value, ...rest } = field;
                  return (
                    <Checkbox
                      {...rest}
                      testId="stackable-discount-checkbox"
                      label="Stackable discount"
                      value={value}
                      checked={value}
                      error={!!error}
                    />
                  );
                }}
              />
            </StyledStackableContainer>
          </Grid>
          <Grid item xs={12} style={{ marginBottom: convertPxToRem(14) }}>
            <Typography variant="mediumText" color="secondaryText">
              This discount can be combined with other stackable discounts
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              name="method"
              control={control}
              rules={{ required: "Discount method is required" }}
              render={({ field, fieldState: { error } }) => {
                //  Treez Select component doesn't accept a ref prop
                const { ref, ...rest } = field;
                return (
                  <Select
                    {...rest}
                    testId="discount-method-select"
                    label="Discount method"
                    menuItems={methodsMenuItems}
                    helperText={error?.message ? error.message : ""}
                    error={!!error}
                    required
                  />
                );
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            {shouldDisplayAmountField() && (
              <Controller
                name="amount"
                control={control}
                rules={{
                  required: { value: true, message: "Amount is required" },
                  validate: validateDiscountAmountInput(discountMethod),
                }}
                render={({ field, fieldState: { error } }) => {
                  // Treez Input component doesn't accept a ref prop
                  const { ref, ...rest } = field;
                  return (
                    <Input
                      {...rest}
                      testId="discount-amount-input"
                      label="Amount"
                      helperText={error?.message ? error.message : ""}
                      error={!!error}
                      required
                      type="number"
                    />
                  );
                }}
              />
            )}
          </Grid>

          <Grid item xs={12}>
            <Typography
              variant="mediumText"
              color="secondaryText"
              data-testid="automated-discount-method-description"
            >
              {METHOD_TO_DESCRIPTION[discountMethod]}
            </Typography>
          </Grid>
        </Grid>
      </Panel>
    </StyledDiscountDetailsStep>
  );
};

export default DiscountDetailsStep;
