{"name": "discount-management", "description": "Centralized discount creation, enabling retailers to centrally manage all of the discounts they use today across all stores.", "scripts": {"start": "webpack serve --port 3001 --env stage=local", "start:standalone": "webpack serve --port 3001 --env standalone", "build": "webpack --mode=production", "build:dev": "webpack --mode=production --env stage=dev", "build:build": "webpack --mode=production --env stage=build", "build:prod": "webpack --mode=production --env stage=prod", "build:types": "tsc", "cypress:open": "cypress open", "cypress:run": "cypress run", "analyze": "webpack --mode=production --env analyze", "lint": "eslint .", "format": "prettier --write ./src", "check-format": "prettier --check .", "test": "jest --no-coverage --detectOpenHandles --config ./jest.config.js", "test:coverage": "jest --coverage --detectOpenHandles --config ./jest.config.js"}, "version": "1.71.4", "private": true, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "@tanstack/eslint-plugin-query": "^5.18.1", "@testing-library/dom": "^8.19.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^14.4.3", "@treez-inc/file-management": "^1.16.1", "@types/jest": "^28.1.7", "@types/node": "^18.11.7", "@types/react": "^18.0.21", "@types/react-dom": "^18.0.6", "@types/systemjs": "^6.1.1", "@types/webpack-env": "^1.16.2", "@typescript-eslint/eslint-plugin": "^5.33.0", "@typescript-eslint/parser": "^5.33.0", "babel-jest": "^27.0.6", "concurrently": "^6.2.1", "cypress": "^13.6.6", "dotenv-webpack": "^8.0.1", "eslint": "^7.32.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-import": "^2.25.3", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.28.0", "file-loader": "^6.2.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "msw": "^2.1.7", "prettier": "^2.3.2", "ts-config-single-spa": "^3.0.0", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "^4.3.5", "undici": "5.0.0", "webpack": "^5.51.1", "webpack-cli": "^4.8.0", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@emotion/react": "11.10.0", "@emotion/styled": "11.10.0", "@fontsource/roboto": "^4.5.8", "@mui/lab": "5.0.0-alpha.170", "@mui/material": "5.15.14", "@tanstack/react-query": "^5.18.1", "@tanstack/react-query-devtools": "^5.18.1", "@treez-inc/component-library": "^6.6.1", "axios": "^1.1.3", "fs": "^0.0.1-security", "moment": "^2.30.1", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.39.1", "react-router-dom": "^6.4.1", "single-spa": "5.9.3", "single-spa-react": "^5.0.0", "uuid": "^11.0.2"}, "types": "dist/treez-test.d.ts"}