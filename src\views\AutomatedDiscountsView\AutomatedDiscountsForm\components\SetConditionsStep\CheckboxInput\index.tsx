import React from "react";
import {
  Checkbox,
  CheckboxProps,
  convertPxToRem,
} from "@treez-inc/component-library";
import { Control, Controller, FieldValues } from "react-hook-form";
import { Box, styled, Typography } from "@mui/material";
import { DiscountConditionFormData } from "../../../../../../interfaces/discounts";
import { validateFulfillmentTypes } from "../../../../../../utils/validations";

export type CheckboxOption = Pick<CheckboxProps, "label" | "value">;

interface CheckboxInputProps {
  name: string;
  control: Control<FieldValues, DiscountConditionFormData>;
  options: CheckboxOption[];
  testId?: string;
}

const StyledCheckboxWrapper = styled(Box)({
  display: "flex",
  marginTop: convertPxToRem(16),
});

const StyledContainer = styled(Box)({
  maxWidth: convertPxToRem(300),
});

const StyledErrorContainer = styled(Box)({
  margin: `${convertPxToRem(16)} 0`,
});

const CheckboxInput = ({
  name,
  control,
  options,
  testId,
}: CheckboxInputProps) => (
  <Controller
    name={name}
    control={control}
    rules={{
      validate: validateFulfillmentTypes,
    }}
    render={({ field: { onChange, value }, fieldState: { error } }) => {
      const checkedValues = value || {};

      const handleCheckboxChange = (optionValue: string, checked: boolean) => {
        const updatedCheckedValues = {
          ...checkedValues,
          [optionValue]: checked,
        };

        options.forEach((option) => {
          if (!(option.value in updatedCheckedValues)) {
            updatedCheckedValues[option.value] = false;
          }
        });

        onChange(updatedCheckedValues);
      };

      return (
        <StyledContainer data-testid={testId && `${testId}-checkbox-input`}>
          {options.map((option: CheckboxOption) => (
            <StyledCheckboxWrapper key={option.label}>
              <Checkbox
                testId={testId && `${testId}-checkbox-option`}
                label={option.label}
                value={option.value}
                checked={!!checkedValues[option.value]}
                onChange={(checked) =>
                  handleCheckboxChange(option.value, !!checked)
                }
              />
            </StyledCheckboxWrapper>
          ))}

          {error && (
            <StyledErrorContainer>
              <Typography variant="mediumText" color="error" data-testid="select-one-fulfillment-type-error">
                {error.message}
              </Typography>
            </StyledErrorContainer>
          )}
        </StyledContainer>
      );
    }}
  />
);

export default CheckboxInput;
