import React, { useState } from "react";
import { Box, styled } from "@mui/material/";
import { ObjectType } from "@treez-inc/file-management";
import ImagePreview from "./ImagePreview";
import ImageUploader from "./ImageUploader";
import ApiService from "../../services/api/apiService";
import ImageDetailsModal from "./ImageDetailsModal";

const SubPanel = styled(Box)(({ theme }) => ({
  borderRadius: "16px",
  background: `${theme.palette.grey03.main}`,
  padding: "16px",
}));

export interface ImagesProps {
  api: ApiService;
  objectId: string;
  objectType: ObjectType;
  imageUrl: string | null;
  onDeleteImage?: () => any;
  onNewImageCreated: (objectId: string) => void;
}

const Images = ({
  api,
  objectId,
  imageUrl,
  objectType,
  onDeleteImage,
  onNewImageCreated,
}: ImagesProps) => {
  const [imageDetailsOpen, setImageDetailsOpen] = useState(false);

  return (
    <>
      {imageUrl && (
        <ImagePreview
          imageUrl={imageUrl}
          onDeleteImage={onDeleteImage}
          onSelectImage={() => setImageDetailsOpen(true)}
        />
      )}
      <SubPanel>
        <ImageUploader
          api={api}
          objectId={objectId}
          label={imageUrl ? "Update image" : "Choose file"}
          objectType={objectType}
          onNewImageCreated={onNewImageCreated}
        />
      </SubPanel>

      {imageDetailsOpen && (
        <ImageDetailsModal
          imageUrl={imageUrl}
          onClose={() => {
            setImageDetailsOpen(false);
          }}
        />
      )}
    </>
  );
};

export default Images;
