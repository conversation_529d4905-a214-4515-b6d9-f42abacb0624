import { formatMethod } from ".";

describe("formatMethod()", () => {
  it("should return the correct enum key for a known display name", () => {
    expect(formatMethod("Dollar Amount")).toBe("DOLLAR");
    expect(formatMethod("Percent Discount")).toBe("PERCENT");
    expect(formatMethod("Cost Plus %")).toBe("COST_PLUS");
    expect(formatMethod("Buy One Get One")).toBe("BOGO");
  });

  it("should return the display name itself for an unknown display name", () => {
    const unknownDisplayName = "Unknown Method";
    expect(formatMethod(unknownDisplayName)).toBe(unknownDisplayName);
  });
});
