import React from "react";
import {
  DesktopTimePicker as MUIDesktopTimePicker,
  TimePickerProps as MUITimePickerProps,
} from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { convertPxToRem } from "@treez-inc/component-library";
import { styled, TextFieldProps } from "@mui/material/";

interface TimePickerProps
  extends Pick<
      TextFieldProps,
      "disabled" | "error" | "helperText" | "required"
    >,
    Pick<MUITimePickerProps<any>, "value" | "onChange"> {
  /** Text to label the date picker */
  label: string;
  /** Test id that can be used for targeting elements in tests: data-testid={testId} */
  testId?: string;
}

const StyledTimePicker = styled(MUIDesktopTimePicker)(({ theme }) => ({
  "&.MuiTextField-root": {
    "& .MuiOutlinedInput-root": {
      borderRadius: convertPxToRem(19),
      minHeight: convertPxToRem(52),
      height: convertPxToRem(52),
      backgroundColor: theme.palette.grey03.main,
      color: theme.palette.primaryBlack.main,
      ...theme.typography.largeText,
      "&.Mui-focused": {
        outline: `${convertPxToRem(2)} solid ${
          theme.palette.primaryBlack.main
        }`,
      },
      "&.Mui-disabled": {
        color: theme.palette.disabledText.main,
      },
      "&.Mui-error": {
        color: theme.palette.error.main,
        outlineColor: theme.palette.error.main,
      },
    },
    "& .MuiFormHelperText-root": {
      color: theme.palette.secondaryText.main,
      ...theme.typography.smallText,
      "&.Mui-disabled": {
        color: theme.palette.tertiaryText.main,
      },
      "&.Mui-error": {
        color: theme.palette.error.main,
      },
    },
    // textfield
    input: {
      paddingTop: convertPxToRem(10),
      paddingBottom: 0,
    },
    // calendar icon button
    button: {
      padding: convertPxToRem(12),
    },
    // textfield label
    label: {
      color: theme.palette.secondaryText.main,
      "&[data-shrink=false]": {
        marginTop: convertPxToRem(-2),
        ...theme.typography.largeText,
      },
      "&[data-shrink=true]": {
        top: convertPxToRem(14),
        ...theme.typography.smallText,
      },
      "&.Mui-disabled": {
        color: `${theme.palette.disabledText.main} !important`,
      },
      "&.Mui-error": {
        color: theme.palette.error.main,
      },
    },
    // outlined input--remove outline
    fieldset: {
      border: "unset",
    },
  },
}));

const PopperStyles = (disableHighlightToday: boolean, value: any) => ({
  ".MuiPaper-root": {
    padding: convertPxToRem(16),
    borderRadius: convertPxToRem(19),
    boxShadow: `0 ${convertPxToRem(4)} ${convertPxToRem(
      16
    )} rgba(0, 0, 0, 0.16)`,
  },
  ".MuiMultiSectionDigitalClock-root": {
    borderBottom: "none",
  },
  ".MuiButtonBase-root": {
    borderRadius: convertPxToRem(12),
    "&[aria-current='date']": {
      backgroundColor: "unset",
      border:
        !disableHighlightToday && !value
          ? `${convertPxToRem(1)} solid`
          : "unset",
      borderColor: !disableHighlightToday && !value ? "grey04.main" : "unset",
    },
    "&:hover": {
      backgroundColor: "grey02.main",
    },
  },
  ".MuiDialogActions-root": {
    display: "none",
  },
});

const TimePicker: React.FC<TimePickerProps> = ({
  value,
  label,
  onChange,
  disabled = false,
  required = false,
  helperText,
  error = false,
  testId,
  ...props
}) => (
  <LocalizationProvider dateAdapter={AdapterDayjs}>
    <StyledTimePicker
      label={label}
      disabled={disabled}
      onChange={onChange}
      timeSteps={{ minutes: 1 }}
      slotProps={{
        textField: {
          helperText,
          required,
          value,
          error,
          ...{ "data-testid": testId },
          ...props,
        },
        popper: { sx: PopperStyles(false, value) },
      }}
    />
  </LocalizationProvider>
);

export default TimePicker;
