export enum CustomerEvents {
  BIRTHDAY = "BIRTHDAY",
  SIGN_UP_DATE = "SIGN_UP_DATE",
  VISIT_NUMBER = "VISIT_NUMBER",
}

export enum LicenseTypes {
  ADULT = "ADULT",
  MEDICAL = "MEDICAL",
}

export enum FulfillmentTypes {
  IN_STORE = "IN_STORE",
  DELIVERY = "DELIVERY",
  PICKUP = "PICKUP",
  EXPRESS = "EXPRESS",
}

export enum AutomatedDiscountMethods {
  DOLLAR = "DOLLAR",
  PERCENT = "PERCENT",
  COST_PLUS = "COST_PLUS",
  BOGO = "BOGO",
  BUNDLE = "BUNDLE",
}

export enum ManualDiscountMethods {
  DOLLAR = "DOLLAR",
  PERCENT = "PERCENT",
  PRICE_AT = "PRICE_AT",
}

export enum PurchaseAmountTypes {
  GRANDTOTAL = "GRANDTOTAL",
  SUBTOTAL = "SUBTOTAL",
}

export enum BogoDiscountMethods {
  PERCENT = "PERCENT",
  DOLLAR = "DOLLAR",
  TARGET_PRICE = "TARGET_PRICE",
}

export enum BundleDiscountMethods {
  PERCENT = "PERCENT",
  DOLLAR = "DOLLAR",
  TARGET_PRICE = "TARGET_PRICE",
}

export enum BundlePurchaseRequirement {
  UNIT_COUNT = "UNIT_COUNT",
  RETAIL_VALUE = "RETAIL_VALUE",
}

export enum PackageAgeTypes {
  RECEIVED = "RECEIVED",
  PACKAGED = "PACKAGED",
}
