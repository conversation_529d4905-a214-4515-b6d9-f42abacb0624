import React from "react";
import {
  Icon,
  StaticChip,
  convertPxToRem,
  Panel,
} from "@treez-inc/component-library";
import { UseFormGetValues } from "react-hook-form";
import {
  Box,
  Typography,
  Grid,
  TableHead,
  TableRow,
  TableBody,
  Table,
  TableCell,
  styled,
} from "@mui/material";
import { MANUAL_DISCOUNT_METHODS } from "../../../../../constants/discountForm";
import {
  ManualDiscountConditionFormData,
  ManualDiscountFormData,
  StoreCustomization,
} from "../../../../../interfaces/discounts";
import { EntityResponse } from "../../../../../interfaces/entity";
import {
  formatDateToLocale,
  formatDateToLocaleTime,
  getStores,
} from "../../../../../utils";

interface ReviewDiscountProps {
  getValues: UseFormGetValues<ManualDiscountFormData>;
  entityList: EntityResponse[];
  shouldShowStoresReview?: boolean;
}
interface SummaryItemProps {
  label: string;
  value: string | undefined;
  testId?: string | undefined;
}

interface CouponItemProps {
  code: string;
  startDate: Date;
  endDate?: Date;
  startTime?: Date;
  endTime?: Date;
  testId?: string | undefined;
}

const DiscountContainer = styled(Box)({
  // panel
  "> div": {
    marginBottom: convertPxToRem(24),
  },
});

const StyledGrid = styled(Grid)(({ theme }) => ({
  width: "100%",
  height: "auto",
  boxSizing: "border-box",
  background: theme.palette.primaryWhite.main,
  border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
  borderRadius: convertPxToRem(16),
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  padding: convertPxToRem(24),
  marginBottom: convertPxToRem(24),
}));

const StyledGlobalConditionsGrid = styled(Grid)(({ theme }) => ({
  width: "100%",
  height: "auto",
  boxSizing: "border-box",
  background: theme.palette.primaryWhite.main,
  border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
  borderRadius: convertPxToRem(16),
  padding: `${convertPxToRem(18)}`,
}));

const StyledCouponItem = styled(Box)({
  // panel
  "> div": {
    padding: `${convertPxToRem(16)} ${convertPxToRem(24)}`,
    marginBottom: convertPxToRem(8),
  },
});

const StyledCouponDateTime = styled(Typography)(({ theme }) => ({
  ...theme.typography.smallCapitalizedTextStrong,
  color: theme.palette.grey08.main,
  marginLeft: convertPxToRem(9),
}));

const StyledTitle = styled(Box)({
  marginBottom: convertPxToRem(8),
});

const StyledBox = styled(Box)({
  width: "100%",
  display: "flex",
  flexDirection: "column",
});

const StyledGridContainer = styled(Grid)({
  height: "auto",
  display: "flex",
  justifyContent: "space-between",
});

const StyledGridItem = styled(Grid)({
  display: "flex",
  flexDirection: "column",
});

const StyledCouponTimeGridItem = styled(Grid)({
  display: "flex",
});

const StyledGlobalConditionGrid = styled(Grid)(({ theme }) => ({
  borderLeft: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
  padding: `0 ${convertPxToRem(16)}`,
}));

const StyledGlobalConditionWithIconGrid = styled(Grid)({
  display: "flex",
  flexDirection: "row",
});

const StyledValueWrap = styled(Box)({
  marginTop: convertPxToRem(8),
  marginBottom: convertPxToRem(16),
  display: "flex",
  flexDirection: "row",
  flexWrap: "wrap",
  width: "100%",
});

const IconBoxWrap = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  padding: convertPxToRem(16),
  backgroundColor: theme.palette.green02.main,
  borderRadius: convertPxToRem(8),
  marginRight: convertPxToRem(12),
}));

const StyledValue = styled(Box)({
  marginTop: convertPxToRem(16),
});

const StyledDiscountName = styled(Box)({
  marginBottom: convertPxToRem(24),
  marginTop: convertPxToRem(24),
});

const StyledTable = styled(Table)({
  width: "100%",
});

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  width: "100%",
  borderBottom: `${convertPxToRem(1)} solid ${theme.palette.grey09.main}`,
}));

const conditionDisplayMap: Record<string, string> = {
  purchaseMinimumValue: "Purchase Minimum Requirement",
  customerCapValue: "Per-Customer Limit",
  itemLimitValue: "Redemption Item Limit",
};

const GlobalConditionItem: React.FC<SummaryItemProps> = ({
  label,
  value,
  testId,
}) => (
  <StyledGridItem item xs={12} lg={12} md={12} sm={12}>
    <Typography variant="smallText">{label}</Typography>

    <StyledValue>
      <Typography
        variant="largeText"
        color="secondaryText"
        data-testid={testId}
      >
        {value || "Not Required"}
      </Typography>
    </StyledValue>
  </StyledGridItem>
);

const CouponItem: React.FC<CouponItemProps> = ({
  code,
  startDate,
  endDate,
  startTime,
  endTime,
  testId,
}) => {
  const start = `${formatDateToLocale(startDate)} ${startTime ? formatDateToLocaleTime(startTime) : ""
    }`;

  const expires =
    `${endDate ? formatDateToLocale(endDate) : ""} ${endTime ? formatDateToLocaleTime(endTime) : ""
      }`.trim() || "NEVER";

  return (
    <StyledCouponItem data-testid={testId}>
      <Panel>
        <Typography data-testid={`${testId}-code`} variant="largeTextStrong">
          #{code}
        </Typography>

        <Grid container>
          <StyledCouponTimeGridItem item xs={4} lg={4} md={4} sm={4}>
            <Typography variant="smallCapitalizedTextStrong">START</Typography>
            <StyledCouponDateTime>{start}</StyledCouponDateTime>
          </StyledCouponTimeGridItem>

          <StyledCouponTimeGridItem item xs={4} lg={4} md={4} sm={4}>
            <Typography variant="smallCapitalizedTextStrong">
              EXPIRES
            </Typography>
            <StyledCouponDateTime>{expires}</StyledCouponDateTime>
          </StyledCouponTimeGridItem>
        </Grid>
      </Panel>
    </StyledCouponItem>
  );
};

const ReviewDiscount: React.FC<ReviewDiscountProps> = ({
  getValues,
  entityList,
  shouldShowStoresReview,
}) => {
  const discountData = getValues();
  const storeArray: StoreCustomization[] = [];
  const storeCustomizationsWithDetails: StoreCustomization[] = [];
  const manualConditions = Object.entries(discountData.manualConditions || {})
    .filter(([key, value]) => {
      const enabledKey = `${key.replace("Value", "Enabled")}` as keyof ManualDiscountConditionFormData;
      return (
        Object.prototype.hasOwnProperty.call(conditionDisplayMap, key) &&
        value !== undefined &&
        value !== null &&
        discountData.manualConditions[enabledKey]
      );
    })
    .map(([key, value]) => ({
      name: conditionDisplayMap[key],
      value: key === "purchaseMinimumValue" ? `$ ${value}` : value,
    }));

  getStores(entityList).forEach((e) => {
    const store = discountData.storeCustomizations.find(
      (storeCustom) => storeCustom.entityId === e.id && storeCustom.isActive
    );
    if (store) {
      storeArray.push(store);

      if (
        store.amount !== null ||
        store.requirePin !== null ||
        store.requireReason !== null
      ) {
        storeCustomizationsWithDetails.push(store);
      }
    }
  });

  const getGlobalConditionAmountText = () => {
    const customization = discountData;

    if (customization.method === MANUAL_DISCOUNT_METHODS.PERCENT) {
      return `${parseFloat(customization.amount).toFixed(2)} %`;
    }

    const dollarAmount = Number(parseFloat(customization.amount).toFixed(2));
    return `$${dollarAmount}`;
  };

  const getEntityName = (entityId: string) =>
    entityList.find((entity) => entity.id === entityId)?.name;

  const getAmountPercentText = (storeCustomization: StoreCustomization) => {
    const amount = parseFloat(
      storeCustomization?.amount || discountData.amount
    ).toFixed(2);

    if (discountData.method === MANUAL_DISCOUNT_METHODS.PERCENT) {
      return `${amount} %`;
    }

    return `$ ${Number(amount)}`;
  };

  return (
    <DiscountContainer>
      <Panel testId="review-discount-grid">
        <Typography variant="h6" color="primaryBlack">
          Activate Discount
        </Typography>

        <StyledGridContainer container spacing={2}>
          <StyledGridItem item xs={12} lg={6} md={6} sm={12}>
            <StyledDiscountName>
              <Typography variant="h5">{discountData.title}</Typography>
            </StyledDiscountName>
          </StyledGridItem>
        </StyledGridContainer>

        <StyledBox>
          <StyledGridContainer container>
            <StyledTitle>
              <Typography variant="mediumText" color="secondaryText">
                Global discount conditions
              </Typography>
            </StyledTitle>

            <StyledGlobalConditionsGrid container>
              <StyledGlobalConditionWithIconGrid
                item
                xs={3}
                lg={3}
                md={3}
                sm={3}
              >
                <IconBoxWrap>
                  <Icon
                    iconName={
                      discountData?.method === MANUAL_DISCOUNT_METHODS.PERCENT
                        ? "Percent"
                        : "Dollar"
                    }
                  />
                </IconBoxWrap>
                <GlobalConditionItem
                  label={
                    discountData?.method === MANUAL_DISCOUNT_METHODS.PERCENT
                      ? "Percent"
                      : "Amount"
                  }
                  value={getGlobalConditionAmountText()}
                />
              </StyledGlobalConditionWithIconGrid>

              <StyledGlobalConditionGrid item xs={3} lg={3} md={3} sm={3}>
                <GlobalConditionItem
                  label="TYPE"
                  value={discountData?.isItem ? "Item" : "Cart"}
                  testId="globalconditiondiscounttype-text"
                />
              </StyledGlobalConditionGrid>

              <StyledGlobalConditionGrid item xs={3} lg={3} md={3} sm={3}>
                <GlobalConditionItem
                  label="REASON"
                  value={
                    discountData?.requireReason ? "Required" : "Not Required"
                  }
                />
              </StyledGlobalConditionGrid>

              <StyledGlobalConditionGrid item xs={3} lg={3} md={3} sm={3}>
                <GlobalConditionItem
                  label="MANAGER PIN"
                  value={discountData?.requirePin ? "Required" : "Not Required"}
                />
              </StyledGlobalConditionGrid>
            </StyledGlobalConditionsGrid>
          </StyledGridContainer>
        </StyledBox>
      </Panel>

      {discountData.couponFormModel.coupons && (
        <Panel testId="couponsreviewdiscount-grid">
          <StyledTitle>
            <Typography variant="largeTextStrong" color="secondaryText">
              Coupons
            </Typography>
          </StyledTitle>

          <StyledBox>
            <StyledGridContainer container>
              <StyledGridItem item xs={12} lg={12} md={12} sm={12}>
                {discountData.couponFormModel.coupons.map((coupon) => (
                  <CouponItem
                    key={coupon.code}
                    code={coupon.code}
                    startDate={coupon.startDate}
                    startTime={coupon.startTime}
                    endDate={coupon.endDate}
                    endTime={coupon.endTime}
                    testId={`review-coupon-panel-${coupon.code}`}
                  />
                ))}
              </StyledGridItem>
            </StyledGridContainer>
          </StyledBox>
        </Panel>
      )}

      <StyledGrid data-testid="conditionsreviewdiscount-grid" item sm={9} xs={12}>
        <StyledTitle>
          <Typography variant="largeTextStrong" color="primaryBlack">
            Conditions
          </Typography>
        </StyledTitle>

        <StyledBox>
          <StyledGridContainer container>
            <StyledGridItem item xs={12} lg={12} md={12} sm={12}>
              <StyledValueWrap data-testid="review-condition-box">
                <StyledTable>
                  <TableHead>
                    <StyledTableRow>
                      <TableCell>Condition</TableCell>
                      <TableCell>Value</TableCell>
                    </StyledTableRow>
                  </TableHead>
                  <TableBody>
                    {manualConditions.map((manualCondition) => (
                      <StyledTableRow
                        key={manualCondition.name}
                        data-testid="review-condition-row"
                      >
                        <TableCell data-testid="review-condition-name-col">
                          <Typography variant="mediumText" color="grey08">
                            {manualCondition.name}
                          </Typography>
                        </TableCell>
                        <TableCell data-testid="review-condition-value-col">
                          <Typography variant="mediumText" color="grey08">
                            {manualCondition.value}
                          </Typography>
                        </TableCell>
                      </StyledTableRow>
                    ))}
                  </TableBody>
                </StyledTable>
              </StyledValueWrap>
            </StyledGridItem>
          </StyledGridContainer>
        </StyledBox>
      </StyledGrid>

      {shouldShowStoresReview &&
        <StyledGrid data-testid="storesreviewdiscount-grid" item sm={9} xs={12}>
          <StyledTitle>
            <Typography variant="largeTextStrong" color="primaryBlack">
              Stores
            </Typography>
          </StyledTitle>

          {storeCustomizationsWithDetails.length > 0 && (
            <StyledTitle data-testid="custom-store-review-discount-title">
              <Typography variant="mediumText" color="secondaryText.main">
                {storeCustomizationsWithDetails.length} store(s) using different
                conditions
              </Typography>
            </StyledTitle>
          )}

          <StyledBox>
            <StyledGridContainer container>
              <StyledGridItem item xs={12} lg={12} md={12} sm={12}>
                <StyledValueWrap data-testid="review-condition-box">
                  <StyledTable>
                    <TableHead>
                      <StyledTableRow>
                        <TableCell>Store name</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Amount</TableCell>
                        <TableCell>Reason</TableCell>
                        <TableCell>Manager PIN</TableCell>
                      </StyledTableRow>
                    </TableHead>
                    <TableBody>
                      {storeArray.map((row) => (
                        <StyledTableRow
                          key={row.entityId}
                          data-testid="review-condition-row"
                        >
                          <TableCell data-testid="reviewconditionentityname-col">
                            <Box
                              key={row.entityId}
                              sx={{
                                width: "auto",
                                marginRight: convertPxToRem(5),
                              }}
                            >
                              <Typography variant="mediumText" color="grey08">
                                {getEntityName(row.entityId)}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="mediumText" color="grey08">
                              {discountData.isItem ? "Item" : "Cart"}
                            </Typography>
                          </TableCell>
                          <TableCell data-testid="review-condition-amount-col">
                            {row.amount !== null ? (
                              <StaticChip
                                variant="filled"
                                color="gray"
                                label={getAmountPercentText(row)}
                              />
                            ) : (
                              <Typography variant="mediumText" color="grey08">
                                {getAmountPercentText(row)}
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell data-testid="review-condition-require-reason-col">
                            {row.requireReason !== null ? (
                              <StaticChip
                                variant="filled"
                                color="gray"
                                label={
                                  (row.requireReason && "Required") ||
                                  "Not required"
                                }
                              />
                            ) : (
                              <Typography variant="mediumText" color="grey08">
                                {(discountData.requireReason && "Required") ||
                                  "Not required"}
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell data-testid="review-condition-require-pin-col">
                            {row.requirePin !== null ? (
                              <StaticChip
                                variant="filled"
                                color="gray"
                                label={
                                  (row.requirePin && "Required") || "Not required"
                                }
                              />
                            ) : (
                              <Typography variant="mediumText" color="grey08">
                                {(discountData.requirePin && "Required") ||
                                  "Not required"}
                              </Typography>
                            )}
                          </TableCell>
                        </StyledTableRow>
                      ))}
                    </TableBody>
                  </StyledTable>
                </StyledValueWrap>
              </StyledGridItem>
            </StyledGridContainer>
          </StyledBox>
        </StyledGrid>
      }
    </DiscountContainer>
  );
};

export default ReviewDiscount;
