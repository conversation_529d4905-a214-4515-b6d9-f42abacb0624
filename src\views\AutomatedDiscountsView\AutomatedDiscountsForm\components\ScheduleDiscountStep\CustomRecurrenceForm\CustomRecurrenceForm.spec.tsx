import React from "react";
import userEvent from "@testing-library/user-event";
import { render, renderHook, screen } from "@testing-library/react";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { FormProvider, useForm } from "react-hook-form";
import { RepeatType } from "../../../../../../interfaces/discounts";
import { defaultSchedule, validateStartDateVsEndDateOfSchedule } from "..";
import CustomRecurrenceForm, {
  daysOfWeekToIntArray,
  toCustomRepeatDaysOfWeek,
} from ".";

describe("ScheduleDiscountStep.spec.tsx", () => {
  const renderScheduleDiscountStep = () => {
    const { result } = renderHook(() =>
      useForm({
        defaultValues: defaultSchedule(),
      })
    );

    const FormProviderWrapper: React.FC<{ children: React.ReactNode }> = ({
      children,
    }) => <FormProvider {...result.current}>{children}</FormProvider>;

    const { getByTestId } = screen;

    render(
      <TreezThemeProvider>
        <FormProviderWrapper>
          <CustomRecurrenceForm />
        </FormProviderWrapper>
      </TreezThemeProvider>
    );

    return {
      customRecurrenceContainer: getByTestId("custom-recurrence-container"),
      customRepeatIntervalCountInput: getByTestId(
        "custom-repeat-interval-count-input"
      ),
      customRepeatEverySelect: getByTestId("custom-repeat-every-select"),
      customEndTypeRadioButtons: getByTestId("custom-end-type-radio-buttons"),
      customEndDatePicker: getByTestId("custom-end-date-picker"),
      customEndRepeatCountInput: getByTestId("custom-end-repeat-count-input"),
    };
  };

  it("container loads with repeat number input", () => {
    const {
      customRecurrenceContainer,
      customRepeatIntervalCountInput,
      customRepeatEverySelect,
      customEndTypeRadioButtons,
      customEndDatePicker,
      customEndRepeatCountInput,
    } = renderScheduleDiscountStep();

    expect(customRecurrenceContainer).toBeInTheDocument();
    expect(customRepeatIntervalCountInput).toBeInTheDocument();
    expect(customRepeatEverySelect).toBeInTheDocument();
    expect(customEndTypeRadioButtons).toBeInTheDocument();
    expect(customEndDatePicker).toBeInTheDocument();
    expect(customEndRepeatCountInput).toBeInTheDocument();
  });

  it("defaults end date picker to disabled", () => {
    const { customEndDatePicker } = renderScheduleDiscountStep();

    expect(customEndDatePicker.querySelector("button")).toHaveClass(
      "Mui-disabled"
    );
  });

  it("Selecting custom recurrence end type to on date enables date picker ", async () => {
    const { getByTestId } = screen;

    const { customEndDatePicker } = renderScheduleDiscountStep();

    await userEvent.click(getByTestId("on-date-radio-button-option"));

    expect(customEndDatePicker.querySelector("button")).not.toHaveClass(
      "Mui-disabled"
    );
  });

  it("defaults repeat number input to disabled", () => {
    const { customEndRepeatCountInput } = renderScheduleDiscountStep();

    expect(customEndRepeatCountInput.querySelector("input")).toHaveClass(
      "Mui-disabled"
    );
  });

  it("Selecting custom recurrence end type to 'after # of recurrences' enables the custom end repeat count input", async () => {
    const { getByTestId } = screen;

    const { customEndRepeatCountInput } = renderScheduleDiscountStep();

    await userEvent.click(
      getByTestId("after-recurrence-count-radio-button-option")
    );

    expect(customEndRepeatCountInput.querySelector("input")).not.toHaveClass(
      "Mui-disabled"
    );
  });

  it("converts intArray to repeatDaysOfWeek object", () => {
    const result = toCustomRepeatDaysOfWeek([0, 3]);

    expect(result.MON).toBe(false);
    expect(result.TUE).toBe(false);
    expect(result.WED).toBe(true);
    expect(result.THU).toBe(false);
    expect(result.FRI).toBe(false);
    expect(result.SAT).toBe(false);
    expect(result.SUN).toBe(true);
  });

  it("converts repeatDaysOfWeek object to int array", () => {
    const result = daysOfWeekToIntArray({
      SUN: true,
      MON: false,
      TUE: false,
      WED: true,
      THU: false,
      FRI: true,
      SAT: true,
    });

    expect(result).toMatchObject([0, 3, 5, 6]);
  });

  it("converts repeatDaysOfWeek object to correct in array when returned out of order", () => {
    const result = daysOfWeekToIntArray({
      TUE: true,
      WED: true,
      SUN: true,
      THU: false,
      FRI: true,
      SAT: false,
      MON: false,
    });

    expect(result).toMatchObject([0, 2, 3, 5]);
  });

  it("validates start time and end time on their own, if spansMultipleDays is off", () => {
    expect(
      validateStartDateVsEndDateOfSchedule({
        id: "6cc97a07-a3de-46cd-8247-db6e2ae863c7",
        startDate: new Date(),
        startTime: new Date(2024, 1, 1, 11, 0),
        endDate: null,
        endTime: new Date(2024, 1, 1, 10, 0),
        allDay: false,
        spansMultipleDays: false,
        repeatType: RepeatType.DO_NOT,
        customRepeatIntervalCount: null,
        customRepeatEvery: null,
        customRepeatDaysOfWeek: null,
        customEndType: null,
        customEndDate: null,
        customEndRepeatCount: null,
      })
    ).toBe("End time needs to be after start time");
  });
});
