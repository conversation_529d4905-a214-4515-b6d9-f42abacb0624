import { ConditionsRowData } from "../../../interfaces/table";
import {
  AutomatedDiscountMethods,
  BogoDiscountMethods,
  BundleDiscountMethods,
} from "../../../constants/discounts";

const formatAmount = (
  amount: string,
  method: string,
  conditions?: Partial<ConditionsRowData>
) => {
  const formattedAmount = parseFloat(amount).toFixed(2);
  if (
    method === AutomatedDiscountMethods.DOLLAR ||
    method === AutomatedDiscountMethods.COST_PLUS
  ) {
    return `$${formattedAmount}`;
  }
  if (method === AutomatedDiscountMethods.PERCENT) {
    return `${formattedAmount}%`;
  }

  if (
    (method === AutomatedDiscountMethods.BOGO ||
      method === AutomatedDiscountMethods.BUNDLE) &&
    conditions
  ) {
    const discountUnit =
      conditions?.bogoConditions?.discountUnit ||
      conditions?.bundleConditions?.discountUnit;

    if (
      discountUnit === BogoDiscountMethods.PERCENT ||
      discountUnit === BundleDiscountMethods.PERCENT
    ) {
      return `${formattedAmount}%`;
    }
    return `$${formattedAmount}`;
  }

  return formattedAmount;
};

export { formatAmount };
