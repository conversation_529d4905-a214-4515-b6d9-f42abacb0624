import axios from "axios";
import retry from "./retry";

jest.mock("axios");

describe("retry request", () => {
  it("should retry request", async () => {
    const config = {
      message: "Network Error",
      code: "1",
      config: {
        headers: {
          retry: "1",
        },
      },
      request: {},
      response: {},
    };

    // @ts-ignore
    await retry.retryRequest(config);
    expect(axios).toBeCalled();
  });
});
