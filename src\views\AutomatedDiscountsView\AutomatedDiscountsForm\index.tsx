import React, { useMemo, useState } from "react";
import { FormProvider } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { styled, Box, Typography } from "@mui/material";
import {
  convertPxToRem,
  LinearStepper,
  Modal,
} from "@treez-inc/component-library";
import { IconName } from "@treez-inc/component-library/dist/components/Icon/types";
import LoadingSpinner from "../../../components/LoadingSpinner";
import WizardFormModal from "../../../components/WizardFormModal";
import Footer from "../../../components/WizardFormModal/Footer";
import HeaderBar from "../../../components/WizardFormModal/HeaderBar";
import { automatedDiscountsPath } from "../../../constants/routes";
import ApiService from "../../../services/api/apiService";
import { EntityResponse } from "../../../interfaces/entity";
import { renderForm } from "./utils/renderForm";
import { useAutomatedDiscountsForm } from "./utils/useAutomatedDiscountsForm";
import { usePageLoading, useGetEntities } from "../../../hooks";
import { entityListUrl } from "../../../services/apiEndPoints";
import {
  parseJwt,
  getBannerTitle,
  getNextStepButtonLabel,
} from "../../../utils";
import { EntityContext } from "../../../providers/EntityProvider";

interface AutomatedDiscountsFormProps {
  api: ApiService;
  permissions: { read: boolean; write: boolean };
  isEditMode?: boolean;
  getPermissions?: () => Promise<any>;
}

const FormLayout = styled("div")(() => ({
  maxWidth: convertPxToRem(900),
  overflowY: "auto",
  display: "flex",
  padding: `${convertPxToRem(32)} 0`,
  gap: `${convertPxToRem(24)}`,
  flex: "1 1 0",
  justifyContent: "space-between",
}));

const StepsWrapper = styled(Box)(() => ({
  marginBottom: "auto",
  display: "grid",
  gridTemplateColumns: `${convertPxToRem(208)} 1fr`,
  paddingTop: `${convertPxToRem(34)}`,
  width: `${convertPxToRem(256)}`,
  // Linear Stepper
  ".MuiStepper-root": {
    zIndex: 0,
  },
}));

const AutomatedDiscountsForm: React.FC<AutomatedDiscountsFormProps> = ({
  api,
  permissions,
  isEditMode = false,
  getPermissions,
}) => {
  const decodedToken = parseJwt(api.getTokens().accessToken);
  const { orgDiscountId } = useParams();
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(true);
  const [isConfirmEditModalOpen, setIsConfirmEditModalOpen] =
    useState<boolean>(false);
  const { isPageLoading, setIsPageLoading } = usePageLoading();
  const [entityList] = useGetEntities(api, entityListUrl(decodedToken?.orgId));
  const entityListData = entityList.data || [];

  const {
    methods,
    activeStep,
    existingDiscount,
    customerGroups,
    steps,
    handleNext,
    handleBack,
    onSubmit,
  } = useAutomatedDiscountsForm(
    api,
    setIsPageLoading,
    entityListData,
    orgDiscountId
  );

  const providerValue = useMemo<EntityResponse[]>(
    () => entityList.data || [],
    [entityList]
  );

  const handleClose = () => {
    setIsModalOpen(!isModalOpen);
    navigate(automatedDiscountsPath);
  };

  const handlePrimaryButton = () => {
    if (activeStep === steps.length - 1 && !isPageLoading) {
      methods.handleSubmit(onSubmit)();
    } else {
      handleNext();
    }
  };

  const handleDoneEditingButton = () => {
    setIsConfirmEditModalOpen(true);
  };

  const secondaryButtonProps =
    activeStep > 0
      ? {
          iconName: "ChevronLeft" as IconName,
          label: steps[activeStep - 1].label,
          onClick: handleBack,
          testId: "back-button",
        }
      : undefined;

  const tertiaryButtonProps = isEditMode
    ? {
        label: "Done Editing",
        onClick: handleDoneEditingButton,
        testId: "done-editing-button",
        disabled: !permissions.write,
      }
    : undefined;

  return (
    <EntityContext.Provider value={providerValue}>
      <FormProvider {...methods}>
        <WizardFormModal
          bannerTitle={
            existingDiscount &&
            getBannerTitle({
              orgDiscount: existingDiscount,
            })
          }
          testId="automated-discounts-add-modal"
          isOpen={isModalOpen}
          handleClose={handleClose}
        >
          {isPageLoading && <LoadingSpinner />}
          <HeaderBar
            header={
              existingDiscount
                ? existingDiscount.title
                : "Add Automated Discount"
            }
            subheader=""
            iconName="HorizontalRule"
          />
          <FormLayout>
            <StepsWrapper>
              <LinearStepper
                testId="automated-discount-form-steps"
                steps={steps}
                activeStep={activeStep}
              />
            </StepsWrapper>
            <Box sx={{ width: "100%" }}>
              <form onSubmit={methods.handleSubmit(onSubmit)}>
                {renderForm(steps[activeStep].label, {
                  api,
                  customerGroups,
                  getPermissions,
                })}
              </form>
            </Box>
          </FormLayout>

          <Footer
            testId="automated-discounts-footer"
            activeStep={activeStep}
            totalSteps={steps.length}
            primaryButtonProps={{
              label: getNextStepButtonLabel(
                activeStep,
                steps.length,
                !!orgDiscountId
              ),
              disabled:
                isPageLoading ||
                (activeStep === steps.length - 1 && !permissions.write),
              onClick: handlePrimaryButton,
              testId: "next-finish-button",
            }}
            secondaryButtonProps={secondaryButtonProps}
            tertiaryButtonProps={tertiaryButtonProps}
          />
        </WizardFormModal>
        <Modal
          testId="automated-discounts-confirm-edit-modal"
          title="Confirm your edit"
          open={isConfirmEditModalOpen}
          primaryButton={{
            label: "Confirm",
            onClick: () => {
              methods.handleSubmit(onSubmit)();
              setIsConfirmEditModalOpen(false);
            },
          }}
          onClose={() => setIsConfirmEditModalOpen(false)}
          secondaryButton={{
            label: "Cancel",
            onClick: () => setIsConfirmEditModalOpen(false),
          }}
          content={
            <Typography variant="largeText" color="primaryBlackText">
              Are you sure you want to make the edits to this discount?
            </Typography>
          }
        />
      </FormProvider>
    </EntityContext.Provider>
  );
};

export default AutomatedDiscountsForm;
