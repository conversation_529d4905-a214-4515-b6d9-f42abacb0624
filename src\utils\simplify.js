const simplifyJSON = (objStr) => {
  const obj = JSON.parse(objStr);
  const simplifiedJSON = {};
  Object.keys(obj).forEach((key) => {
    const value = obj[key];
    const type = typeof value;
    if (
      ["string", "boolean"].includes(type) ||
      (type === "number" && !Number.isNaN(value))
    ) {
      simplifiedJSON[key] = value;
    } else if (type === "object") {
      Object.assign(simplifiedJSON, simplifyJSON(JSON.stringify(value)));
    }
  });
  return simplifiedJSON;
};

const groupBy = (entityArray, key) =>
  entityArray.reduce((entity, initialValue) => {
    // eslint-disable-next-line no-param-reassign
    (entity[initialValue[key] === undefined ? "Other" : initialValue[key]] =
      entity[initialValue[key] || "Other"] || []).push(initialValue);
    return entity;
  }, {});

export default {
  simplifyJSO<PERSON>,
  groupBy,
};
