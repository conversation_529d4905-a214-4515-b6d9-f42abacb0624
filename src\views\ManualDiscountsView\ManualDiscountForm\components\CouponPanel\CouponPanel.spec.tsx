/* eslint-disable react/prop-types */
import React from "react";
import { FormProvider, useForm } from "react-hook-form";
import { fireEvent, render, screen } from "@testing-library/react";
import {
  TreezThemeProvider,
  convertPxToRem,
  lightGreyColors,
} from "@treez-inc/component-library";
import iconAriaLabels from "@treez-inc/component-library/dist/components/Icon/icon-library/icon-aria-labels";
import CouponPanel, { CouponPanelProps } from ".";
import { ManualDiscountFormData } from "../../../../../interfaces/discounts";
import {
  defaultCoupon,
  doesNotExpireFalseCoupon,
  endDateCoupon,
  endTimeCoupon,
  isAllDayFalseCoupon,
  startTimeCoupon,
} from "./fixtures";

describe("<CouponPanel />", () => {
  const renderCouponPanel = (props: Pick<CouponPanelProps, "coupon">) => {
    const { getByTestId } = screen;

    const onDeleteCallback = jest.fn();
    const onChangeCallback = jest.fn();

    const Wrapper: React.FC = () => {
      const defaultValues = {
        title: "",
        displayTitle: "",
        method: "",
        amount: "",
        isItem: false,
        isAdjustment: false,
        isActive: true,
        requirePin: false,
        requireReason: false,
        requireCoupon: false,
        storeCustomizations: [],
        couponFormModel: {
          coupons: [props.coupon],
        },
      };
      const methods = useForm<ManualDiscountFormData>({
        defaultValues,
        mode: "all",
      });

      const {
        control,
        formState: { errors },
      } = methods;

      const couponErrors =
        errors?.couponFormModel?.coupons && errors?.couponFormModel?.coupons[0];

      return (
        <FormProvider {...methods}>
          <CouponPanel
            {...props}
            control={control}
            couponErrors={couponErrors}
            couponIdx={0}
            coupon={props.coupon}
            existingCoupons={[props.coupon]}
            onDelete={onDeleteCallback}
            onFieldChange={onChangeCallback}
          />
        </FormProvider>
      );
    };

    render(
      <TreezThemeProvider>
        <Wrapper />
      </TreezThemeProvider>
    );

    const couponPanel = getByTestId("coupon-panel");
    const codeInput = getByTestId("coupon-code-input");
    const startDatePicker = getByTestId("coupon-start-date-picker");
    const endDatePicker = getByTestId("coupon-end-date-picker");
    const startTimePicker = getByTestId("coupon-start-time-picker");
    const endTimePicker = getByTestId("coupon-end-time-picker");
    const isAllDayCheckbox = getByTestId("coupon-is-all-day-checkbox");
    const doesNotExpireCheckbox = getByTestId(
      "coupon-does-not-expire-checkbox"
    );
    const deleteButton = getByTestId("coupon-delete-button");

    return {
      couponPanel,
      codeInput,
      startDatePicker,
      endDatePicker,
      startTimePicker,
      endTimePicker,
      isAllDayCheckbox,
      doesNotExpireCheckbox,
      deleteButton,
      onDeleteCallback,
      onChangeCallback,
    };
  };

  it("should render with code", () => {
    const { couponPanel, codeInput } = renderCouponPanel({
      coupon: defaultCoupon,
    });
    const input = codeInput.querySelector("input") as HTMLInputElement;
    expect(couponPanel).toBeInTheDocument();
    expect(input.value).toBe(defaultCoupon.code);
    expect(couponPanel).toHaveStyle(` border-radius: ${convertPxToRem(16)}`);
    expect(couponPanel).toHaveStyle(
      ` border-color: ${lightGreyColors.grey04.main}`
    );
  });

  it("should render a delete button", () => {
    const { deleteButton } = renderCouponPanel({
      coupon: defaultCoupon,
    });
    const deleteIcon = deleteButton.querySelector('span[role="img"]');

    expect(deleteButton).toBeInTheDocument();
    expect(deleteButton).toHaveTextContent("Delete");
    expect(deleteIcon).toHaveAttribute("aria-label", iconAriaLabels.Delete);
  });

  it("should render with start date", () => {
    const { startDatePicker } = renderCouponPanel({
      coupon: defaultCoupon,
    });

    const input = startDatePicker.querySelector("input") as HTMLInputElement;

    const expectedDate = defaultCoupon.startDate.toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "numeric",
    });

    expect(input.value).toBe(expectedDate);
  });

  it("should render with end date", () => {
    const { endDatePicker } = renderCouponPanel({
      coupon: endDateCoupon,
    });

    const input = endDatePicker.querySelector("input") as HTMLInputElement;

    const expectedDate = endDateCoupon.endDate.toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "numeric",
    });

    expect(input.value).toBe(expectedDate);
  });

  it("should render with start time", () => {
    const { startTimePicker } = renderCouponPanel({
      coupon: startTimeCoupon,
    });

    const input = startTimePicker.querySelector("input") as HTMLInputElement;

    expect(input.value).toBe("10:05 AM");
  });

  it("should render with end time", () => {
    const { endTimePicker } = renderCouponPanel({
      coupon: endTimeCoupon,
    });

    const input = endTimePicker.querySelector("input") as HTMLInputElement;

    expect(input.value).toBe("08:15 PM");
  });

  it("should call `onDeleteCallback` when Delete button is clicked", () => {
    const { deleteButton, onDeleteCallback } = renderCouponPanel({
      coupon: defaultCoupon,
    });

    fireEvent.click(deleteButton);

    expect(onDeleteCallback).toHaveBeenCalled();
  });

  describe("isAllDay = true", () => {
    it("should show the 'All day' checkbox checked", () => {
      const { isAllDayCheckbox } = renderCouponPanel({
        coupon: defaultCoupon,
      });

      const checkbox = isAllDayCheckbox.querySelector(
        "input"
      ) as HTMLInputElement;
      expect(checkbox.hasAttribute("checked")).toBeTruthy();
    });

    it("should show the start time input disabled when 'All day' checkbox is checked", () => {
      const { startTimePicker } = renderCouponPanel({
        coupon: defaultCoupon,
      });

      const input = startTimePicker.querySelector("input") as HTMLInputElement;
      expect(input.hasAttribute("disabled")).toBeTruthy();
    });

    it("should show the end time input disabled when 'All day' checked and 'Does not expire' is unchecked", () => {
      const { endTimePicker } = renderCouponPanel({
        coupon: {
          ...defaultCoupon,
          ignoreExpiration: false,
        },
      });

      const input = endTimePicker.querySelector("input") as HTMLInputElement;
      expect(input.hasAttribute("disabled")).toBeTruthy();
    });
  });

  describe("isAllDay = false", () => {
    it("should show the 'All day' checkbox unchecked", () => {
      const { isAllDayCheckbox } = renderCouponPanel({
        coupon: isAllDayFalseCoupon,
      });

      const checkbox = isAllDayCheckbox.querySelector(
        "input"
      ) as HTMLInputElement;
      expect(checkbox.hasAttribute("checked")).toBeFalsy();
    });

    it("should show the start time input enabled when 'All day' checkbox is unchecked", () => {
      const { startTimePicker } = renderCouponPanel({
        coupon: isAllDayFalseCoupon,
      });

      const input = startTimePicker.querySelector("input") as HTMLInputElement;
      expect(input.hasAttribute("disabled")).toBeFalsy();
    });

    it("should show the end time input enabled when 'All day' unchecked and 'Does not expire' is unchecked", () => {
      const { endTimePicker } = renderCouponPanel({
        coupon: {
          ...isAllDayFalseCoupon,
          ignoreExpiration: false,
        },
      });

      const input = endTimePicker.querySelector("input") as HTMLInputElement;
      expect(input.hasAttribute("disabled")).toBeFalsy();
    });
  });

  describe("ignoreExpiration = true", () => {
    it("should show the 'Does not expire' checkbox checked", () => {
      const { doesNotExpireCheckbox } = renderCouponPanel({
        coupon: defaultCoupon,
      });

      const checkbox = doesNotExpireCheckbox.querySelector(
        "input"
      ) as HTMLInputElement;
      expect(checkbox.hasAttribute("checked")).toBeTruthy();
    });

    it("should show the end date input disabled when 'Does not expire' checkbox is checked", () => {
      const { endDatePicker } = renderCouponPanel({
        coupon: defaultCoupon,
      });

      const input = endDatePicker.querySelector("input") as HTMLInputElement;
      expect(input.hasAttribute("disabled")).toBeTruthy();
    });

    it("should show the end time input disabled when 'Does not expire' checkbox is checked", () => {
      const { endTimePicker } = renderCouponPanel({
        coupon: defaultCoupon,
      });

      const input = endTimePicker.querySelector("input") as HTMLInputElement;
      expect(input.hasAttribute("disabled")).toBeTruthy();
    });
  });

  describe("ignoreExpiration = false", () => {
    it("should show the 'Does not expire' checkbox unchecked", () => {
      const { doesNotExpireCheckbox } = renderCouponPanel({
        coupon: doesNotExpireFalseCoupon,
      });

      const checkbox = doesNotExpireCheckbox.querySelector(
        "input"
      ) as HTMLInputElement;
      expect(checkbox.hasAttribute("checked")).toBeFalsy();
    });

    it("should show the end date input enabled when 'Does not expire' checkbox is unchecked", () => {
      const { endDatePicker } = renderCouponPanel({
        coupon: doesNotExpireFalseCoupon,
      });

      const input = endDatePicker.querySelector("input") as HTMLInputElement;
      expect(input.hasAttribute("disabled")).toBeFalsy();
    });

    it("should show the end time input enabled when 'Does not expire' checkbox is unchecked", () => {
      const { endTimePicker } = renderCouponPanel({
        coupon: doesNotExpireFalseCoupon,
      });

      const input = endTimePicker.querySelector("input") as HTMLInputElement;
      expect(input.hasAttribute("disabled")).toBeFalsy();
    });
  });

  describe("onFieldChange", () => {
    it("should fire 'onFieldChange' when code input is changed", () => {
      const { codeInput, onChangeCallback } = renderCouponPanel({
        coupon: defaultCoupon,
      });

      const input = codeInput.querySelector("input") as HTMLInputElement;

      fireEvent.change(input, { target: { value: "NEWCODE01" } });

      expect(onChangeCallback).toHaveBeenCalledWith("code", expect.any(String));
    });

    it("should fire 'onFieldChange' when start time input is changed", () => {
      const { startTimePicker, onChangeCallback } = renderCouponPanel({
        coupon: isAllDayFalseCoupon,
      });

      const input = startTimePicker.querySelector("input") as HTMLInputElement;

      fireEvent.change(input, {
        target: { value: `10:05 AM` },
      });

      expect(onChangeCallback).toHaveBeenCalledWith(
        "startTime",
        expect.any(Date)
      );
    });

    it("should fire 'onFieldChange' when end time input is changed", () => {
      const { endTimePicker, onChangeCallback } = renderCouponPanel({
        coupon: doesNotExpireFalseCoupon,
      });

      const input = endTimePicker.querySelector("input") as HTMLInputElement;

      fireEvent.change(input, {
        target: { value: `11:05 AM` },
      });

      expect(onChangeCallback).toHaveBeenCalledWith(
        "endTime",
        expect.any(Date)
      );
    });

    it("should fire 'onFieldChange' when `Is all day` is clicked", () => {
      const { isAllDayCheckbox, onChangeCallback } = renderCouponPanel({
        coupon: defaultCoupon,
      });

      const input = isAllDayCheckbox.querySelector("input") as HTMLInputElement;

      fireEvent.click(input);

      expect(onChangeCallback).toHaveBeenCalledWith(
        "isAllDay",
        expect.any(Boolean)
      );
    });

    it("should fire 'onFieldChange' when `Does not expire` is clicked", () => {
      const { doesNotExpireCheckbox, onChangeCallback } = renderCouponPanel({
        coupon: defaultCoupon,
      });

      const input = doesNotExpireCheckbox.querySelector(
        "input"
      ) as HTMLInputElement;

      fireEvent.click(input);

      expect(onChangeCallback).toHaveBeenCalledWith(
        "ignoreExpiration",
        expect.any(Boolean)
      );
    });
  });

  // Note: Not able to test onFieldChange being fired because MUI DatePicker needs a work around to make it able to test and this is wrapped in TCL.
  // Source: https://github.com/mui/material-ui-pickers/issues/2073
  describe("Date picker inputs", () => {
    it("should change the start date when input is changed", async () => {
      const newStartDate = "10/19/1996";

      const { startDatePicker } = renderCouponPanel({
        coupon: defaultCoupon,
      });

      const input = startDatePicker.querySelector("input") as HTMLInputElement;

      fireEvent.change(input, { target: { value: newStartDate } });

      expect(input).toHaveValue(newStartDate);
    });

    it("should change the end date when input is changed", async () => {
      const newEndDate = "10/19/1996";

      const { endDatePicker } = renderCouponPanel({
        coupon: defaultCoupon,
      });

      const input = endDatePicker.querySelector("input") as HTMLInputElement;

      fireEvent.change(input, { target: { value: newEndDate } });

      expect(input).toHaveValue(newEndDate);
    });
  });
});
