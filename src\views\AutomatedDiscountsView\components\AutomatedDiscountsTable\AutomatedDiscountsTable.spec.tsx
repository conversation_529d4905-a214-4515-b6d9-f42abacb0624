import React from "react";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import { render, screen } from "@testing-library/react";
import {
  convertPxToRem,
  TreezThemeProvider,
} from "@treez-inc/component-library";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import AutomatedDiscountsTable, { AutomatedDiscountsTableProps } from ".";
import { testAutomatedDiscountsResponse } from "../../../../test/fixtures";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

describe("<AutomatedDiscountTable />", () => {
  const mockOpenDiscountModal = jest.fn();
  const mockOpenDiscountLog = jest.fn();

  const renderAutomatedDiscountsTable = (
    props: Omit<AutomatedDiscountsTableProps, "api" | "openDiscountModal" | "openDiscountLog">
  ) => {
    render(
      <Router>
        <TreezThemeProvider>
          <QueryClientProvider client={queryClient}>
            <AutomatedDiscountsTable
              {...props}
              openDiscountModal={mockOpenDiscountModal}
              openDiscountLog={mockOpenDiscountLog}
            />
          </QueryClientProvider>
        </TreezThemeProvider>
      </Router>
    );

    const { getByTestId, getAllByRole } = screen;

    const table = getByTestId("automated-discounts-list-container");
    const discountsCount = getByTestId("automated-discounts-count");
    const dataGridPro = getByTestId("data-grid-pro-container");
    const parentRows = getAllByRole("row");
    const columnHeaders = getAllByRole("columnheader");

    return {
      table,
      discountsCount,
      dataGridPro,
      parentRows,
      columnHeaders,
    };
  };

  it("renders the automated discounts table container", () => {
    const { table } = renderAutomatedDiscountsTable({
      discounts: testAutomatedDiscountsResponse,
    });
    expect(table).toBeInTheDocument();
    expect(table).toHaveStyle(`padding: ${convertPxToRem(40)} 0`);
  });

  it("renders the discount count", () => {
    const { discountsCount } = renderAutomatedDiscountsTable({
      discounts: testAutomatedDiscountsResponse,
    });
    expect(discountsCount).toBeInTheDocument();
    expect(discountsCount).toHaveTextContent(
      `${testAutomatedDiscountsResponse.length} Discounts`
    );
  });

  it("renders the correct number of parent rows", () => {
    const { dataGridPro, parentRows } = renderAutomatedDiscountsTable({
      discounts: testAutomatedDiscountsResponse,
    });

    expect(dataGridPro).toBeInTheDocument();
    expect(parentRows).toHaveLength(3);
  });

  it("renders the correct number of total rows", () => {
    const { dataGridPro } = renderAutomatedDiscountsTable({
      discounts: testAutomatedDiscountsResponse,
    });
    const grid = dataGridPro.querySelectorAll("div")[1];
    expect(grid).toHaveAttribute("aria-rowcount", "6");
  });

  // Cannot test all headers - known issue with MUI DataGrid not rendering all column headers in test environment - 10/31/23
  it("renders the correct number of columns", () => {
    const { columnHeaders } = renderAutomatedDiscountsTable({
      discounts: testAutomatedDiscountsResponse,
    });

    expect(columnHeaders).toHaveLength(3);
    expect(columnHeaders[1]).toHaveTextContent(/Name/);
    expect(columnHeaders[2]).toHaveTextContent(/Method/);
  });

  it("should render the rowActionColumn with the correct width", () => {
    const { columnHeaders } = renderAutomatedDiscountsTable({
      discounts: testAutomatedDiscountsResponse,
    });
    // width in px due to DataGridPro constraints
    expect(columnHeaders[0]).toHaveStyle(`width: 100px`);
  });
});
