import React from "react";
import { styled, Box } from "@mui/material";
import { Button, convertPxToRem } from "@treez-inc/component-library";

interface LeftPanelProps {
  onClose: () => void;
}

const LeftPanelWrapper = styled(Box)(() => ({
  display: "flex",
  flexDirection: "column",
  justifyContent: "flex-start",
}));

const LeftPanelTop = styled(Box)(() => ({
  flex: `0 0 ${convertPxToRem(64)}`,
  marginBottom: "auto",
  paddingTop: `${convertPxToRem(32)}`,
  paddingLeft: `${convertPxToRem(32)}`,
  // link
  ">a": {
    display: "flex",
    alignItems: "center",
  },
}));

const LeftPanelBottom = styled(Box)(({ theme }) => ({
  flex: `0 0 ${convertPxToRem(84)}`,
  borderTop: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
}));

const LeftPanel: React.FC<LeftPanelProps> = ({ onClose }) => (
  <LeftPanelWrapper data-testid="roleaddoredit-leftpanel">
    <LeftPanelTop data-testid="roleaddoredit-leftpanel-top">
      <Button
        variant="text"
        iconName="ChevronLeft"
        label="Close"
        onClick={onClose}
        testId="close-form"
      />
    </LeftPanelTop>
    <LeftPanelBottom data-testid="roleaddoredit-leftpanel-bottom" />
  </LeftPanelWrapper>
);

export default LeftPanel;
