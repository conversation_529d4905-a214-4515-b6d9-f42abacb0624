import React from "react";
import {
  render,
  screen,
  fireEvent,
  within,
  waitFor,
  renderHook,
} from "@testing-library/react";
import { useForm, FormProvider } from "react-hook-form";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { TreezThemeProvider } from "@treez-inc/component-library";
import {
  AUTOMATED_DISCOUNT_METHODS,
  defaultAutomatedDiscountFormValues,
} from "../../../../../constants/discountForm";
import ProductCollectionsStep from ".";
import {
  testAutomatedDiscountsResponse,
  testProductCollections,
} from "../../../../../test/fixtures";
import {
  AutomatedDiscountFormData,
  ProductCollection,
} from "../../../../../interfaces/discounts";
import { BundleDiscountMethods, BundlePurchaseRequirement } from "../../../../../constants/discounts";

describe("ProductCollectionsStep", () => {
  const renderProductCollectionsStep = (
    invalid = false,
    collections: ProductCollection[] = [],
    discount: Partial<AutomatedDiscountFormData> = {}
  ) => {
    const { result } = renderHook(() =>
      useForm({
        defaultValues: {
          ...defaultAutomatedDiscountFormValues,
          collections,
          ...discount,
        },
      })
    );

    if (invalid) {
      result.current.formState.errors.collections = {
        type: "required",
        message: "No Products Selected",
      };
    }

    const queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });

    render(
      <TreezThemeProvider>
        <QueryClientProvider client={queryClient}>
          <FormProvider {...result.current}>
            <ProductCollectionsStep />
          </FormProvider>
        </QueryClientProvider>
      </TreezThemeProvider>
    );

    const { getByTestId, getAllByTestId, queryByTestId } = screen;

    const selectCollectionsStepContainer = getByTestId(
      "automated-product-collections-step"
    );

    const searchInputWrapper = getByTestId(
      "automated-product-collections-search-input"
    );

    const getSearchInput = () =>
      within(searchInputWrapper).getByRole("searchbox");

    const selectAllCheckboxWrapper = getByTestId(
      "automated-product-collections-select-all-wrapper"
    );

    const getSelectAllCheckbox = () =>
      within(
        getByTestId("automated-product-collections-select-all-checkbox")
      ).getByRole("checkbox");

    const getCollectionCheckboxes = () =>
      getAllByTestId("automated-product-collection-checkbox");

    const getErrorModal = () =>
      queryByTestId("automated-product-collections-error-modal");

    return {
      selectCollectionsStepContainer,
      searchInputWrapper,
      getSearchInput,
      selectAllCheckboxWrapper,
      getSelectAllCheckbox,
      getCollectionCheckboxes,
      getErrorModal,
    };
  };

  it("should render the ProductCollectionsStep container", () => {
    const { selectCollectionsStepContainer } = renderProductCollectionsStep();
    expect(selectCollectionsStepContainer).toBeInTheDocument();
  });

  it("should render the SearchInput", () => {
    const { searchInputWrapper } = renderProductCollectionsStep();
    expect(searchInputWrapper).toBeInTheDocument();
  });

  it("should render the 'Select all Collections' checkbox", () => {
    const { getSelectAllCheckbox } = renderProductCollectionsStep();
    expect(getSelectAllCheckbox()).toBeInTheDocument();
  });

  it("should render the Bundle control panel", () => {
    renderProductCollectionsStep(false, [], {
      method: AUTOMATED_DISCOUNT_METHODS.BUNDLE,
    });
    expect(
      screen.getByTestId("bundle-method-amount-count-panel")
    ).toBeInTheDocument();
  });

  it("should render the default list of collections", async () => {
    const { getCollectionCheckboxes } = renderProductCollectionsStep();

    expect(await screen.findByText("Collection 0")).toBeInTheDocument();
    expect(getCollectionCheckboxes()).toHaveLength(
      testProductCollections.length
    );
  });

  it("should be able to filter collections using the search input", async () => {
    const { getSearchInput, getCollectionCheckboxes } =
      renderProductCollectionsStep();

    expect(await screen.findByText("Collection 0")).toBeInTheDocument();

    await waitFor(() => {
      fireEvent.change(getSearchInput(), {
        target: { value: "Collection 0" },
      });
      expect(getCollectionCheckboxes()).toHaveLength(1);
    });
  });

  it("should display the correct amount of collections filtered in the select all checkbox", async () => {
    const { getSearchInput, selectAllCheckboxWrapper } =
      renderProductCollectionsStep();

    expect(await screen.findByText("Collection 0")).toBeInTheDocument();

    await waitFor(() => {
      fireEvent.change(getSearchInput(), {
        target: { value: "Collection 1" },
      });
      expect(selectAllCheckboxWrapper).toHaveTextContent(
        "Select all Collections (1)"
      );
    });
  });

  it("should be able to select all collections from the select all checkbox", async () => {
    const { getSelectAllCheckbox, getCollectionCheckboxes } =
      renderProductCollectionsStep();

    expect(await screen.findByText("Collection 0")).toBeInTheDocument();

    await waitFor(() => {
      fireEvent.click(getSelectAllCheckbox());
      getCollectionCheckboxes().forEach((checkbox) => {
        expect(checkbox.querySelector("input")).toBeChecked();
      });
    });
  });

  it("should be able to select collections then filter by group, select all and maintain the previous selected collections", async () => {
    const { getSearchInput, getSelectAllCheckbox, getCollectionCheckboxes } =
      renderProductCollectionsStep();

    expect(await screen.findByText("Collection 0")).toBeInTheDocument();

    const collectionCheckboxes = getCollectionCheckboxes();
    const checkbox1 = collectionCheckboxes[0].querySelector("input");
    const checkbox2 = collectionCheckboxes[1].querySelector("input");

    await waitFor(() => {
      fireEvent.click(checkbox1!);
      fireEvent.click(checkbox2!);
      expect(checkbox1).toBeChecked();
      expect(checkbox2).toBeChecked();
    });

    const searchInput = getSearchInput();
    fireEvent.change(searchInput, { target: { value: "Collection 0" } });

    await waitFor(() => {
      fireEvent.click(getSelectAllCheckbox());
      const filteredCollectionCheckboxes = getCollectionCheckboxes();
      filteredCollectionCheckboxes.forEach((checkbox) => {
        expect(checkbox.querySelector("input")).toBeChecked();
      });
    });

    const searchInputAfterFilter = getSearchInput();
    fireEvent.change(searchInputAfterFilter, { target: { value: "" } });

    await waitFor(() => {
      const allCollectionCheckboxes = getCollectionCheckboxes();
      expect(allCollectionCheckboxes[0].querySelector("input")).toBeChecked();
      expect(allCollectionCheckboxes[1].querySelector("input")).toBeChecked();
    });
  });

  it("should be able to select a single or multiple checkboxes from the collections list", async () => {
    const { getCollectionCheckboxes } = renderProductCollectionsStep();

    expect(await screen.findByText("Collection 0")).toBeInTheDocument();

    const collectionCheckboxes = getCollectionCheckboxes();

    const checkbox1 = collectionCheckboxes[0].querySelector("input");
    const checkbox2 = collectionCheckboxes[1].querySelector("input");

    await waitFor(() => {
      checkbox1?.click();
      checkbox2?.click();
      expect(checkbox1).toBeChecked();
      expect(checkbox2).toBeChecked();
    });
  });

  it("Should use updatedAt desc as secondary sort", async () => {
    const { getCollectionCheckboxes } = renderProductCollectionsStep();

    expect(await screen.findByText("Collection 0")).toBeInTheDocument();

    const collectionCheckboxes = getCollectionCheckboxes();

    const checkbox1Label = collectionCheckboxes[0].querySelector(
      ".MuiFormControlLabel-label "
    );
    const checkbox2Label = collectionCheckboxes[1].querySelector(
      ".MuiFormControlLabel-label "
    );

    await waitFor(() => {
      expect(checkbox1Label?.innerHTML).toBe("Collection 0");
      expect(checkbox2Label?.innerHTML).toBe("Collection 1");
    });
  });

  it("should not display the modal if collections are present", async () => {
    const { getErrorModal } = renderProductCollectionsStep(
      false,
      testAutomatedDiscountsResponse[0].collections
    );

    await waitFor(() => {
      const errorModal = getErrorModal();
      expect(errorModal).not.toBeInTheDocument();
    });
  });

  it("should display the modal if no product collections are selected", async () => {
    const { getErrorModal } = renderProductCollectionsStep(true);

    await waitFor(() => {
      const errorModal = getErrorModal();
      expect(errorModal).toBeInTheDocument();
      expect(errorModal).toHaveTextContent(
        "Add a Product Collection to select which products are eligible for the discount"
      );
    });
  });

  it("should have a link for each collection", async () => {
    const { getCollectionCheckboxes } = renderProductCollectionsStep();

    expect(await screen.findByText("Collection 0")).toBeInTheDocument();

    const collectionCheckboxes = getCollectionCheckboxes();

    const collection1Link =
      collectionCheckboxes[0].parentElement?.parentElement?.querySelector("a");
    const collection2Link =
      collectionCheckboxes[1].parentElement?.parentElement?.querySelector("a");

    await waitFor(() => {
      expect(collection1Link).toBeInTheDocument();
      expect(collection1Link?.href).toContain("/0");

      expect(collection2Link).toBeInTheDocument();
      expect(collection2Link?.href).toContain("/1");
    });
  });

  it("should show enable threshold when discount is bundle and discount unit is percentage", async () => {
    renderProductCollectionsStep(false, [], {
      method: AUTOMATED_DISCOUNT_METHODS.BUNDLE,
      conditions: {
        customerCapEnabled: false,
        customerCapValue: null,
        customerLimitEnabled: false,
        customerLimitValue: null,
        customerEventEnabled: false,
        customerEvents: null,
        customerGroupsEnabled: false,
        customerLicenseType: null,
        customerLicenseTypeEnabled: false,
        fulfillmentTypes: null,
        fulfillmentTypesEnabled: false,
        purchaseMinimumEnabled:  false,
        redemptionLimitEnabled: false,
        redemptionLimitValue: null,
        packageAgeEnabled: false,
        bogoConditions: null,
        purchaseMinimumType: null,
        purchaseMinimumValue: null,
        bundleConditions: {
          buyCount: 1,
          discountUnit: BundleDiscountMethods.PERCENT,
          purchaseRequirement: BundlePurchaseRequirement.UNIT_COUNT,
          threshold: true
        }
      }
    });

    expect(await screen.getByTestId("bundle-threshold-checkbox")).toBeInTheDocument();

  })
});
