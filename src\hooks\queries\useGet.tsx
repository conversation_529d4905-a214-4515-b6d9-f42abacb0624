import { useEffect, useReducer } from "react";
import axios from "axios";
import ApiService from "../../services/api/apiService";

const LOADING = "LOADING" as const;
const SUCCESS = "SUCCESS" as const;
const FAILURE = "FAILURE" as const;

let ERROR_MSG = "There was a problem while retrieving data";

export type TStateUseGet<Data> =
  | { loading: true; error: undefined; data: undefined }
  | { loading: false; error: typeof ERROR_MSG; data: undefined }
  | { loading: false; error: undefined; data: Data };

export type TRefetchUseGet = () => void;

type TActionUseGet<Data> =
  | { type: typeof LOADING }
  | { type: typeof SUCCESS; payload: Data }
  | { type: typeof FAILURE };

function reducer<Data>(
  _: TStateUseGet<Data>,
  action: TActionUseGet<Data>
): TStateUseGet<Data> {
  if (action.type === LOADING) {
    return {
      error: undefined,
      loading: true,
      data: undefined,
    };
  }
  if (action.type === SUCCESS) {
    return {
      error: undefined,
      loading: false,
      data: action.payload,
    };
  }
  return {
    error: ERROR_MSG,
    loading: false,
    data: undefined,
  };
}

const initialState: TStateUseGet<never> = {
  loading: true,
  error: undefined,
  data: undefined,
};

export default function useGet<Data>(
  apiInstance: ApiService,
  endpoint: string
): [TStateUseGet<Data>, TRefetchUseGet] {
  const [state, dispatch] = useReducer(reducer<Data>, initialState);

  const getDataAsync = async () => {
    try {
      dispatch({ type: LOADING });
      const { data } = await apiInstance.get(endpoint);
      dispatch({ type: SUCCESS, payload: data });
    } catch (error: Error | unknown) {
      let errorMessage = ERROR_MSG;

      if (axios.isAxiosError(error)) {
        const axiosError = error;
        errorMessage = axiosError.response?.data?.errorResponse || ERROR_MSG;
      }
      ERROR_MSG = errorMessage;
      dispatch({ type: FAILURE });
    }
  };

  useEffect(() => {
    getDataAsync();
  }, []);

  return [state, getDataAsync];
}
