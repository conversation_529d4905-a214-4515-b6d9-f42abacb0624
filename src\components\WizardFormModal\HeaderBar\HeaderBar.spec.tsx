import React from "react";
import { <PERSON><PERSON>erRouter as Router } from "react-router-dom";
import { render, screen } from "@testing-library/react";
import { TreezThemeProvider, textColors } from "@treez-inc/component-library";
import iconAriaLabels from "@treez-inc/component-library/dist/components/Icon/icon-library/icon-aria-labels";
import HeaderBar, { HeaderBarProps } from ".";

describe("<HeaderBar />", () => {
  const renderHeaderBar = (props: Pick<HeaderBarProps, "subheader">) => {
    render(
      <Router>
        <TreezThemeProvider>
          <HeaderBar {...props} iconName="Add" header="Test Header Bar" />
        </TreezThemeProvider>
      </Router>
    );
    const { getByTestId, queryByTestId } = screen;

    const headerBar = () => queryByTestId("header-bar");
    const iconWrapper = () => queryByTestId("header-bar-icon");
    const icon = iconWrapper()?.querySelector("span");
    const header = getByTestId("header-bar-header");
    const subheader = () => queryByTestId("header-bar-subheader");

    return { headerBar, iconWrapper, icon, header, subheader };
  };

  it("should render the icon if the iconName prop is passed", () => {
    const { iconWrapper, icon } = renderHeaderBar({});

    expect(iconWrapper()).toBeInTheDocument();
    expect(icon).toHaveAttribute("aria-label", iconAriaLabels.Add);
  });

  it("should render the header text", () => {
    const { header } = renderHeaderBar({});

    expect(header).toBeInTheDocument();
    expect(header).toHaveTextContent("Test Header Bar");
    expect(header).toHaveStyle(`color: ${textColors.primaryBlackText}`);
    expect(header).toHaveStyle(`font-size: 1.438rem`);
    expect(header).toHaveStyle(`font-weight: 400`);
  });

  it("should render the subheader text when the subheader prop is passed", () => {
    const { subheader } = renderHeaderBar({ subheader: "Test Subheader" });

    expect(subheader()).toBeInTheDocument();
    expect(subheader()).toHaveTextContent("Test Subheader");
    expect(subheader()).toHaveStyle(`color: ${textColors.primaryBlackText}`);
    expect(subheader()).toHaveStyle(`font-size: 0.875rem`);
    expect(subheader()).toHaveStyle(`font-weight: 400`);
  });
});
