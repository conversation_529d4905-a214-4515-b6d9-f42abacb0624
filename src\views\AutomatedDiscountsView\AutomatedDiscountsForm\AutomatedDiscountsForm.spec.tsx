import React from "react";
import { <PERSON><PERSON>erRouter as Router } from "react-router-dom";
import {
  fireEvent,
  render,
  screen,
  waitFor,
  waitForElementToBeRemoved,
} from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { TreezThemeProvider } from "@treez-inc/component-library";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HttpResponse, http } from "msw";
import ApiService from "../../../services/api/apiService";
import { tokens } from "../../../test/constants";
import AutomatedDiscountsForm from ".";
import { server } from "../../../test/mocks/node";
import { upsertDiscountUrl } from "../../../services/apiEndPoints";
import { AUTOMATED_DISCOUNT_METHODS } from "../../../constants/discountForm";

const mockApiService = new ApiService(
  () => tokens,
  () => {}
);

jest.mock("../../../hooks/usePageLoading");
const mockUsePageLoading = require("../../../hooks/usePageLoading").default;

const mockNavigate = jest.fn();

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => mockNavigate,
}));

const mockOpenSnackbar = jest.fn();
jest.mock("../../../providers/SnackbarProvider", () => ({
  useSnackbar: jest.fn(() => ({ openSnackbar: mockOpenSnackbar })),
}));

jest.mock("../../../hooks/mutations/useCreateDiscount", () => ({
  __esModule: true,
  default: jest.fn(() => [
    jest.fn().mockResolvedValue({
      message: "New automated discount created successfully",
    }),
    {
      loading: false,
      error: undefined,
      data: {},
    },
  ]),
}));

jest.mock("../../../hooks/queries/useGetEntities", () => ({
  __esModule: true,
  default: jest.fn(),
}));

const mockUseGetEntities =
  require("../../../hooks/queries/useGetEntities").default;

describe("AutomatedDiscountsForm", () => {
  beforeEach(() => {
    mockUsePageLoading.mockReturnValue({
      isPageLoading: false,
      setIsPageLoading: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const renderAutomatedDiscountsForm = (
    storeCount = 2,
    isEditMode = false,
    writePermission = true
  ) => {
    const mockEntityList = {
      data: new Array(storeCount).fill({}),
    };

    mockUseGetEntities.mockImplementation(() => [mockEntityList]);

    const queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });

    render(
      <TreezThemeProvider>
        <QueryClientProvider client={queryClient}>
          <Router>
            <AutomatedDiscountsForm
              api={mockApiService}
              permissions={{ write: writePermission, read: true, manageTags: true }}
              isEditMode={isEditMode}
            />
          </Router>
        </QueryClientProvider>
      </TreezThemeProvider>
    );

    const { getByTestId, getAllByTestId, queryByTestId } = screen;

    const wizardFormModal = getByTestId("automated-discounts-add-modal");
    const closeButton = getByTestId("close-form");
    const header = getByTestId("header-bar");
    const linearStepper = getByTestId("automated-discount-form-steps");
    const formFooter = getByTestId("automated-discounts-footer");
    const primaryButton = getByTestId("next-finish-button");
    const secondaryButton = () => queryByTestId("back-button");
    const tertiaryButton = () => queryByTestId("done-editing-button");
    const loadingSpinner = () => queryByTestId("loading-spinner");

    const discountDetailsStep = () =>
      queryByTestId("automated-discounts-details-step");
    const discountTitleInput = () => queryByTestId("discount-title-input");
    const discountMethodSelect = () => queryByTestId("discount-method-select");
    const discountAmountInput = () => queryByTestId("discount-amount-input");

    const productCollectionsStep = () =>
      queryByTestId("automated-product-collections-step");
    const productCollectionsCheckboxes = () =>
      getAllByTestId("automated-product-collection-checkbox");

    const selectStoresStep = () => queryByTestId("automated-select-store-step");
    const scheduleDiscountStep = () =>
      queryByTestId("automated-schedule-discount-step");
    const setConditionsStep = () =>
      queryByTestId("automated-set-conditions-step");

    return {
      wizardFormModal,
      closeButton,
      linearStepper,
      header,
      formFooter,
      primaryButton,
      secondaryButton,
      tertiaryButton,
      discountDetailsStep,
      discountTitleInput,
      discountMethodSelect,
      discountAmountInput,
      productCollectionsStep,
      productCollectionsCheckboxes,
      selectStoresStep,
      scheduleDiscountStep,
      setConditionsStep,
      loadingSpinner,
    };
  };

  const setupAndNavigateToStep = async (
    stepNumber: number,
    storeCount: number,
    isEditMode: boolean = false,
    writePermission: boolean = true
  ) => {
    const formUtils = renderAutomatedDiscountsForm(
      storeCount,
      isEditMode,
      writePermission
    );
    const {
      primaryButton,
      discountTitleInput,
      discountAmountInput,
      setConditionsStep,
      selectStoresStep,
      scheduleDiscountStep,
      productCollectionsCheckboxes,
      productCollectionsStep,
    } = formUtils;

    // Get to Product Collections Step
    if (stepNumber >= 1) {
      fireEvent.change(discountTitleInput()!.querySelector("input")!, {
        target: { value: "Test Title" },
      });

      fireEvent.change(discountAmountInput()!.querySelector("input")!, {
        target: { value: "100" },
      });

      fireEvent.click(primaryButton);

      await waitFor(() => {
        expect(productCollectionsStep()).toBeInTheDocument();
      });
      expect(await screen.findByText("Collection 0")).toBeInTheDocument();
    }

    // Get to Select Stores Step
    if (stepNumber >= 2 && storeCount > 1) {
      const collectionCheckbox = productCollectionsCheckboxes()![0];
      collectionCheckbox.click();

      fireEvent.click(primaryButton);

      await waitFor(() => {
        expect(selectStoresStep()).toBeInTheDocument();
      });
    }

    // Get to Schedule Discount Step
    if (stepNumber >= 3) {
      fireEvent.click(primaryButton);

      await waitFor(() => {
        expect(scheduleDiscountStep()).toBeInTheDocument();
      });
    }

    // Get to Set Conditions Step
    if (stepNumber >= 4 && storeCount > 1) {
      fireEvent.click(primaryButton);

      await waitFor(() => {
        expect(setConditionsStep()).toBeInTheDocument();
      });
    }

    return formUtils;
  };

  it("should render the automated discounts form ", () => {
    const { wizardFormModal } = renderAutomatedDiscountsForm();
    expect(wizardFormModal).toBeInTheDocument();
  });

  describe("header bar", () => {
    it("should render the header with appropriate elements", () => {
      const { header } = renderAutomatedDiscountsForm();
      const icon = screen.getByTestId("header-bar-icon");
      const headerText = screen.getByTestId("header-bar-header");
      expect(header).toBeInTheDocument();
      expect(headerText).toContainHTML("Add Automated Discount");
      expect(icon).toBeInTheDocument();
      expect(icon.firstChild).toContainHTML("horizontal_rule");
    });
  });

  it("should render the 'Close' button", () => {
    const { closeButton } = renderAutomatedDiscountsForm();
    expect(closeButton).toBeInTheDocument();
    expect(closeButton).toHaveTextContent("Close");
    expect(closeButton).toHaveTextContent("chevron_left");
  });

  it("should navigate the user to /automated when the 'Close' button is clicked", async () => {
    const { closeButton } = renderAutomatedDiscountsForm();
    expect(closeButton).toBeInTheDocument();

    await userEvent.click(closeButton);

    expect(mockNavigate).toHaveBeenCalledWith("/automated");
  });

  it("should render the linear stepper ", () => {
    const { linearStepper } = renderAutomatedDiscountsForm();
    expect(linearStepper).toBeInTheDocument();
    expect(linearStepper).toHaveTextContent("Discount Details");
    expect(linearStepper).toHaveTextContent("Select Product Collections");
    expect(linearStepper).toHaveTextContent("Select Stores");
    expect(linearStepper).toHaveTextContent("Schedule Discount");
    expect(linearStepper).toHaveTextContent("Set Conditions");
  });

  it("should render the form footer ", () => {
    const { formFooter } = renderAutomatedDiscountsForm();
    expect(formFooter).toBeInTheDocument();
  });

  describe("Step 1: Discount Details", () => {
    it("should render the first step in the form", () => {
      const { wizardFormModal } = renderAutomatedDiscountsForm();
      expect(wizardFormModal).toHaveTextContent("Discount Details");
    });

    it("should render error validation message when the form is not complete and the 'Next' button is clicked", async () => {
      const { primaryButton, discountTitleInput, discountAmountInput } =
        renderAutomatedDiscountsForm();

      await userEvent.click(primaryButton);

      const titleHelperText = discountTitleInput()?.querySelector("p");
      const amountHelperText = discountAmountInput()?.querySelector("p");

      expect(titleHelperText).toHaveTextContent(/Discount title is required/);
      expect(amountHelperText).toHaveTextContent(/Amount is required/);
    });

    it("should not render the amount input when method selected is BOGO", async () => {
      const user = userEvent.setup();
      const { discountMethodSelect, discountAmountInput } =
        renderAutomatedDiscountsForm();

      expect(discountMethodSelect()).toBeInTheDocument();
      await user.click(discountMethodSelect()!.firstElementChild!);

      await screen.findByText(AUTOMATED_DISCOUNT_METHODS.BOGO);

      user.click(screen.getByText(AUTOMATED_DISCOUNT_METHODS.BOGO));

      await waitForElementToBeRemoved(discountAmountInput());
    });

    it("triggers validation on amount input when editing method", async () => {
      const { discountTitleInput, discountAmountInput, discountMethodSelect } =
        renderAutomatedDiscountsForm();

      fireEvent.change(discountTitleInput()!.querySelector("input")!, {
        target: { value: "Test Title" },
      });

      fireEvent.change(discountAmountInput()!.querySelector("input")!, {
        target: { value: "120" },
      });

      fireEvent.change(discountMethodSelect()!.querySelector("input")!, {
        target: { value: AUTOMATED_DISCOUNT_METHODS.PERCENT },
      });

      await waitFor(() => {
        expect(
          discountAmountInput()!.querySelector("#Amount-helper-text")
            ?.textContent
        ).toContain("Please enter a number smaller than or equal to 100%");
      });
    });

    it("Shows a specific message for dollar discounts", async () => {
      const { discountMethodSelect } = renderAutomatedDiscountsForm();

      fireEvent.change(discountMethodSelect()!.querySelector("input")!, {
        target: { value: AUTOMATED_DISCOUNT_METHODS.DOLLAR },
      });

      await waitFor(() => {
        expect(
          screen.getByTestId("automated-discount-method-description")
        ).toHaveTextContent(
          "Automated dollar discounts will not be applied to bulk products. Manual discounts can still be applied"
        );
      });
    });

    it("Shows a specific message for COST_PLUS discounts", async () => {
      const { discountMethodSelect } = renderAutomatedDiscountsForm();

      fireEvent.change(discountMethodSelect()!.querySelector("input")!, {
        target: { value: AUTOMATED_DISCOUNT_METHODS.COST_PLUS },
      });

      await waitFor(() => {
        expect(
          screen.getByTestId("automated-discount-method-description")
        ).toHaveTextContent(
          "The discounted price is the wholesale cost plus a percentage."
        );
      });
    });

    it("Shows a specific message for BOGO discounts", async () => {
      const { discountMethodSelect } = renderAutomatedDiscountsForm();

      fireEvent.change(discountMethodSelect()!.querySelector("input")!, {
        target: { value: AUTOMATED_DISCOUNT_METHODS.BOGO },
      });

      await waitFor(() => {
        expect(
          screen.getByTestId("automated-discount-method-description")
        ).toHaveTextContent(
          "The BOGO discount terms ($ and % off or Priced At) are defined in the next step."
        );
      });
    });

    it("should render the Product Collections Step on 'Next' button click when the required form fields are completed", async () => {
      const {
        primaryButton,
        discountTitleInput,
        discountAmountInput,
        productCollectionsStep,
      } = renderAutomatedDiscountsForm();

      fireEvent.change(discountTitleInput()!.querySelector("input")!, {
        target: { value: "Test Title" },
      });
      fireEvent.change(discountAmountInput()!.querySelector("input")!, {
        target: { value: "100" },
      });

      fireEvent.click(primaryButton);

      await waitFor(() => {
        expect(productCollectionsStep()).toBeInTheDocument();
      });
    });

    describe("footer buttons", () => {
      it("should render the 'Next' button", () => {
        const { primaryButton } = renderAutomatedDiscountsForm();
        expect(primaryButton).toBeInTheDocument();
        expect(primaryButton).toHaveTextContent("Next");
      });

      it("should render the 'Done Editing' button when isEditMode is true", () => {
        const { tertiaryButton } = renderAutomatedDiscountsForm(2, true);
        expect(tertiaryButton()).toBeInTheDocument();
        expect(tertiaryButton()).toHaveTextContent("Done Editing");
      });

      it("should show the confirmation modal if the 'Done Editing' button is clicked", async () => {
        const { tertiaryButton } = renderAutomatedDiscountsForm(2, true);

        expect(tertiaryButton()).toBeInTheDocument();
        expect(tertiaryButton()).toHaveTextContent("Done Editing");

        fireEvent.click(tertiaryButton()!);

        await waitFor(() => {
          expect(
            screen.getByTestId("automated-discounts-confirm-edit-modal")
          ).toBeInTheDocument();
        });
      });

      it("should hide the confirmation modal if close button is clicked", async () => {
        const { tertiaryButton } = renderAutomatedDiscountsForm(2, true);

        expect(tertiaryButton()).toBeInTheDocument();
        expect(tertiaryButton()).toHaveTextContent("Done Editing");

        fireEvent.click(tertiaryButton()!);

        let confirmationModal: HTMLElement;

        await waitFor(() => {
          confirmationModal = screen.getByTestId(
            "automated-discounts-confirm-edit-modal"
          );
          expect(confirmationModal).toBeInTheDocument();
        });

        const closeModalButton = confirmationModal!.querySelector(
          '[aria-label="Close-button"]'
        );

        fireEvent.click(closeModalButton!);

        await waitFor(() => {
          expect(confirmationModal).not.toBeInTheDocument();
        });
      });

      it("should not show the confirmation modal if the 'Done Editing' button is clicked when the user does not have permissions", async () => {
        const { tertiaryButton } = renderAutomatedDiscountsForm(2, true, false);

        expect(tertiaryButton()).toBeInTheDocument();
        expect(tertiaryButton()).toHaveTextContent("Done Editing");

        fireEvent.click(tertiaryButton()!);

        await waitFor(() => {
          expect(
            screen.queryByTestId("automated-discounts-confirm-edit-modal")
          ).not.toBeInTheDocument();
        });
      });
    });
  });

  describe("Step 2: Select Product Collections", () => {
    it("should render the second step in the form", async () => {
      const formUtils = await setupAndNavigateToStep(1, 2);
      expect(formUtils.productCollectionsStep()).toBeInTheDocument();
    });

    it("should render the BOGO fields when the Discount method is set to BOGO", async () => {
      const user = userEvent.setup();
      const { discountTitleInput, discountMethodSelect } =
        renderAutomatedDiscountsForm();
      expect(discountMethodSelect()).toBeInTheDocument();

      const titleInput = discountTitleInput()!.querySelector("input")!;
      await user.type(titleInput, "Auto Discount Test");

      await user.click(discountMethodSelect()!.firstElementChild!);

      const bogoSelectItem = await screen.findByText(
        AUTOMATED_DISCOUNT_METHODS.BOGO
      );
      user.click(bogoSelectItem);
      user.click(screen.getByTestId("next-finish-button"));

      await waitFor(() => {
        expect(screen.queryByText("Set Customer Buy Count")).not.toBeNull();
        expect(
          screen.queryByText("Discount Method and Items to Get")
        ).not.toBeNull();
        expect(
          screen.queryByText("Select Products to Activate the Discount")
        ).not.toBeNull();
      });
    });

    it("should be able to go to the first step via picker after selecting a collection", async () => {
      const { getByTestId } = screen;

      const {
        primaryButton,
        discountTitleInput,
        discountMethodSelect,
        discountAmountInput,
        productCollectionsCheckboxes,
      } = renderAutomatedDiscountsForm();
      expect(discountMethodSelect()).toBeInTheDocument();

      fireEvent.change(discountTitleInput()!.querySelector("input")!, {
        target: { value: "Test Title" },
      });

      fireEvent.change(discountAmountInput()!.querySelector("input")!, {
        target: { value: "100" },
      });

      fireEvent.change(discountMethodSelect()!.querySelector("input")!, {
        target: { value: AUTOMATED_DISCOUNT_METHODS.PERCENT },
      });

      fireEvent.click(primaryButton);

      await waitFor(async () => {
        const collectionCheckbox = productCollectionsCheckboxes()![0];
        expect(collectionCheckbox).toBeInTheDocument();
        collectionCheckbox.click();

        const firstStepPickerItem = getByTestId(
          "automated-discount-form-steps-step-0"
        );

        const firstStepItemButton = firstStepPickerItem.querySelector("button");
        expect(firstStepItemButton).not.toHaveClass("Mui-disabled");

        await userEvent.click(firstStepItemButton!);
      });

      await waitFor(() => {
        expect(discountTitleInput()).toBeInTheDocument();
      });
    });

    it("should be able to go to the first step via picker without selecting any collection", async () => {
      const { getByTestId } = screen;

      const {
        primaryButton,
        discountTitleInput,
        discountMethodSelect,
        discountAmountInput,
      } = renderAutomatedDiscountsForm();
      expect(discountMethodSelect()).toBeInTheDocument();

      fireEvent.change(discountTitleInput()!.querySelector("input")!, {
        target: { value: "Test Title" },
      });

      fireEvent.change(discountAmountInput()!.querySelector("input")!, {
        target: { value: "100" },
      });

      fireEvent.change(discountMethodSelect()!.querySelector("input")!, {
        target: { value: AUTOMATED_DISCOUNT_METHODS.PERCENT },
      });

      fireEvent.click(primaryButton);

      await waitFor(async () => {
        const firstStepPickerItem = getByTestId(
          "automated-discount-form-steps-step-0"
        );

        const firstStepItemButton = firstStepPickerItem.querySelector("button");
        expect(firstStepItemButton).not.toHaveClass("Mui-disabled");

        await userEvent.click(firstStepItemButton!);
      });

      await waitFor(() => {
        expect(discountTitleInput()).toBeInTheDocument();
      });
    });

    it("should render the 'Done Editing' button when in edit mode", async () => {
      const formUtils = await setupAndNavigateToStep(1, 2, true);
      const doneEditingButton = formUtils.tertiaryButton();
      expect(doneEditingButton).toBeInTheDocument();
      expect(doneEditingButton).toHaveTextContent("Done Editing");
    });
  });

  describe("Step 3: Select Stores", () => {
    it("should render the 'Select Stores' step when storeCount is more than 1", async () => {
      const formUtils = await setupAndNavigateToStep(2, 2);
      expect(formUtils.selectStoresStep()).toBeInTheDocument();
    });

    it("should not render the 'Select Stores' step when storeCount is 1", async () => {
      const formUtils = await setupAndNavigateToStep(2, 1);
      expect(formUtils.selectStoresStep()).not.toBeInTheDocument();
    });

    describe("Multi Store Organizations", () => {
      describe("footer buttons", () => {
        it("should render the 'Next' button", async () => {
          const formUtils = await setupAndNavigateToStep(2, 2);
          expect(formUtils.primaryButton).toBeInTheDocument();
          expect(formUtils.primaryButton).toHaveTextContent("Next");
        });

        it("should render the 'Select Product Collections' back button", async () => {
          const formUtils = await setupAndNavigateToStep(2, 2);
          expect(formUtils.secondaryButton()).toBeInTheDocument();
          expect(formUtils.secondaryButton()).toHaveTextContent(
            "Select Product Collections"
          );
        });

        it("should render the 'Select Stores' step when storeCount is more than 1", async () => {
          const formUtils = await setupAndNavigateToStep(2, 2);
          expect(formUtils.selectStoresStep()).toBeInTheDocument();
        });

        it("should render the 'Done Editing' button when in edit mode", async () => {
          const formUtils = await setupAndNavigateToStep(2, 2, true);
          const doneEditingButton = formUtils.tertiaryButton();
          expect(doneEditingButton).toBeInTheDocument();
          expect(doneEditingButton).toHaveTextContent("Done Editing");
        });
      });
    });
  });

  describe("Step 4: Schedule Discount", () => {
    it("should render the 'Schedule Discount' step", async () => {
      const formUtils = await setupAndNavigateToStep(3, 2);
      expect(formUtils.scheduleDiscountStep()).toBeInTheDocument();
    });

    describe("footer buttons", () => {
      it("should render the 'Select Stores' back button", async () => {
        const formUtils = await setupAndNavigateToStep(3, 2);
        expect(formUtils.secondaryButton()).toBeInTheDocument();
        expect(formUtils.secondaryButton()).toHaveTextContent("Select Stores");
      });

      it("should render the 'Next' button", () => {
        const { primaryButton } = renderAutomatedDiscountsForm();
        expect(primaryButton).toBeInTheDocument();
        expect(primaryButton).toHaveTextContent("Next");
      });

      it("should render the 'Done Editing' button when isEditMode is true", () => {
        const { tertiaryButton } = renderAutomatedDiscountsForm(3, true);
        expect(tertiaryButton()).toBeInTheDocument();
        expect(tertiaryButton()).toHaveTextContent("Done Editing");
      });
    });
  });

  describe("Step 5: Set Conditions", () => {
    it("should render the 'Set Conditions' step", async () => {
      const formUtils = await setupAndNavigateToStep(4, 2);
      expect(formUtils.setConditionsStep()).toBeInTheDocument();
    });

    describe("footer buttons", () => {
      it("should render the 'Schedule Discount' back button", async () => {
        const formUtils = await setupAndNavigateToStep(4, 2);
        expect(formUtils.secondaryButton()).toBeInTheDocument();
        expect(formUtils.secondaryButton()).toHaveTextContent(
          "Schedule Discount"
        );
      });

      it("should render the correct 'Done Editing' button when in edit mode", async () => {
        const formUtils = await setupAndNavigateToStep(4, 2, true);
        const doneEditingButton = formUtils.tertiaryButton();

        expect(doneEditingButton).not.toBeInTheDocument();
        expect(formUtils.primaryButton).toBeInTheDocument();
        expect(formUtils.primaryButton).toHaveTextContent("Finish");
      });
    });

    describe("on 'Finish' button click", () => {
      it("should navigate to '/automated' on successful discount creation", async () => {
        const formUtils = await setupAndNavigateToStep(4, 2);
        fireEvent.click(formUtils.primaryButton);

        await waitFor(() => {
          expect(mockNavigate).toHaveBeenCalledTimes(1);
          expect(mockNavigate).toHaveBeenCalledWith("/automated");
        });
      });

      it("should not navigate on click if user lacks permissions", async () => {
        const formUtils = await setupAndNavigateToStep(4, 2, false, false);
        await userEvent.click(formUtils.primaryButton);
        expect(mockNavigate).not.toHaveBeenCalled();
      });

      it("should render a success snackbar when the discount is created successfully", async () => {
        const formUtils = await setupAndNavigateToStep(4, 2);
        expect(formUtils.primaryButton).toHaveTextContent("Finish");

        await userEvent.click(formUtils.primaryButton);

        expect(mockOpenSnackbar).toHaveBeenCalledWith({
          severity: "info",
          iconName: "Success",
          message: '"Test Title" discount has been created',
        });
      });

      it("should submit the form with isManual false", async () => {
        const formUtils = await setupAndNavigateToStep(4, 2);
        expect(formUtils.primaryButton).toHaveTextContent("Finish");

        fireEvent.click(formUtils.primaryButton);

        jest.spyOn(mockApiService, "post");

        await waitFor(() => {
          expect(mockApiService.post).toHaveBeenCalledWith(
            expect.any(String),
            expect.objectContaining({
              isManual: false,
            })
          );
        });
      });

      it("should render an error snackbar when the discount is not created", async () => {
        server.use(
          http.post(
            upsertDiscountUrl,
            () => new HttpResponse(null, { status: 401 }),
            {
              once: true,
            }
          )
        );

        jest.spyOn(console, "error").mockImplementation(jest.fn());

        const formUtils = await setupAndNavigateToStep(4, 2);
        fireEvent.click(formUtils.primaryButton);
        await waitFor(() => {
          expect(mockOpenSnackbar).toHaveBeenCalledWith({
            severity: "error",
            message: "Unable to create the discount. Please try again",
          });
        });
      });

      it("should render an error snackbar when the discount is not created due to a network error", async () => {
        server.use(
          http.post(upsertDiscountUrl, () => HttpResponse.error(), {
            once: true,
          })
        );

        const formUtils = await setupAndNavigateToStep(4, 2);
        fireEvent.click(formUtils.primaryButton);

        await waitFor(() => {
          expect(mockOpenSnackbar).toHaveBeenCalledWith({
            severity: "error",
            message:
              "Your request could not be completed due to a network error",
          });
        });
      });
    });
  });

  describe("loading spinner", () => {
    it("should render the loading spinner when page is loading", () => {
      mockUsePageLoading.mockReturnValue({
        isPageLoading: true,
        setIsPageLoading: jest.fn(),
      });
      const { loadingSpinner } = renderAutomatedDiscountsForm();
      expect(loadingSpinner()).toBeInTheDocument();
    });

    it("should not render the loading spinner when page is NOT loading", () => {
      mockUsePageLoading.mockReturnValue({
        isPageLoading: false,
        setIsPageLoading: jest.fn(),
      });
      const { loadingSpinner } = renderAutomatedDiscountsForm();
      expect(loadingSpinner()).not.toBeInTheDocument();
    });
  });
});
