import React from "react";
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  styled,
} from "@mui/material/";
import { Button, convertPxToRem } from "@treez-inc/component-library";
import ImageViewer from "./ImageViewer";

export const ImagesDialog = styled(Dialog)(() => ({
  ".MuiDialog-paper": {
    borderRadius: convertPxToRem(28),
    width: convertPxToRem(606),
  },
}));

export const DialogContentRow = styled(Box)(() => ({
  padding: `${convertPxToRem(10)} 0`,
  textAlign: "center",
  fontSize: convertPxToRem(15),
  fontStyle: "normal",
  fontWeight: 500,
}));

interface ImageDetailsModalProps {
  imageUrl: string | null;
  onClose: () => void;
}

const ImageDetailsModal = ({ imageUrl, onClose }: ImageDetailsModalProps) => {
  const handleClose = async () => {
    onClose();
  };

  return (
    <ImagesDialog open onClose={onClose} data-testId="image-details-modal">
      <DialogContent>
        <DialogContentRow>
          {imageUrl && <ImageViewer imageUrl={imageUrl} />}
        </DialogContentRow>
      </DialogContent>
      <DialogActions>
        <Button
          label="OK"
          type="button"
          data-testid="image-details-modal-button"
          onClick={handleClose}
        />
      </DialogActions>
    </ImagesDialog>
  );
};

export default ImageDetailsModal;
