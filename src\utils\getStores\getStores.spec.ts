import { getStores } from ".";

describe("retry request", () => {
  it("should not throw with null values", async () => {
    // @ts-ignore
    const stores = getStores(
      JSON.parse(`
        [
            {
                "id": "ed10faed-0f48-4780-abe2-795169d8c2a7",
                "name": "msofoh1",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": null,
                "website": null,
                "customerType": "Both",
                "storeHours": {
                    "fri": "Closed",
                    "mon": "Closed",
                    "sat": "Closed",
                    "sun": "Closed",
                    "thu": "Closed",
                    "tue": "Closed",
                    "wed": "Closed"
                },
                "tags": [],
                "displayName": "msofoh1",
                "status": "Onboarding",
                "license": [],
                "address": {
                    "id": "1bd8dd3f-f07a-475d-8d97-6a893edef583",
                    "entityId": "ed10faed-0f48-4780-abe2-795169d8c2a7",
                    "streetAddress1": "123",
                    "streetAddress2": null,
                    "city": "California",
                    "region": "California",
                    "country": null,
                    "postalCode": "123",
                    "createdAt": "2023-08-23T16:34:35.357Z",
                    "updatedAt": "2023-08-23T16:34:35.357Z",
                    "deletedAt": null
                }
            },
            {
                "id": "7db2a339-ee18-40aa-85d9-8a5b7b31d8e3",
                "name": "msofoh2",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": null,
                "website": null,
                "customerType": "Both",
                "storeHours": {
                    "fri": "Closed",
                    "mon": "Closed",
                    "sat": "Closed",
                    "sun": "Closed",
                    "thu": "Closed",
                    "tue": "Closed",
                    "wed": "Closed"
                },
                "tags": [],
                "displayName": "msofoh2",
                "status": "Onboarding",
                "license": [],
                "address": {
                    "id": "03e25091-9b37-4191-9e85-dd80c78336ce",
                    "entityId": "7db2a339-ee18-40aa-85d9-8a5b7b31d8e3",
                    "streetAddress1": "123",
                    "streetAddress2": null,
                    "city": "California",
                    "region": "Iowa",
                    "country": null,
                    "postalCode": "123",
                    "createdAt": "2023-08-23T16:32:48.937Z",
                    "updatedAt": "2023-08-23T16:32:48.937Z",
                    "deletedAt": null
                }
            },
            {
                "id": "221c4b69-2916-40c5-b568-1319a40cf12d",
                "name": "test6",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": {
                    "fri": "Closed",
                    "mon": "Closed",
                    "sat": "Closed",
                    "sun": "Closed",
                    "thu": "Closed",
                    "tue": "Closed",
                    "wed": "Closed"
                },
                "tags": [],
                "displayName": "test6",
                "status": "Active",
                "license": [
                    {
                        "id": "3626ea87-dc39-4088-800f-9462d9db6830",
                        "entityId": "221c4b69-2916-40c5-b568-1319a40cf12d",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "A12-0000015-LIC",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:14:52.406Z",
                        "updatedAt": "2023-08-15T18:58:00.133Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "8526ddb1-998e-477f-87d2-9082977287e2",
                    "entityId": "221c4b69-2916-40c5-b568-1319a40cf12d",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "California",
                    "country": "COUNTRY",
                    "postalCode": "78130",
                    "createdAt": "2023-01-23T22:14:52.406Z",
                    "updatedAt": "2023-08-15T18:58:00.133Z",
                    "deletedAt": null
                }
            },
            {
                "id": "737392e1-d3e2-4150-87f3-4de16ce586ff",
                "name": "testalyssa",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "4154372829",
                "website": null,
                "customerType": "Both",
                "storeHours": {
                    "fri": "Closed",
                    "mon": "Closed",
                    "sat": "Closed",
                    "sun": "Closed",
                    "thu": "Closed",
                    "tue": "Closed",
                    "wed": "Closed"
                },
                "tags": [],
                "displayName": "testalyssa",
                "status": "Active",
                "license": [
                    {
                        "id": "7e0a6143-17cb-4517-a46f-6a9e68b25054",
                        "entityId": "737392e1-d3e2-4150-87f3-4de16ce586ff",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-**********",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-05-05T18:31:21.680Z",
                        "updatedAt": "2023-08-08T21:57:19.759Z",
                        "deletedAt": null
                    },
                    {
                        "id": "91dd32f1-e190-4901-b010-4fdaa04ccc89",
                        "entityId": "737392e1-d3e2-4150-87f3-4de16ce586ff",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-762656555605",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-05-05T18:31:21.680Z",
                        "updatedAt": "2023-08-08T21:57:19.759Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "af906ea1-68ad-43a2-9cd0-888204cd7fa0",
                    "entityId": "737392e1-d3e2-4150-87f3-4de16ce586ff",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "California",
                    "country": "COUNTRY",
                    "postalCode": "94105",
                    "createdAt": "2023-05-05T18:31:21.680Z",
                    "updatedAt": "2023-08-08T21:57:19.759Z",
                    "deletedAt": null
                }
            },
            {
                "id": "51ce251b-149a-4e30-848f-12304eb22368",
                "name": "testaml2 49 1",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": null,
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [],
                "address": null
            },
            {
                "id": "9a15215b-8c07-4a9a-8f5b-1b003d5bc3e5",
                "name": "testaml2 49 3",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": null,
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [],
                "address": null
            },
            {
                "id": "8e59948d-7c68-40d0-af6e-cc80f6c3794f",
                "name": "testbrad",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [
                    {
                        "id": "0691b7c9-c1f5-4b6f-ba82-ab3363059548",
                        "entityId": "8e59948d-7c68-40d0-af6e-cc80f6c3794f",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-897689656",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:44:51.116Z",
                        "updatedAt": "2023-01-23T22:44:51.116Z",
                        "deletedAt": null
                    },
                    {
                        "id": "a2c9f6c3-9a1b-4e37-86f2-634369d736fc",
                        "entityId": "8e59948d-7c68-40d0-af6e-cc80f6c3794f",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-56787678",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:44:51.116Z",
                        "updatedAt": "2023-01-23T22:44:51.116Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "dd03f9aa-01aa-4073-bf9a-7dab37a92377",
                    "entityId": "8e59948d-7c68-40d0-af6e-cc80f6c3794f",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-01-23T22:44:51.116Z",
                    "updatedAt": "2023-01-23T22:44:51.116Z",
                    "deletedAt": null
                }
            },
            {
                "id": "1d4e12f9-4716-4c23-9123-1631b2501ebd",
                "name": "testbradauto",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [
                    {
                        "id": "fe1f46fb-743b-4945-8e37-bda683643239",
                        "entityId": "1d4e12f9-4716-4c23-9123-1631b2501ebd",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-876787678",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:44:52.077Z",
                        "updatedAt": "2023-01-23T22:44:52.077Z",
                        "deletedAt": null
                    },
                    {
                        "id": "f555402c-991a-4700-a39e-99745e976a83",
                        "entityId": "1d4e12f9-4716-4c23-9123-1631b2501ebd",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-456765678",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:44:52.077Z",
                        "updatedAt": "2023-01-23T22:44:52.077Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "df9a2d69-0ae2-495b-b39f-b15ee63174da",
                    "entityId": "1d4e12f9-4716-4c23-9123-1631b2501ebd",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-01-23T22:44:52.077Z",
                    "updatedAt": "2023-01-23T22:44:52.077Z",
                    "deletedAt": null
                }
            },
            {
                "id": "5b653128-7196-4c58-883e-d192a9629172",
                "name": "testdavid",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Onboarding",
                "license": [
                    {
                        "id": "de59c681-5baf-4169-80de-4172ff562a23",
                        "entityId": "5b653128-7196-4c58-883e-d192a9629172",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-9837495702305",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-08-31T17:46:12.725Z",
                        "updatedAt": "2023-08-31T17:46:12.725Z",
                        "deletedAt": null
                    },
                    {
                        "id": "8025aef4-1356-4c8a-b320-0e106f271471",
                        "entityId": "5b653128-7196-4c58-883e-d192a9629172",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-9873453045722067",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-08-31T17:46:12.725Z",
                        "updatedAt": "2023-08-31T17:46:12.725Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "37c534a1-da89-4226-aa72-069bcadcceca",
                    "entityId": "5b653128-7196-4c58-883e-d192a9629172",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-08-31T17:46:12.725Z",
                    "updatedAt": "2023-08-31T17:46:12.725Z",
                    "deletedAt": null
                }
            },
            {
                "id": "88233d9f-b8f3-4c97-ab62-3cb04f7e4c52",
                "name": "testdavidauto",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Onboarding",
                "license": [
                    {
                        "id": "889e6630-e2ea-4e06-a1bb-83a94e7c90bc",
                        "entityId": "88233d9f-b8f3-4c97-ab62-3cb04f7e4c52",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-76243807252035",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-08-31T17:26:55.452Z",
                        "updatedAt": "2023-08-31T17:26:55.452Z",
                        "deletedAt": null
                    },
                    {
                        "id": "9c933d84-afe1-482e-b936-1cc75cfba41d",
                        "entityId": "88233d9f-b8f3-4c97-ab62-3cb04f7e4c52",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-0198276381204",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-08-31T17:26:55.452Z",
                        "updatedAt": "2023-08-31T17:26:55.452Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "46712736-ff1c-4313-815e-5bf4bab70f7c",
                    "entityId": "88233d9f-b8f3-4c97-ab62-3cb04f7e4c52",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-08-31T17:26:55.452Z",
                    "updatedAt": "2023-08-31T17:26:55.452Z",
                    "deletedAt": null
                }
            },
            {
                "id": "89953faa-bea7-4406-a292-9993f1d9e034",
                "name": "testdevbridge1",
                "description": null,
                "type": "Retail Location",
                "externalIds": [],
                "phoneNumber": null,
                "website": null,
                "customerType": "Both",
                "storeHours": {
                    "fri": "Closed",
                    "mon": "Closed",
                    "sat": "Closed",
                    "sun": "Closed",
                    "thu": "Closed",
                    "tue": "Closed",
                    "wed": "Closed"
                },
                "tags": [],
                "displayName": "testdevbridge1",
                "status": "Active",
                "license": [],
                "address": {
                    "id": "e64db194-db41-40a8-a7bc-f9df6807f33d",
                    "entityId": "89953faa-bea7-4406-a292-9993f1d9e034",
                    "streetAddress1": null,
                    "streetAddress2": null,
                    "city": null,
                    "region": null,
                    "country": null,
                    "postalCode": null,
                    "createdAt": "2023-08-04T17:05:24.569Z",
                    "updatedAt": "2023-08-04T17:05:24.569Z",
                    "deletedAt": null
                }
            },
            {
                "id": "b0fe6dde-1e6a-4d2c-9980-9f555151ecf6",
                "name": "Test Entity UPSERTED",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "************",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [
                    {
                        "id": "42b4567e-d2fa-4eba-8bec-89facb010e10",
                        "entityId": "b0fe6dde-1e6a-4d2c-9980-9f555151ecf6",
                        "name": "TEST_LIC",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "09871234",
                        "startDate": "2023-12-21T00:00:00.000+00:00",
                        "expirationDate": "2083-12-21T00:00:00.000+00:00",
                        "createdAt": "2023-03-02T02:27:03.315Z",
                        "updatedAt": "2023-03-02T02:46:00.849Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "6a88365b-5463-4b55-a38f-b87b6a889d2e",
                    "entityId": "b0fe6dde-1e6a-4d2c-9980-9f555151ecf6",
                    "streetAddress1": "1530 Henry St",
                    "streetAddress2": null,
                    "city": "Berkeley",
                    "region": "CAL",
                    "country": "USA",
                    "postalCode": "94709",
                    "createdAt": "2023-03-02T02:27:03.315Z",
                    "updatedAt": "2023-03-02T02:27:03.315Z",
                    "deletedAt": null
                }
            },
            {
                "id": "a8db659f-ae86-4519-be39-8d9a10b79bfd",
                "name": "testfluresh1",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": null,
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [],
                "address": null
            },
            {
                "id": "52aaf9bf-da59-4505-88c6-d8e0fa385e8e",
                "name": "testfoh2",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": null,
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Onboarding",
                "license": [],
                "address": null
            },
            {
                "id": "87aa2033-ae3f-4157-a46f-e7033673d543",
                "name": "testgopika",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Onboarding",
                "license": [
                    {
                        "id": "b06b2ebe-5e26-4590-9f35-ad4fce3812fe",
                        "entityId": "87aa2033-ae3f-4157-a46f-e7033673d543",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-6725376745203529",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-08-31T17:13:11.783Z",
                        "updatedAt": "2023-08-31T17:13:11.783Z",
                        "deletedAt": null
                    },
                    {
                        "id": "451ce679-36e2-47ab-a430-2ca08da2aef9",
                        "entityId": "87aa2033-ae3f-4157-a46f-e7033673d543",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-98374534579",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-08-31T17:13:11.783Z",
                        "updatedAt": "2023-08-31T17:13:11.783Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "a5fe78cc-ac32-4250-881d-0bf1b090bcb5",
                    "entityId": "87aa2033-ae3f-4157-a46f-e7033673d543",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-08-31T17:13:11.783Z",
                    "updatedAt": "2023-08-31T17:13:11.783Z",
                    "deletedAt": null
                }
            },
            {
                "id": "6e01dfd5-747d-49e9-b60e-2a97e530f8e8",
                "name": "testgopikaauto",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "3253534543",
                "website": null,
                "customerType": "Both",
                "storeHours": {
                    "fri": "Closed",
                    "mon": "Closed",
                    "sat": "Closed",
                    "sun": "Closed",
                    "thu": "Closed",
                    "tue": "Closed",
                    "wed": "Closed"
                },
                "tags": [],
                "displayName": "testgopikaauto",
                "status": "Onboarding",
                "license": [
                    {
                        "id": "b25de42f-6883-486e-a23b-f4252a50e1d0",
                        "entityId": "6e01dfd5-747d-49e9-b60e-2a97e530f8e8",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-98374593752345",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-08-31T17:22:28.752Z",
                        "updatedAt": "2023-09-01T06:15:17.268Z",
                        "deletedAt": null
                    },
                    {
                        "id": "f6119cd2-095c-4f68-bc03-a8e4300baf7d",
                        "entityId": "6e01dfd5-747d-49e9-b60e-2a97e530f8e8",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-7668340562335",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-08-31T17:22:28.752Z",
                        "updatedAt": "2023-09-01T06:15:17.268Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "894e7879-313b-4b6f-80ff-f398865ec1b7",
                    "entityId": "6e01dfd5-747d-49e9-b60e-2a97e530f8e8",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "California",
                    "country": "COUNTRY",
                    "postalCode": "465466",
                    "createdAt": "2023-08-31T17:22:28.752Z",
                    "updatedAt": "2023-09-01T06:15:17.268Z",
                    "deletedAt": null
                }
            },
            {
                "id": "1b8af7e6-a816-42a9-8793-df5011cf98fd",
                "name": "testkyler",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [
                    {
                        "id": "b3e22091-2dbb-4a77-8e10-557070547f4d",
                        "entityId": "1b8af7e6-a816-42a9-8793-df5011cf98fd",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-982374239",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:32:21.840Z",
                        "updatedAt": "2023-01-23T22:32:21.840Z",
                        "deletedAt": null
                    },
                    {
                        "id": "7b542552-b31e-41fa-857b-e64c27a54e25",
                        "entityId": "1b8af7e6-a816-42a9-8793-df5011cf98fd",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-239847898",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:32:21.840Z",
                        "updatedAt": "2023-01-23T22:32:21.840Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "d6861f15-f6bd-48e4-8a38-bfd52f6b793c",
                    "entityId": "1b8af7e6-a816-42a9-8793-df5011cf98fd",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-01-23T22:32:21.840Z",
                    "updatedAt": "2023-01-23T22:32:21.840Z",
                    "deletedAt": null
                }
            },
            {
                "id": "44885417-5276-44a5-8ee1-6efbcf25e664",
                "name": "testkylerauto",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [
                    {
                        "id": "58a0ec9d-33aa-4bbe-8850-3ac85c732e8f",
                        "entityId": "44885417-5276-44a5-8ee1-6efbcf25e664",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-**********",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:34:19.962Z",
                        "updatedAt": "2023-01-23T22:34:19.962Z",
                        "deletedAt": null
                    },
                    {
                        "id": "8b808987-f977-4ed0-b776-de9a879e01e0",
                        "entityId": "44885417-5276-44a5-8ee1-6efbcf25e664",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-938475934",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:34:19.962Z",
                        "updatedAt": "2023-01-23T22:34:19.962Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "2d2cec14-0049-4075-9223-7c5d37d8244b",
                    "entityId": "44885417-5276-44a5-8ee1-6efbcf25e664",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-01-23T22:34:19.962Z",
                    "updatedAt": "2023-01-23T22:34:19.962Z",
                    "deletedAt": null
                }
            },
            {
                "id": "0f265107-2aba-4b10-a095-5d7e59f91a01",
                "name": "testos1",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "4353453453",
                "website": null,
                "customerType": "Both",
                "storeHours": {
                    "fri": "Closed",
                    "mon": "Closed",
                    "sat": "Closed",
                    "sun": "Closed",
                    "thu": "Closed",
                    "tue": "Closed",
                    "wed": "Closed"
                },
                "tags": [],
                "displayName": "testos1",
                "status": "Active",
                "license": [
                    {
                        "id": "919312f2-3f04-400a-9f28-914f39e10e8d",
                        "entityId": "0f265107-2aba-4b10-a095-5d7e59f91a01",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-83749537495834",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-05-05T14:42:20.270Z",
                        "updatedAt": "2023-09-01T06:13:47.827Z",
                        "deletedAt": null
                    },
                    {
                        "id": "03b6f614-06fd-4123-a7df-3ff675ba901a",
                        "entityId": "0f265107-2aba-4b10-a095-5d7e59f91a01",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-763849628395",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-05-05T14:42:20.270Z",
                        "updatedAt": "2023-09-01T06:13:47.827Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "31010aa0-8ad0-43b7-a8f0-719edb82c975",
                    "entityId": "0f265107-2aba-4b10-a095-5d7e59f91a01",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "45345",
                    "createdAt": "2023-05-05T14:42:20.270Z",
                    "updatedAt": "2023-09-01T06:13:47.827Z",
                    "deletedAt": null
                }
            },
            {
                "id": "06459a72-23cd-4e9b-9f20-db78bc3fb05e",
                "name": "testpurplelotus",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": null,
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Onboarding",
                "license": [],
                "address": null
            },
            {
                "id": "8dbbb521-a479-4df5-8348-c07a276fc9ab",
                "name": "testrick",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [
                    {
                        "id": "c8360916-91c9-4c20-a798-8856a2ccd725",
                        "entityId": "8dbbb521-a479-4df5-8348-c07a276fc9ab",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-782653645",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:35:49.757Z",
                        "updatedAt": "2023-01-23T22:35:49.757Z",
                        "deletedAt": null
                    },
                    {
                        "id": "c2b10cf1-b137-4e85-9225-89c8d1f979f6",
                        "entityId": "8dbbb521-a479-4df5-8348-c07a276fc9ab",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-**********",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:35:49.757Z",
                        "updatedAt": "2023-01-23T22:35:49.757Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "fe75b8ec-d06d-4791-8e25-8dbfa6d8f9f8",
                    "entityId": "8dbbb521-a479-4df5-8348-c07a276fc9ab",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-01-23T22:35:49.757Z",
                    "updatedAt": "2023-01-23T22:35:49.757Z",
                    "deletedAt": null
                }
            },
            {
                "id": "254763af-d0d3-4b1d-ad10-1c1129589837",
                "name": "testrickauto",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [
                    {
                        "id": "0f605ba1-c61d-460e-a664-8af70bfa4438",
                        "entityId": "254763af-d0d3-4b1d-ad10-1c1129589837",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-72638948503",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:37:19.020Z",
                        "updatedAt": "2023-01-23T22:37:19.020Z",
                        "deletedAt": null
                    },
                    {
                        "id": "c6f5bd32-d949-4488-ae5c-a5d3da6849df",
                        "entityId": "254763af-d0d3-4b1d-ad10-1c1129589837",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-938475903845",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:37:19.020Z",
                        "updatedAt": "2023-01-23T22:37:19.020Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "2b14da0f-60d0-4d83-b828-fd70142cc199",
                    "entityId": "254763af-d0d3-4b1d-ad10-1c1129589837",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-01-23T22:37:19.020Z",
                    "updatedAt": "2023-01-23T22:37:19.020Z",
                    "deletedAt": null
                }
            },
            {
                "id": "061138d4-54f8-4a3c-898c-c439897ce7f2",
                "name": "testui",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [
                    {
                        "id": "7c0f8913-019b-45a9-9b1a-e7f64dc93dbe",
                        "entityId": "061138d4-54f8-4a3c-898c-c439897ce7f2",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-876567865678",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:46:19.561Z",
                        "updatedAt": "2023-01-23T22:46:19.561Z",
                        "deletedAt": null
                    },
                    {
                        "id": "a7c2169b-b495-446d-8863-a305f7acc03d",
                        "entityId": "061138d4-54f8-4a3c-898c-c439897ce7f2",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-87654567898",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:46:19.561Z",
                        "updatedAt": "2023-01-23T22:46:19.561Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "d7b4b2c0-3e83-4137-a9d1-36bd5cec465f",
                    "entityId": "061138d4-54f8-4a3c-898c-c439897ce7f2",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-01-23T22:46:19.561Z",
                    "updatedAt": "2023-01-23T22:46:19.561Z",
                    "deletedAt": null
                }
            },
            {
                "id": "38932789-ac83-4072-843a-59efd499ce89",
                "name": "testui1",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [
                    {
                        "id": "1ffae17c-e12d-4f79-aaeb-8402655f4309",
                        "entityId": "38932789-ac83-4072-843a-59efd499ce89",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-62732405235",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:46:20.555Z",
                        "updatedAt": "2023-01-23T22:46:20.555Z",
                        "deletedAt": null
                    },
                    {
                        "id": "31120acd-832b-4dbf-bef0-c447ebfc0eaf",
                        "entityId": "38932789-ac83-4072-843a-59efd499ce89",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-37846583457",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-01-23T22:46:20.555Z",
                        "updatedAt": "2023-01-23T22:46:20.555Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "cb556e2a-31f2-4f6d-9510-7ffccf114124",
                    "entityId": "38932789-ac83-4072-843a-59efd499ce89",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-01-23T22:46:20.555Z",
                    "updatedAt": "2023-01-23T22:46:20.555Z",
                    "deletedAt": null
                }
            },
            {
                "id": "4ddddd43-f54b-43bd-9469-a7d261ca651a",
                "name": "testui2",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": null,
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Onboarding",
                "license": [],
                "address": null
            },
            {
                "id": "383676a6-e208-48e9-a5db-242f954f403f",
                "name": "testui3",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": null,
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Onboarding",
                "license": [],
                "address": null
            },
            {
                "id": "058382bd-f53c-448e-8a87-7472a4dd67f1",
                "name": "testuilocal",
                "description": null,
                "type": "Retail Location",
                "externalIds": null,
                "phoneNumber": "PHONE_NUMBER",
                "website": null,
                "customerType": "Both",
                "storeHours": null,
                "tags": null,
                "displayName": null,
                "status": "Active",
                "license": [
                    {
                        "id": "03d6e6e7-9886-486a-b8aa-5a0e31a837f4",
                        "entityId": "058382bd-f53c-448e-8a87-7472a4dd67f1",
                        "name": "Med License",
                        "description": null,
                        "type": "Medical",
                        "licenseNumber": "LIC-7834938593592",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-02-15T20:51:49.699Z",
                        "updatedAt": "2023-02-15T20:51:49.699Z",
                        "deletedAt": null
                    },
                    {
                        "id": "2658ca15-3c6e-46a5-8157-1727fbeaffdd",
                        "entityId": "058382bd-f53c-448e-8a87-7472a4dd67f1",
                        "name": "Adult License",
                        "description": null,
                        "type": "Recreational",
                        "licenseNumber": "LIC-8734653789594",
                        "startDate": "2023-01-01T00:00:00.000+00:00",
                        "expirationDate": "2030-01-01T00:00:00.000+00:00",
                        "createdAt": "2023-02-15T20:51:49.699Z",
                        "updatedAt": "2023-02-15T20:51:49.699Z",
                        "deletedAt": null
                    }
                ],
                "address": {
                    "id": "c32a6655-cb93-43a9-ac0b-610920985151",
                    "entityId": "058382bd-f53c-448e-8a87-7472a4dd67f1",
                    "streetAddress1": "STREET_ADDRESS",
                    "streetAddress2": null,
                    "city": "CITY",
                    "region": "REGION",
                    "country": "COUNTRY",
                    "postalCode": "POSTAL_CODE",
                    "createdAt": "2023-02-15T20:51:49.699Z",
                    "updatedAt": "2023-02-15T20:51:49.699Z",
                    "deletedAt": null
                }
            }
        ]

        `)
    );

    expect(Object.keys(stores).length).toBe(27);
  });
});
