import { ManualDiscountMethods, AutomatedDiscountMethods, LicenseTypes } from "../constants/discounts";
import {
  BogoConditions,
  BundleConditions,
  CustomEndType,
  CustomerEvent,
  CustomerGroup,
  CustomRepeatEvery,
  DaysOfWeek,
  FulfillmentTypesMap,
  RepeatType,
} from "./discounts";

export interface OrgDiscountResponse {
  id: string;
  title: string;
  displayTitle: string | null;
  description: string | null;
  amount: string;
  method: AutomatedDiscountMethods | ManualDiscountMethods;
  isActive: boolean;
  isAdjustment: boolean;
  isCart: boolean;
  isManual: boolean;
  isStackable: boolean | null;
  // Does not exist in the list response, defined or null in getDiscountById response
  conditions?: ConditionsResponse | null;
  manualConditions?: ManualConditionsResponse | null;
  schedule?: ScheduleResponse | null;
  requireReason: boolean;
  requirePin: boolean;
  requireCoupon: boolean;
  showEcommerce: boolean;
  showCustomerFacing: boolean;
  showSellTreez: boolean;
  imageUrl?: string;
  organizationId: string;
  storeCustomizations: StoreCustomizationResponse[];
  customerGroups: CustomerGroup[];
  collections: ProductCollectionDiscountResponse[];
  collectionsRequired: ProductCollectionDiscountResponse[];
  coupons: CouponResponse[];
  createdAt: string;
  updatedAt: string;
}

export interface DiscountLogResponse {
  username: string;
  createdAt: string;
  updates: string[];
  limitedData: boolean;
}

export interface StoreCustomizationResponse {
  id: string;
  orgDiscountId: string;
  entityId: string;
  entityName: string;
  amount: string | null;
  isActive: boolean;
  isCart: boolean | null;
  isAdjustment: boolean | null;
  requireCoupon: boolean | null;
  requirePin: boolean | null;
  requireReason: boolean | null;
  createdAt: string;
  updatedAt: string;
}
export interface CouponResponse {
  id: string;
  code: string;
  title: string;
  description: string | null;
  orgDiscountId: string;
  organizationId: string;
  startDate: string;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface ConditionsResponse {
  customerCapEnabled: boolean;
  customerCapValue: number | null;
  customerLimitEnabled: boolean;
  customerLimitValue: number | null;
  purchaseMinimumEnabled: boolean;
  purchaseMinimumValue: string | null;
  purchaseMinimumType: string | null;
  customerEventEnabled: boolean;
  customerEvents: CustomerEvent[] | null;
  customerGroupsEnabled: boolean;
  itemLimitEnabled: boolean;
  itemLimitValue: number | null;
  fulfillmentTypesEnabled: boolean;
  fulfillmentTypes: FulfillmentTypesMap | null;
  customerLicenseTypeEnabled: boolean;
  customerLicenseType: LicenseTypes | null;
  bogoConditions: BogoConditions | null;
  bundleConditions: BundleConditions | null;
  updatedAt: string;
  createdAt: string;
  id: string;
  packageAgeEnabled: boolean;
}

export interface ManualConditionsResponse {
  customerCapEnabled: boolean;
  customerCapValue: number | null;
  purchaseMinimumEnabled: boolean;
  purchaseMinimumValue: string | null;
  purchaseMinimumType: string | null;
  itemLimitEnabled: boolean;
  itemLimitValue: number | null;
  updatedAt: string;
  createdAt: string;
  id: string;
}

export interface ScheduleResponse {
  id: string;
  startDate: string;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  allDay: boolean;
  spansMultipleDays: boolean;
  repeatType: RepeatType;
  customRepeatEvery: CustomRepeatEvery | null;
  customRepeatIntervalCount: number | null;
  customRepeatDaysOfWeek: DaysOfWeek | null;
  customEndType: CustomEndType | null;
  customEndDate: string | null;
  customEndRepeatCount: number | null;
  updatedAt: string;
  createdAt: string;
}

export interface ProductCollectionDiscountResponse {
  id: string;
  orgDiscountId: string | null;
  productCollectionId: string;
  productCollectionName: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface ProductCollectionResponse {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  sync: boolean;
}

export interface GetAllProductCollectionsResponse {
  totalCount: number;
  data: ProductCollectionResponse[];
}

export interface OrgTags {
  id: string;
  tagGroupId: string;
  organizationId: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}
