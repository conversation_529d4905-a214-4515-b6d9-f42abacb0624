import { QueryClient, useMutation } from "@tanstack/react-query";
import ApiService from "../../services/api/apiService";
import { OrgDiscountReqBody } from "../../interfaces/requestModels";
import { upsertDiscountUrl } from "../../services/apiEndPoints";
import { OrgDiscountResponse } from "../../interfaces/responseModels";

const useCreateDiscountMutation = ({
  api,
  queryClient,
}: {
  api: ApiService;
  queryClient: QueryClient;
}) =>
  useMutation({
    mutationFn: async (newDiscount: OrgDiscountReqBody) => {
      const result = await api.post(upsertDiscountUrl, newDiscount);
      return result.data;
    },
    onSuccess: (data: OrgDiscountResponse) => {
      queryClient.setQueryData(
        ["discounts"],
        (oldDiscountsList: OrgDiscountResponse[]): OrgDiscountResponse[] =>
          oldDiscountsList ? [...oldDiscountsList, data] : oldDiscountsList
      );
    },
  });

export default useCreateDiscountMutation;
