import React from "react";
import { convertPxToRem } from "@treez-inc/component-library";
import { styled, Box } from "@mui/material";
import { DISCOUNT_FILTER_FIELDS } from "../../../../constants/discountTable";
import DropdownSelect, {
  DropdownSelectOptionProps,
} from "../../../../components/DropdownSelect";

interface ManualDiscountFiltersProps {
  setFilter: (field: string, value: string | boolean | string[]) => void;
  entitiesOptions: DropdownSelectOptionProps[];
}

const ManualDiscountFiltersContainer = styled(Box)(() => ({
  display: "flex",
}));

const ManualDiscountFilter = styled(Box)({
  marginRight: convertPxToRem(12),
});

const ManualDiscountFilters: React.FC<ManualDiscountFiltersProps> = ({
  setFilter,
  entitiesOptions,
}) => (
  <ManualDiscountFiltersContainer>
    <ManualDiscountFilter>
      <DropdownSelect
        testId="filterstores-dropdown"
        label="Stores"
        data={entitiesOptions}
        allOption
        onChange={(ids) => {
          setFilter(DISCOUNT_FILTER_FIELDS.STORES, ids);
        }}
      />
    </ManualDiscountFilter>
  </ManualDiscountFiltersContainer>
);

export default ManualDiscountFilters;
