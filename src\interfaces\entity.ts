export interface EntityResponse {
  id: string;
  name: string;
  description?: string;
  type?: string;
  organizationId?: string;
  externalIds?: { [key: string]: string }[];
  website?: string;
  customerType?: string;
  phoneNumber?: string;
  storeHours?: { [key: string]: string };
  address: {
    city?: string;
    region?: string;
    country?: string;
    streetAddress1?: string;
    streetAddress2?: string;
    postalCode?: string;
  };
  license?: { [key: string]: string }[];
}

export interface Store {
  city: string;
  country: string;
  region: string;
  name: string;
  id: string;
}
export interface Entity {
  [key: string]: Store[];
}
